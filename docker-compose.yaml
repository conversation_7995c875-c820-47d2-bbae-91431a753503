version: "3"
services:
  marketplace:
    build: .
    command: bash -c "python -m marketplace db up && uvicorn --reload --host 0.0.0.0 marketplace.asgi:app"
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: "*******************************************************"
      REDIS_HOST: "redis"
      PYTHONPATH: "/app/src"
    depends_on:
      - postgres
      - redis
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672" # AMQP port
      - "15672:15672" # Management port
  postgres:
    image: postgres:16
    environment:
      POSTGRES_USER: marketplace
      POSTGRES_PASSWORD: marketplace
      POSTGRES_DB: marketplace
    ports:
      - "5432:5432" # PostgreSQL port
    volumes:
      - postgres_data:/var/lib/postgresql/data
  redis:
    image: redis:7.4
    ports:
      - "6379:6379" # REDIS port
  jaeger:
    image: jaegertracing/all-in-one:1.62.0
    ports:
      - 16686:16686
      - 4317:4317

volumes:
  postgres_data:
