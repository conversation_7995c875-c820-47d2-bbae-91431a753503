# Marketplace

## Contributing

You can start all necessary components using Docker Compose with the following command:

```bash
docker-compose up -d
```

Alternatively, you can set up the local environment using Hatch. Ensure you have
`hatch` installed. If not, install it using pip:

```bash
pip install hatch
```

If you are using VSCode (or Cursor), you need to install Hatch globally to use things such as identifying imports and debugging.
Please refer to the documentation: https://hatch.pypa.io/latest/how-to/integrate/vscode/

Additionally, make sure you have `Docker Compose` installed. If you don't have it, follow the instructions on the [Docker Compose installation page](https://docs.docker.com/compose/install/).

Next, install the project dependencies:

```bash
hatch env create
```

Verify that all dependencies have been installed successfully:

```bash
hatch env show
```

Ensure that both `Redis` and `PostgreSQL` databases are running. You can start them using Docker with the following commands:

```bash
docker-compose up -d redis postgres
```

Apply the database migrations:

```bash
for f in ./migrations/*.sql; do
  cat $f | docker compose exec -T postgres psql -U marketplace
done
```

You can run the application using Hatch:

```bash
hatch run server
```

The application will be accessible at [http://localhost:8000/](http://localhost:8000/).

### Code Formatting

To format your code according to project standards:

```bash
hatch run format  # Format all files
hatch run format path/to/file.py  # Format specific file
```

This will run both `ruff check --fix` and `ruff format` on your code.

### Testing

This project utilizes `pytest` for testing. To execute the tests, run:

```bash
hatch run test
```

## CLI Commands

The project includes CLI commands for managing places:

### Upsert OTA Configurations

The `upsert_otas` command upsert the `OTAConfig`s in the system.

```bash
hatch run upsert_otas
```

If you want new OTA's, navigate to the `configs/` directory and modify the configuration files with the desired OTA settings.


### import-places

The `import-places` command allows you to bulk import place data from a CSV file.

```bash
PYTHONPATH=src python -m marketplace import-places --file ./configs/places/places_example.csv
```

If you want new Places's, navigate to the `configs/` directory and modify the configuration files with the desired places settings.


### Refresh Places

The `refresh_places` command updates the `OTAPlace`s in the system:

```bash
hatch run refresh_places
```
