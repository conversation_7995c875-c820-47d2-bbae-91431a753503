# Coding guidelines

* Prioritize code correctness and clarity. Speed and efficiency are secondary priorities unless otherwise specified.
* Do not write organizational or comments that summarize the code. Comments should only be written in order to explain "why" the code is written in some way in the case there is a reason that is tricky / non-obvious.
* Prefer implementing functionality in existing files unless it is a new logical component. Avoid creating many small files.

# Workflow

* Be sure to typecheck when you’re done making a series of code changes. Run `PYTHONPATH=src hatch run type` for it (it will run the pyright for you).
* Prefer running the whole test suite with `PYTHONPATH=src hatch run test`, and not the single test files. The test suite runs fast.
