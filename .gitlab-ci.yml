variables:
  CI_IMAGE: 557869972717.dkr.ecr.us-east-2.amazonaws.com/buser/marketplace:ci_${CI_COMMIT_SHA}

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH

stages:
  - build
  - test
  - deploy

build:
  tags: [k8s]
  stage: build
  image:
    name: moby/buildkit:v0.15.2-rootless
    entrypoint: ["sh", "-c"]
  interruptible: true
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - export PATH=$HOME/.bin:$PATH
    - mkdir -p $HOME/.bin $HOME/.docker
    - wget -O $HOME/.bin/docker-credential-ecr-login https://amazon-ecr-credential-helper-releases.s3.us-east-2.amazonaws.com/0.8.0/linux-amd64/docker-credential-ecr-login
    - chmod +x $HOME/.bin/docker-credential-ecr-login
    - |
      cat <<EOF > "$HOME/.docker/config.json"
      {
        "credHelpers": {
            "557869972717.dkr.ecr.us-east-2.amazonaws.com": "ecr-login"
        }
      }
      EOF
  script:
    - |
      buildctl-daemonless.sh build \
          --frontend=dockerfile.v0 \
          --local context="${CI_PROJECT_DIR}" \
          --local dockerfile="${CI_PROJECT_DIR}" \
          --output type=image,"\"name=${CI_IMAGE}\"",push=true \

test:
  stage: test
  tags: [k8s]
  needs: ["build"]
  interruptible: true
  image: $CI_IMAGE
  variables:
    GIT_STRATEGY: none
    PYTHONPATH: /app/src
  script:
    - cd /app
    - pytest --cov=. --cov-report=xml:coverage.xml .
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    paths:
      - /app/coverage.xml
    reports:
      coverage_report:
        coverage_format: cobertura
        path: /app/coverage.xml
    expire_in: 1 week

lint:
  stage: test
  tags: [k8s]
  needs: ["build"]
  interruptible: true
  image: $CI_IMAGE
  variables:
    GIT_STRATEGY: none
  script:
    - cd /app
    - ruff check .
    - ruff format --check .
    - pyright .

deploy production:
  tags: [k8s]
  stage: deploy
  image: buserbrasil/aws-kubectl:latest
  environment:
    name: production
    url: https://marketplace.buserdev.com.br
  script:
    - cat deployment.yaml | envsubst '$CI_IMAGE' | kubectl apply -f -
  rules:
    - if: $CI_COMMIT_REF_SLUG == "main"
      when: on_success
    - when: manual
