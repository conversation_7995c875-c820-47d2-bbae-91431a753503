import json
from dataclasses import asdict
from datetime import datetime, timezone
from http import HTTPStatus
from unittest.mock import AsyncMock, patch

import pytest
from starlette.responses import JSONResponse

from marketplace.models import (
    Benefit,
    BlockedSeat,
    Seat,
    SeatMap,
)
from marketplace.otas.exception import (
    IncompleteExtra,
    OTAConnectionError,
    OTASearcherTravelNotFound,
    OTATimeoutException,
)
from marketplace.views.seats import (
    travel_block_seat,
    travel_seating_map,
)


@patch(
    "marketplace.views.seats.get_searcher_from_ota_config_id", return_value=AsyncMock()
)
async def test_travel_seating_map(mock_search_ota, create_mock_request):
    seat_map = SeatMap(
        base_price=150.00,
        seats=[
            Seat(
                number="2",
                floor=1,
                row=1,
                column=1,
                available=False,
                category="Executivo",
                seat_type="executivo",
                price=150.00,
                extra={"NumeroPoltrona": 1},
            )
        ],
    )
    mock_search_ota.return_value.available_seats.return_value = seat_map

    body = {
        "travel": {
            "ota_config_id": 2,
            "price": 199.99,
            "seat_type": "executivo",
            "departure_at": "2025-01-09T06:00:00",
            "extra": {
                "CodigoOrigem": 1001,
                "CodigoDestino": 2001,
                "Andares": 1,
                "IdViagem": "123",
                "TipoServico": 1,
                "TipoVeiculo": 2,
                "TipoHorario": "Executivo",
            },
        }
    }
    request = create_mock_request(json_body=body)

    response = await travel_seating_map(request)
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    expected_item = asdict(seat_map.seats[0])
    assert response_json == {"seats": [expected_item], "base_price": 150.00}


@patch(
    "marketplace.views.seats.get_searcher_from_ota_config_id", return_value=AsyncMock()
)
async def test_travel_seating_map_travel_not_found(
    mock_search_ota, create_mock_request
):
    mock_search_ota.return_value.available_seats.side_effect = (
        OTASearcherTravelNotFound("travel not found")
    )

    body = {
        "travel": {
            "ota_config_id": 2,
            "price": 199.99,
            "seat_type": "executivo",
            "departure_at": "2025-01-09T06:00:00",
            "extra": {
                "CodigoOrigem": 1001,
                "CodigoDestino": 2001,
                "Andares": 1,
                "IdViagem": "123",
                "TipoServico": 1,
                "TipoVeiculo": 2,
                "TipoHorario": "Executivo",
            },
        }
    }
    request = create_mock_request(json_body=body)

    response = await travel_seating_map(request)
    assert response.status_code == HTTPStatus.NOT_FOUND
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    assert response_json == {"code": "TRAVEL_NOT_FOUND", "message": "travel not found"}


@patch(
    "marketplace.views.seats.get_searcher_from_ota_config_id", return_value=AsyncMock()
)
async def test_travel_seating_map_incomplete_extra(
    mock_search_ota, create_mock_request
):
    mock_search_ota.return_value.available_seats.side_effect = IncompleteExtra(
        "missing field"
    )

    body = {
        "travel": {
            "ota_config_id": 2,
            "price": 199.99,
            "seat_type": "executivo",
            "departure_at": "2025-01-09T06:00:00",
            "extra": {
                "CodigoOrigem": 1001,
                "CodigoDestino": 2001,
                "Andares": 1,
                "IdViagem": "123",
                "TipoServico": 1,
                "TipoVeiculo": 2,
                "TipoHorario": "Executivo",
            },
        }
    }
    request = create_mock_request(json_body=body)

    response = await travel_seating_map(request)
    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    assert response_json == {"message": "missing field"}


async def test_travel_seating_map_missing_field(create_mock_request):
    body = {
        "travel": {
            "ota_config_id": 2,
            "price": 199.99,
            "extra": {
                "CodigoOrigem": 1001,
                "CodigoDestino": 2001,
                "Andares": 1,
                "IdViagem": "123",
                "TipoServico": 1,
                "TipoVeiculo": 2,
                "TipoHorario": "Executivo",
            },
        }
    }
    request = create_mock_request(json_body=body)

    response = await travel_seating_map(request)
    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    assert response_json == {"message": "departure_at é obrigatório em travel"}


@patch(
    "marketplace.views.seats.get_searcher_from_ota_config_id", return_value=AsyncMock()
)
async def test_travel_block_seat(mock_search_ota, create_mock_request):
    blocked_seat = BlockedSeat(
        number="2",
        floor=1,
        row=1,
        column=1,
        available=False,
        category="Executivo",
        seat_type="executivo",
        price=150.00,
        extra={"NumeroPoltrona": 1},
        block_key="transaction_key_123",
        best_before=datetime.now(timezone.utc),
        benefit=Benefit.NORMAL,
    )
    mock_search_ota.return_value.block_seat.return_value = blocked_seat

    body = {
        "travel": {
            "ota_config_id": 2,
            "extra": {
                "Key": 1001,
            },
        },
        "seat": {
            "number": "2",
            "floor": 1,
            "row": 1,
            "column": 1,
            "available": False,
            "category": "Executivo",
            "seat_type": "executivo",
            "price": 150.00,
            "extra": {"NumeroPoltrona": 1},
        },
        "benefit": "normal",
    }
    request = create_mock_request(json_body=body)

    response = await travel_block_seat(request)
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    expected_item = asdict(blocked_seat)
    expected_item["best_before"] = expected_item["best_before"].isoformat()
    assert response_json == expected_item


async def test_travel_block_seat_invalid_field_type(create_mock_request):
    body = {
        "travel": {
            "ota_config_id": 2,
            "extra": {
                "Key": 1001,
            },
        },
        "seat": {
            "number": 2,
            "floor": 1,
            "row": 1,
            "column": 1,
            "available": False,
            "category": "Executivo",
            "seat_type": "executivo",
            "price": 150.00,
            "extra": {"NumeroPoltrona": 1},
        },
        "benefit": "normal",
    }
    request = create_mock_request(json_body=body)

    response = await travel_block_seat(request)
    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.BAD_REQUEST
    response_json = json.loads(bytes(response.body).decode())
    assert response_json == {
        "message": 'wrong value type for field "number" - should be "str" instead of value "2" of type "int"'
    }


async def test_travel_block_seat_missing_field(create_mock_request):
    body = {
        "travel": {
            "ota_config_id": 2,
            "extra": {
                "Key": 1001,
            },
        },
        "seat": {
            "floor": 1,
            "row": 1,
            "column": 1,
            "available": False,
            "category": "Executivo",
            "seat_type": "executivo",
            "price": 150.00,
            "extra": {"NumeroPoltrona": 1},
        },
        "benefit": "normal",
    }
    request = create_mock_request(json_body=body)

    response = await travel_block_seat(request)
    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.BAD_REQUEST
    response_json = json.loads(bytes(response.body).decode())
    assert response_json == {"message": 'missing value for field "number"'}


@patch(
    "marketplace.views.seats.get_searcher_from_ota_config_id", return_value=AsyncMock()
)
async def test_travel_block_seat_connection_error(mock_search_ota, create_mock_request):
    mock_search_ota.return_value.block_seat.side_effect = OTAConnectionError(
        "Connection error"
    )

    body = {
        "travel": {
            "ota_config_id": 2,
            "extra": {
                "Key": 1001,
            },
        },
        "seat": {
            "number": "2",
            "floor": 1,
            "row": 1,
            "column": 1,
            "available": False,
            "category": "Executivo",
            "seat_type": "executivo",
            "price": 150.00,
            "extra": {"NumeroPoltrona": 1},
        },
        "benefit": "normal",
    }
    request = create_mock_request(json_body=body)

    with pytest.raises(OTAConnectionError):
        response = await travel_block_seat(request)
        assert isinstance(response, JSONResponse)
        assert response.status_code == HTTPStatus.BAD_GATEWAY
        response_json = json.loads(bytes(response.body).decode())
        assert response_json == {
            "code": "CONNECTION_ERROR",
            "message": "Connection error",
        }


@patch(
    "marketplace.views.seats.get_searcher_from_ota_config_id", return_value=AsyncMock()
)
async def test_travel_block_seat_timeout_error(mock_search_ota, create_mock_request):
    mock_search_ota.return_value.block_seat.side_effect = OTATimeoutException(
        "Big Bud 747"
    )

    body = {
        "travel": {
            "ota_config_id": 2,
            "extra": {
                "Key": 1001,
            },
        },
        "seat": {
            "number": "2",
            "floor": 1,
            "row": 1,
            "column": 1,
            "available": False,
            "category": "Executivo",
            "seat_type": "executivo",
            "price": 150.00,
            "extra": {"NumeroPoltrona": 1},
        },
        "benefit": "normal",
    }
    request = create_mock_request(json_body=body)

    with pytest.raises(OTATimeoutException):
        response = await travel_block_seat(request)
        assert isinstance(response, JSONResponse)
        assert response.status_code == HTTPStatus.GATEWAY_TIMEOUT
        response_json = json.loads(bytes(response.body).decode())
        assert response_json == {"code": "TIMEOUT_ERROR", "message": "Big Bud 747"}
