import json
from unittest import mock
from unittest.mock import AsyncMock, patch

import pytest
from starlette.responses import JSONResponse

from marketplace.views.otas import batch_add_ota_place, validate_batch_add_ota_place


@patch(
    "marketplace.views.benefits.database.queries.insert_ota_config_places",
    new_callable=AsyncMock,
)
async def test_batch_add_ota_place_success(
    mock_insert, ota_config, create_mock_request
):
    input_data = [
        {
            "name": "SAO PAULO",
            "extra": {"id": 433},
            "place": "sao-paulo-sp.terminal-rodoviario-sao-paulo-sp",
        },
        {"name": "RIO DE JANEIRO", "extra": {"id": 123}},  # sem "place"
    ]

    request = create_mock_request(
        path_params={"config_id": ota_config.id}, json_body=input_data
    )

    response = await batch_add_ota_place(request)

    assert isinstance(response, JSONResponse)
    assert json.loads(bytes(response.body).decode()) == {"ok": True}

    expected_data = [
        {
            "ota_config_id": ota_config.id,
            "name": "SAO PAULO",
            "extra": {"id": 433},
            "status": "user",
            "place": "sao-paulo-sp.terminal-rodoviario-sao-paulo-sp",
        },
        {
            "ota_config_id": ota_config.id,
            "name": "RIO DE JANEIRO",
            "extra": {"id": 123},
            "status": "system",
            "place": None,
        },
    ]
    mock_insert.assert_called_once_with(mock.ANY, expected_data)


@patch(
    "marketplace.views.benefits.database.queries.insert_ota_config_places",
    new_callable=AsyncMock,
)
async def test_batch_add_ota_place_invalid_data(
    mock_insert, ota_config, create_mock_request
):
    invalid_data = [{"name": 123, "extra": {"id": 1}}]

    request = create_mock_request(
        path_params={"config_id": ota_config.id}, json_body=invalid_data
    )

    response = await batch_add_ota_place(request)

    # Espera a resposta de bad_request
    assert isinstance(response, JSONResponse)
    assert json.loads(bytes(response.body).decode()) == {"message": "Bad request."}

    # Verifica que não chamou o insert
    mock_insert.assert_not_called()


@pytest.mark.parametrize(
    "invalid_data",
    [
        # name is not str
        [{"name": 123, "extra": {"id": 1}}],
        # extra is not dict
        [{"name": "SAO PAULO", "extra": "wrong"}],
        # place is not str
        [{"name": "SAO PAULO", "extra": {"id": 1}, "place": 123}],
        # data is not list
        "not-a-list",
        # list item are not dict
        ["not-a-dict"],
    ],
)
def test_validate_batch_add_ota_place_failure(invalid_data):
    with pytest.raises(AssertionError):
        validate_batch_add_ota_place(1, invalid_data)


def test_validate_batch_add_ota_place_success():
    assert validate_batch_add_ota_place(
        1,
        [
            {
                "name": "RIO",
                "extra": {"id": 123},
                "place": "rio-de-janeiro-rj.terminal-rodoviario-rio-de-janeiro-rj",
            }
        ],
    ) == [
        {
            "ota_config_id": 1,
            "name": "RIO",
            "extra": {"id": 123},
            "status": "user",
            "place": "rio-de-janeiro-rj.terminal-rodoviario-rio-de-janeiro-rj",
        }
    ]
