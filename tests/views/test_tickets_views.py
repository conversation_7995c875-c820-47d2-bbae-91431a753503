import json
from datetime import UTC, date, datetime, timezone
from decimal import Decimal
from http import HTTPStatus
from unittest import mock
from unittest.mock import AsyncMock, patch

import pytest
from starlette.responses import JSONResponse

from marketplace import tickets
from marketplace.models import (
    Benefit,
    OTACompany,
    Pax,
    Place,
    YoungBenefit,
)
from marketplace.otas.exception import OTAIssuerInvalidResponse
from marketplace.tickets import (
    IssuedTicket,
    RefreshedTicket,
    Ticket,
    TicketAlreadyExists,
    TicketIssuer,
    TicketStatus,
    TicketStatusHistory,
)
from marketplace.views.tickets import (
    confirm_ticket,
    create_ticket,
    get_ticket,
    list_tickets,
    refresh_ticket,
)


class FakeTicketIssuer(TicketIssuer):
    async def cancel_ticket(self, ticket: Ticket) -> dict: ...

    async def issue_ticket(self, ticket: Ticket) -> IssuedTicket:
        return IssuedTicket(
            localizador="localiza-101",
            numero_pedido="order",
            numero_bilhete="éverdade esse bilete",
            numero_bpe=None,
            chave_bpe=None,
            serie_bpe=None,
            protocolo_autorizacao=None,
            data_autorizacao=None,
            nome_agencia=None,
            emissao_em_contigencia=None,
            bpe_qrcode=None,
            monitriip_code=None,
            numero_embarque_code=None,
            embarque_code=None,
            tipo_embarque=None,
            preco=None,
            preco_pedagio=None,
            preco_taxa_embarque=None,
            preco_seguro=None,
            preco_desconto=None,
            preco_total=None,
            outros_tributos=None,
            poltrona=None,
            plataforma=None,
            linha=None,
            prefixo=None,
            servico=None,
            cnpj=None,
            endereco_empresa=None,
            extra={"external_id": "EXT12345"},
        )

    async def refresh_ticket(self, ticket: Ticket) -> RefreshedTicket:
        assert ticket.issued_ticket.extra
        return await self.refresh_ticket_from_extra(ticket.issued_ticket.extra)

    async def refresh_ticket_from_extra(self, extra: dict) -> RefreshedTicket:
        return RefreshedTicket(
            linha="São Paulo → Rio de Janeiro",
            prefixo="RJ1234",
            embarque_code="SPT123",
            numero_embarque_code="001",
            preco_taxa_embarque=Decimal("5.50"),
            preco_seguro=Decimal("2.00"),
            preco_pedagio=Decimal("15.75"),
            outros_tributos="ISS, PIS, COFINS",
            chave_bpe="35190812345678000123550010000012341000012345",
            numero_bpe="1234",
            serie_bpe="1",
            bpe_qrcode="https://bpe.fazenda.gov.br/qrcode?chave=35190812345678000123550010000012341000012345",
        )


@pytest.fixture
def ticket(ota_config) -> Ticket:
    return Ticket(
        id=1,
        client_code="ABC123",
        tags={"promo": "yes", "season": "summer"},
        valid_until=datetime(2025, 12, 31, 23, 59, 59),
        pax_name="John Doe",
        pax_doc_type=Pax.DocType.RG,
        pax_doc="*********",
        pax_cpf="11122233344",
        pax_phone="+5511999999999",
        pax_birthdate=date(1990, 5, 20),
        pax_benefit=YoungBenefit(
            type=Benefit.YOUNG_50,
            number="Y12345",
            issue_date=date(2020, 1, 1),
            expiration_date=date(2030, 1, 1),
        ),
        code="TRIP001",
        departure_at=datetime(2025, 9, 10, 15, 0),
        arrival_at=datetime(2025, 9, 10, 20, 0),
        seat_type="executive",
        travel_extra={"wifi": True, "snacks": False},
        seat_number="12A",
        seat_floor=1,
        seat_row=12,
        seat_column=1,
        seat_extra=None,
        seat_block_key=None,
        price=Decimal("150.00"),
        created_at=datetime.now(UTC),
        ota_config=ota_config,
        company=OTACompany(
            name="Test Company",
            external_id=388,
            cnpj="*********01234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=4,
        ),
        origin=Place(slug="sao-paulo", name="São Paulo"),
        destination=Place(slug="rio-de-janeiro", name="Rio de Janeiro"),
        expired=False,
    )


@pytest.fixture
def ticket_dict():
    return {
        "client_code": "afb533f2-c8a1-40cd-b54a-2a666f9744a3",
        "price": 111.38,
        "travel": {
            "ota": "totalbus",
            "ota_config_id": 1,
            "code": "0000639019265141990910202500000000000102",
            "service_code": "6390",
            "company": {
                "ota_config_id": 1,
                "external_id": 42,
                "name": "NORDESTE TRANSPORTES LTDA",
                "cnpj": "76299270000107",
                "created_at": "2024-10-30T18:44:18.431811+00:00",
            },
            "itinerary": [],
            "origin": {
                "slug": "sao-jose-dos-campos-sp.terminal-rodoviario-de-sao-jose-dos-campos-sao-jose-dos-campos-sp",
                "name": "Terminal Rodoviário de São José dos Campos",
                "id": None,
                "status": "active",
                "created_at": "2024-11-07T18:31:45.025463+00:00",
                "updated_at": None,
            },
            "departure_at": "2025-10-10T09:00:00",
            "destination": {
                "slug": "rio-de-janeiro-rj.terminal-rodoviario-novo-rio-rio-de-janeiro-rj",
                "name": "Rodoviária do Rio",
                "id": None,
                "status": "active",
                "created_at": "2024-11-07T18:31:45.025463+00:00",
                "updated_at": None,
            },
            "arrival_at": "2025-10-10T15:05:00",
            "seat_type": {
                "ota_config_id": 1,
                "name": "SEMI-LEITO",
                "status": "available",
                "seat_type": "semi leito",
                "created_at": "2024-11-02T18:47:00.836703+00:00",
                "updated_at": None,
            },
            "available_seats": 39,
            "total_seats": 42,
            "last_synced": "2025-09-09T18:39:09.337531+00:00",
            "extra": {
                "origem": 1,
                "destino": 2,
                "dataCorrida": "2025-10-09",
                "servico": 6390,
                "classe": "SEMI-LEITO",
                "saida": "2025-10-10 09:00",
            },
            "single_ticket_connection": False,
            "stopovers": None,
            "price": 111.38,
            "group_class_code": "6390_2025-10-09",
            "group_code": None,
        },
        "pax": {
            "name": "test",
            "doc_type": "rg",
            "doc": "123",
            "cpf": "123",
            "phone": "12988069620",
            "birthdate": "1995-05-11",
            "client_code": "9faad8c6-99db-49eb-b967-a5d41bba59db",
            "benefit": {
                "type": "elderly_100",
                "monthly_income": "1000",
                "expiration_date": "2030-01-01",
            },
        },
        "blocked_seat": {
            "number": "41",
            "available": True,
            "category": "SEMI-LEITO",
            "seat_type": "semi leito",
            "floor": 1,
            "row": 12,
            "column": 1,
            "extra": {
                "tarifa": "89.48",
                "outros": "0.00",
                "pedagio": "15.00",
                "seguro": "0.00",
                "preco": "89.48",
                "tarifaComPricing": "89.48",
                "taxaEmbarque": "6.90",
                "seguroW2I": "0.00",
            },
            "price": 111.38,
            "benefits": None,
            "best_before": "2025-09-09T19:09:46.855538+00:00",
            "block_key": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxOTI2NS4xNDE5OS4yMDI1LTEwLTEwLjYzOTAuMTAwMDAwMDcwNTU3MDIuNDEuLTEuZmFsc2UuODlANDgiLCJleHAiOjE3ODg5ODA5ODYsInVzZXJJZCI6IkJVU0VSIiwicm9sZSI6Ii0ifQ.hmvn87vdrEFNfKv_LPfOuriIS3iMisc9qwHlSj3JlU4",
            "benefit": "normal",
        },
        "tags": {"site": "playground"},
    }


@patch("marketplace.views.tickets.tickets.list_tickets", new_callable=AsyncMock)
async def test_list_tickets(mock_list_tickets, create_mock_request, ticket):
    mock_list_tickets.return_value = [ticket]
    request = create_mock_request()

    response = await list_tickets(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.OK
    result_ticket = json.loads(bytes(response.body).decode())[0]
    assert result_ticket["client_code"] == ticket.client_code
    assert result_ticket["pax_name"] == ticket.pax_name
    assert result_ticket["origin"]["slug"] == ticket.origin.slug
    assert result_ticket["destination"]["slug"] == ticket.destination.slug
    assert result_ticket["pax_benefit"]["type"] == ticket.pax_benefit.type


@patch("marketplace.views.tickets.tickets.list_tickets", new_callable=AsyncMock)
async def test_list_tickets_using_client_code(
    mock_list_tickets, create_mock_request, ticket
):
    mock_list_tickets.return_value = [ticket]
    request = create_mock_request(query_params={"client_code": ticket.client_code})

    response = await list_tickets(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.OK
    result_ticket = json.loads(bytes(response.body).decode())[0]
    assert result_ticket["client_code"] == ticket.client_code
    assert result_ticket["pax_name"] == ticket.pax_name
    assert result_ticket["origin"]["slug"] == ticket.origin.slug
    assert result_ticket["destination"]["slug"] == ticket.destination.slug
    assert result_ticket["pax_benefit"]["type"] == ticket.pax_benefit.type


@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.insert_ticket", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer", return_value=AsyncMock())
async def test_create_ticket(
    mock_ticket_issuer,
    mock_insert_ticket,
    mock_get_ota_config_by_id,
    create_mock_request,
    ota_config,
    ticket_dict,
):
    mock_ticket_issuer.return_value = FakeTicketIssuer(ota_config)
    mock_insert_ticket.return_value = 1
    mock_get_ota_config_by_id.return_value = ota_config
    request = create_mock_request(json_body=ticket_dict)

    response = await create_ticket(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.CREATED
    assert json.loads(bytes(response.body).decode())["id"] == 1
    inserted_ticket = mock_insert_ticket.call_args[0][1]
    assert ticket_dict["client_code"] == inserted_ticket.client_code
    assert ticket_dict["pax"]["name"] == inserted_ticket.pax_name
    assert ticket_dict["travel"]["origin"]["slug"] == inserted_ticket.origin
    assert ticket_dict["travel"]["destination"]["slug"] == inserted_ticket.destination
    assert ticket_dict["pax"]["benefit"]["type"] == inserted_ticket.pax_benefit.type


@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.insert_ticket", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer", return_value=AsyncMock())
async def test_create_ticket_already_exists(
    mock_ticket_issuer,
    mock_insert_ticket,
    mock_get_ota_config_by_id,
    create_mock_request,
    ota_config,
    ticket_dict,
):
    mock_ticket_issuer.return_value = FakeTicketIssuer(ota_config)
    mock_get_ota_config_by_id.return_value = ota_config
    mock_insert_ticket.side_effect = TicketAlreadyExists(
        "Ticket ABC123 already exists."
    )

    request = create_mock_request(json_body=ticket_dict)
    response = await create_ticket(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.CONFLICT
    body = json.loads(bytes(response.body).decode())
    assert "already exists" in body["message"]
    mock_insert_ticket.assert_awaited_once()


@patch("marketplace.views.tickets.tickets.get_ticket_by_id", new_callable=AsyncMock)
async def test_get_ticket(
    mock_get_ticket_by_id,
    create_mock_request,
    ticket,
):
    mock_get_ticket_by_id.return_value = ticket
    request = create_mock_request(path_params={"ticket_id": 1})

    response = await get_ticket(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.OK
    result_ticket = json.loads(bytes(response.body).decode())["ticket"]
    assert result_ticket["id"] == 1
    assert result_ticket["client_code"] == ticket.client_code
    assert result_ticket["pax_name"] == ticket.pax_name


@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.get_ticket_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.insert_ticket_status", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer")
async def test_confirm_ticket(
    mock_ticket_issuer,
    mock_insert_ticket_status,
    mock_get_ticket_by_id,
    mock_get_ota_config_by_id,
    create_mock_request,
    ota_config,
    ticket,
):
    mock_get_ticket_by_id.return_value = ticket
    mock_get_ota_config_by_id.return_value = ota_config
    mock_ticket_issuer.return_value = FakeTicketIssuer(ota_config)

    request = create_mock_request(path_params={"ticket_id": 1})

    response = await confirm_ticket(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.OK
    assert json.loads(bytes(response.body).decode())["id"] == 1
    expected_calls = [
        mock.call(mock.ANY, 1, tickets.TicketStatus.PROCESSING),
        mock.call(
            mock.ANY,
            1,
            tickets.TicketStatus.ISSUED,
            result={
                "localizador": "localiza-101",
                "numero_pedido": "order",
                "numero_bilhete": "éverdade esse bilete",
                "numero_bpe": None,
                "chave_bpe": None,
                "serie_bpe": None,
                "protocolo_autorizacao": None,
                "data_autorizacao": None,
                "nome_agencia": None,
                "emissao_em_contigencia": None,
                "bpe_qrcode": None,
                "monitriip_code": None,
                "numero_embarque_code": None,
                "embarque_code": None,
                "tipo_embarque": None,
                "preco": None,
                "preco_pedagio": None,
                "preco_taxa_embarque": None,
                "preco_seguro": None,
                "preco_desconto": None,
                "preco_total": None,
                "outros_tributos": None,
                "poltrona": None,
                "plataforma": None,
                "linha": None,
                "prefixo": None,
                "servico": None,
                "cnpj": None,
                "endereco_empresa": None,
                "extra": {"external_id": "EXT12345"},
            },
        ),
    ]
    mock_insert_ticket_status.assert_has_calls(expected_calls)


@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.get_ticket_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.insert_ticket_status", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer")
async def test_confirm_ticket_error(
    mock_ticket_issuer,
    mock_insert_ticket_status,
    mock_get_ticket_by_id,
    mock_get_ota_config_by_id,
    create_mock_request,
    ota_config,
    ticket,
):
    mock_get_ticket_by_id.return_value = ticket
    mock_get_ota_config_by_id.return_value = ota_config
    fake_issuer = mock.AsyncMock()
    fake_issuer.issue_ticket.side_effect = RuntimeError("Issuing failed")
    mock_ticket_issuer.return_value = fake_issuer

    request = create_mock_request(path_params={"ticket_id": 1})

    with pytest.raises(RuntimeError, match="Issuing failed"):
        await confirm_ticket(request)

    expected_calls = [
        mock.call(mock.ANY, 1, tickets.TicketStatus.PROCESSING),
        mock.call(
            mock.ANY, 1, tickets.TicketStatus.FAILED, {"error": "Issuing failed"}
        ),
    ]
    mock_insert_ticket_status.assert_has_calls(expected_calls)


@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.get_ticket_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.tickets.insert_ticket_status", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer")
async def test_confirm_ticket_error_invalid_response(
    mock_ticket_issuer,
    mock_insert_ticket_status,
    mock_get_ticket_by_id,
    mock_get_ota_config_by_id,
    create_mock_request,
    ota_config,
    ticket,
):
    mock_get_ticket_by_id.return_value = ticket
    mock_get_ota_config_by_id.return_value = ota_config
    fake_issuer = mock.AsyncMock()
    fake_issuer.issue_ticket.side_effect = OTAIssuerInvalidResponse(
        response="qualquer coisa que tenha vindo"
    )
    mock_ticket_issuer.return_value = fake_issuer

    request = create_mock_request(path_params={"ticket_id": 1})

    response = await confirm_ticket(request)

    assert response.status_code == 500
    assert json.loads(bytes(response.body).decode()) == {
        "message": "Erro ao converter retorno da OTA."
    }

    expected_calls = [
        mock.call(mock.ANY, 1, tickets.TicketStatus.PROCESSING),
        mock.call(
            mock.ANY, 1, tickets.TicketStatus.FAILED, "qualquer coisa que tenha vindo"
        ),
    ]
    mock_insert_ticket_status.assert_has_calls(expected_calls)


@patch("marketplace.views.tickets.tickets.insert_ticket_status", new_callable=AsyncMock)
@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer")
@patch("marketplace.views.tickets.tickets.get_ticket_by_id", new_callable=AsyncMock)
async def test_refresh_ticket_from_ticket_id(
    mock_get_ticket_by_id,
    mock_ticket_issuer,
    mock_get_ota_config_by_id,
    mock_insert_ticket_status,
    create_mock_request,
    ota_config,
    ticket,
):
    mock_get_ota_config_by_id.return_value = ota_config
    mock_ticket_issuer.return_value = FakeTicketIssuer(ota_config)
    ticket.status_history = [
        TicketStatusHistory(
            status=TicketStatus.ISSUED,
            created_at=datetime.now(UTC),
            result={"extra": {"external_id": "EXT12345"}},
        )
    ]
    mock_get_ticket_by_id.return_value = ticket

    request = create_mock_request(path_params={"ticket_id": 1})

    response = await refresh_ticket(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.OK
    assert json.loads(bytes(response.body).decode()) == {
        "ticket": {
            "linha": "São Paulo → Rio de Janeiro",
            "prefixo": "RJ1234",
            "embarque_code": "SPT123",
            "numero_embarque_code": "001",
            "preco_taxa_embarque": "5.50",
            "preco_seguro": "2.00",
            "preco_pedagio": "15.75",
            "outros_tributos": "ISS, PIS, COFINS",
            "chave_bpe": "35190812345678000123550010000012341000012345",
            "numero_bpe": "1234",
            "serie_bpe": "1",
            "bpe_qrcode": "https://bpe.fazenda.gov.br/qrcode?chave=35190812345678000123550010000012341000012345",
        }
    }

    expected_calls = [
        mock.call(mock.ANY, 1, tickets.TicketStatus.REFRESHING),
        mock.call(
            mock.ANY,
            1,
            tickets.TicketStatus.REFRESHED,
            result={
                "linha": "São Paulo → Rio de Janeiro",
                "prefixo": "RJ1234",
                "embarque_code": "SPT123",
                "numero_embarque_code": "001",
                "preco_taxa_embarque": Decimal("5.50"),
                "preco_seguro": Decimal("2.00"),
                "preco_pedagio": Decimal("15.75"),
                "outros_tributos": "ISS, PIS, COFINS",
                "chave_bpe": "35190812345678000123550010000012341000012345",
                "numero_bpe": "1234",
                "serie_bpe": "1",
                "bpe_qrcode": "https://bpe.fazenda.gov.br/qrcode?chave=35190812345678000123550010000012341000012345",
            },
        ),
    ]
    mock_insert_ticket_status.assert_has_calls(expected_calls, any_order=True)


@patch("marketplace.views.tickets.get_ota_config_by_id", new_callable=AsyncMock)
@patch("marketplace.views.tickets.ticket_issuer")
async def test_refresh_ticket_from_extra(
    mock_ticket_issuer,
    mock_get_ota_config_by_id,
    create_mock_request,
    ota_config,
):
    mock_get_ota_config_by_id.return_value = ota_config
    mock_ticket_issuer.return_value = FakeTicketIssuer(ota_config)

    request = create_mock_request(
        path_params={"ticket_id": 1}, json_body={"ota_config_id": 123, "extra": {}}
    )

    response = await refresh_ticket(request)

    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTPStatus.OK
    assert json.loads(bytes(response.body).decode()) == {
        "ticket": {
            "linha": "São Paulo → Rio de Janeiro",
            "prefixo": "RJ1234",
            "embarque_code": "SPT123",
            "numero_embarque_code": "001",
            "preco_taxa_embarque": "5.50",
            "preco_seguro": "2.00",
            "preco_pedagio": "15.75",
            "outros_tributos": "ISS, PIS, COFINS",
            "chave_bpe": "35190812345678000123550010000012341000012345",
            "numero_bpe": "1234",
            "serie_bpe": "1",
            "bpe_qrcode": "https://bpe.fazenda.gov.br/qrcode?chave=35190812345678000123550010000012341000012345",
        }
    }
