import json
from datetime import datetime
from unittest.mock import AsyncMock, patch

from starlette.responses import JSONResponse

from marketplace.models import (
    Checkpoint,
)
from marketplace.views.travels import (
    travel_itinerary,
)


@patch(
    "marketplace.views.travels.get_searcher_from_ota_config_id",
    return_value=AsyncMock(),
)
async def test_travel_itinerary(mock_search_ota, create_mock_request):
    checkpoints = [
        Checkpoint(
            name="CURITIBA - PR -  TERMINAL RODOVIARIO - TE",
            departure_at=datetime(2025, 6, 17, 19, 0),
            distance_km=0,
        ),
        Checkpoint(
            name="PONTA GROSSA - PR - TERMINAL RODOVIARIO - TE",
            departure_at=datetime(2025, 6, 17, 21, 0),
            distance_km=112.96,
        ),
    ]
    mock_search_ota.return_value.travel_itinerary.return_value = checkpoints

    body = {
        "ota_config_id": 2,
        "price": 199.99,
        "extra": {
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": "123",
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
        },
    }
    request = create_mock_request(json_body=body)

    response = await travel_itinerary(request)
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    assert response_json == {
        "itinerary": [
            {
                "name": "CURITIBA - PR -  TERMINAL RODOVIARIO - TE",
                "departure_at": "2025-06-17T19:00:00",
                "distance_km": 0,
            },
            {
                "name": "PONTA GROSSA - PR - TERMINAL RODOVIARIO - TE",
                "departure_at": "2025-06-17T21:00:00",
                "distance_km": 112.96,
            },
        ]
    }
