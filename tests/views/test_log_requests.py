import logging
from contextlib import contextmanager
from datetime import datetime, timezone
from unittest import mock

import pytest
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette.testclient import TestClient

from marketplace import asgi
from marketplace.log import log_request, requestslogger


@pytest.fixture
def request_mock():
    mock_scope = {
        "type": "http",
        "asgi": {"version": "3.0", "spec_version": "2.3"},
        "http_version": "1.1",
        "method": "GET",
        "scheme": "http",
        "path": "/test",
        "raw_path": b"/test",
        "query_string": b"param=value",
        "headers": [
            (b"host", b"localhost:8000"),
            (b"user-agent", b"testclient"),
        ],
        "client": ("127.0.0.1", 12345),
        "server": ("localhost", 8000),
        "state": {},
    }

    async def mock_receive():
        return {
            "type": "http.request",
            "body": b'{"json": "payload"}',
            "more_body": False,
        }

    request = Request(scope=mock_scope, receive=mock_receive)
    return request


@pytest.fixture
def client_mock_factory():
    """
    Mocka app do starlettte, adicionando um router POST /test.
    Recebe uma funcao para mockar a resposta do endpoint.
    Como mocka o app completo, é possível testar os endpoints existentes da aplicação.
    """

    def _default_response(request: Request):
        return JSONResponse({"message": "ok"})

    @contextmanager
    def factory(response_func=None):
        asgi.app.add_route(
            "/test", response_func or _default_response, methods=["POST"]
        )
        try:
            yield TestClient(asgi.app, raise_server_exceptions=False)
        finally:
            asgi.app.router.routes.pop()

    return factory


def test_log_request_middleware(caplog, client_mock_factory):
    with caplog.at_level(logging.DEBUG, logger="requestslogger"):
        requestslogger.propagate = True
        with client_mock_factory() as client:
            response = client.post(
                "/travels/block-seat",
                json={"travel": {"ota_config_id": 1}},
                headers={"X-Correlation-ID": "12345"},
            )

        assert response.status_code == 400
        assert '{"message":"missing value for field \\"extra\\""}' in response.text
        requestslogger.propagate = False

    log = caplog.records[0]
    assert "POST 400 /travels/block-seat" in log.message
    assert log.levelno == logging.WARNING
    assert log.method == "POST"
    assert log.path == "/travels/block-seat"
    assert log.status == 400
    assert log.request_query == '&json={"travel": {"ota_config_id": 1}}'
    assert log.correlation_id == "12345"
    assert log.response == '{"message": "missing value for field \\"extra\\""}'


async def test_log_request_middleware_exception(caplog, client_mock_factory):
    with caplog.at_level(logging.DEBUG, logger="requestslogger"):
        requestslogger.propagate = True

        def _raise_exception(request: Request):
            raise Exception("test")

        with client_mock_factory(_raise_exception) as client:
            response = client.post(
                "/test",
                headers={"X-Correlation-ID": "12345"},
            )
        assert response.status_code == 500
        assert "Traceback (most recent call last):" in response.text
        requestslogger.propagate = False

    # o response retorna 500, mas o log está como 0
    # isso acontece pq o middleware de log roda antes do middleware padrão de tratamento de erros do Starlette (que fica acima de todos os outros)
    log = caplog.records[0]
    assert "POST 0 /test Exception: test" in log.message
    assert log.levelno == logging.ERROR
    assert log.method == "POST"
    assert log.path == "/test"
    assert log.status == 0
    assert log.request_query == ""
    assert log.correlation_id == "12345"


async def test_log_request(caplog, request_mock):
    with caplog.at_level(logging.DEBUG, logger="requestslogger"):
        requestslogger.propagate = True
        log_request(
            200,
            request_mock,
            b'{"request": "payload"}',
            b'{"response": "body"}',
            0.1,
            datetime.now(timezone.utc),
        )
        requestslogger.propagate = False

    log = caplog.records[0]
    assert "GET 200 /test" in log.message
    assert log.levelno == logging.INFO
    assert log.method == "GET"
    assert log.path == "/test"
    assert log.status_code == 200
    assert log.request_query == 'param=value&json={"request": "[redacted]"}'
    assert log.duration == 0.1
    assert log.time
    assert log.response == '{"response": "[redacted]"}'


async def test_log_request_status_code_500(caplog, request_mock):
    with caplog.at_level(logging.DEBUG, logger="requestslogger"):
        requestslogger.propagate = True
        log_request(
            500,
            request_mock,
            b'{"request": "payload"}',
            b'{"response": "body"}',
            0.1,
            datetime.now(timezone.utc),
        )
        requestslogger.propagate = False

    log = caplog.records[0]
    assert "GET 500 /test" in log.message
    assert log.levelno == logging.ERROR
    assert log.method == "GET"
    assert log.path == "/test"
    assert log.status_code == 500
    assert log.request_query == 'param=value&json={"request": "[redacted]"}'
    assert log.duration == 0.1
    assert log.time
    assert log.response == '{"response": "[redacted]"}'


@mock.patch("marketplace.log.REQUEST_FIELDS_WHITE_LIST", {"request"})
@mock.patch("marketplace.log.RESPONSE_FIELDS_WHITE_LIST", {"response"})
async def test_log_request_white_list(caplog, request_mock):
    with caplog.at_level(logging.DEBUG, logger="requestslogger"):
        requestslogger.propagate = True
        log_request(
            200,
            request_mock,
            b'{"request": "payload"}',
            b'{"response": "body"}',
            0.1,
            datetime.now(timezone.utc),
        )
        requestslogger.propagate = False

    log = caplog.records[0]
    assert "GET 200 /test" in log.message
    assert log.levelno == logging.INFO
    assert log.method == "GET"
    assert log.path == "/test"
    assert log.status_code == 200
    assert log.request_query == 'param=value&json={"request": "payload"}'
    assert log.duration == 0.1
    assert log.time
    assert log.response == '{"response": "body"}'
