import json
from unittest import mock
from unittest.mock import AsyncMock, patch

from starlette.responses import JSONResponse

from marketplace.models import (
    Benefit,
    OTABenefit,
    OTABenefitStatus,
)
from marketplace.views.benefits import (
    refresh_benefits_from_ota,
    save_ota_benefit,
)


@patch(
    "marketplace.views.benefits.get_ota_config_by_id",
    return_value=AsyncMock(),
)
@patch(
    "marketplace.views.benefits.get_searcher_from_ota_config",
    return_value=AsyncMock(),
)
@patch(
    "marketplace.views.benefits.database.queries.insert_ota_benefits",
    return_value=AsyncMock(),
)
async def test_refresh_benefits(
    mock_insert, mock_searcher, mock_get_ota_config, ota_config, create_mock_request
):
    mock_get_ota_config.return_value = ota_config
    request = create_mock_request(path_params={"config_id": ota_config.id})
    mock_searcher.return_value.list_benefits.return_value = {
        OTABenefit(
            ota_config_id=ota_config.id,
            description="CATEGORIA B",
            external_code="2",
            status=OTABenefitStatus.AVAILABLE,
            type=Benefit.NORMAL,
            created_at=None,
            updated_at=None,
        )
    }

    response = await refresh_benefits_from_ota(request)
    assert isinstance(response, JSONResponse)
    assert json.loads(bytes(response.body).decode()) == {"ok": True}
    mock_insert.assert_called_once_with(
        mock.ANY,  # db_conn
        [
            {
                "ota_config_id": ota_config.id,
                "description": "CATEGORIA B",
                "external_code": "2",
                "status": "available",
                "type": "normal",
                "created_at": None,
                "updated_at": None,
            }
        ],
    )


@patch(
    "marketplace.views.benefits.get_ota_config_by_id",
    return_value=AsyncMock(),
)
@patch(
    "marketplace.views.benefits.database.queries.update_ota_benefit",
    return_value=AsyncMock(),
)
async def test_save_ota_benefit(
    mock_update, mock_get_ota_config, ota_config, create_mock_request
):
    mock_get_ota_config.return_value = ota_config
    request = create_mock_request(
        path_params={"config_id": ota_config.id},
        json_body={
            "description": "CATEGORIA A",
            "external_code": "1",
            "status": "available",
            "type": "normal",
        },
    )
    mock_update.return_value = {
        "ota_config_id": ota_config.id,
        "description": "CATEGORIA A",
        "external_code": "1",
        "status": "available",
        "type": "normal",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }

    response = await save_ota_benefit(request)
    assert isinstance(response, JSONResponse)
    assert json.loads(bytes(response.body).decode()) == mock_update.return_value
    mock_update.assert_called_once_with(
        mock.ANY,  # db_conn
        ota_config_id=ota_config.id,
        external_code="1",
        status="available",
        type="normal",
    )
