from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock

import pytest
from pytest_asyncio import is_async_test
from starlette.requests import Request

from marketplace.models import (
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
)


def pytest_collection_modifyitems(items):
    pytest_asyncio_tests = (item for item in items if is_async_test(item))
    session_scope_marker = pytest.mark.asyncio(loop_scope="session")
    for async_test in pytest_asyncio_tests:
        async_test.add_marker(session_scope_marker, append=False)


@pytest.fixture
def create_mock_request():
    def func(query_params=None, json_body=None, path_params=None):
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = query_params
        mock_request.json = AsyncMock(return_value=json_body)
        mock_request.path_params = path_params or {}

        mock_db_conn = MagicMock()
        mock_transaction_cm = AsyncMock()
        mock_transaction_cm.__aenter__.return_value = None
        mock_transaction_cm.__aexit__.return_value = None
        mock_db_conn.transaction.return_value = mock_transaction_cm

        mock_db_pool = MagicMock()
        mock_acquire_cm = AsyncMock()
        mock_acquire_cm.__aenter__.return_value = mock_db_conn
        mock_acquire_cm.__aexit__.return_value = None
        mock_db_pool.acquire.return_value = mock_acquire_cm

        mock_request.state.db_pool = mock_db_pool

        return mock_request

    return func


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=4,
        name="OTAConfig",
        provider="ota",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=4,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=4,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
    )
