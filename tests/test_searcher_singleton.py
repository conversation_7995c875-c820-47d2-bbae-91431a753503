from copy import deepcopy
from datetime import datetime, timezone
from unittest import mock

import pytest

from marketplace import get_searcher_from_ota_config, ticket_issuer
from marketplace.models import (
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
)


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=2,
        name="Eulabs",
        provider="eulabs",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=2,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=2,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            )
        ],
        search_cache=OTASearchCacheConfig(
            ttl=86400, stale_after=900, ttl_few_seats=900
        ),
    )


def test_get_searcher_from_ota_config(ota_config):
    class FakeSearcher:
        def __init__(self, ota_config: OTAConfig, *args, **kwargs):
            self.ota_config = ota_config

    with mock.patch("marketplace._import_cls", return_value=FakeSearcher):
        first_searcher = get_searcher_from_ota_config(ota_config)
        assert get_searcher_from_ota_config(ota_config) is first_searcher
        ota_config_copy = deepcopy(ota_config)
        ota_config_copy.config["test"] = "test"
        assert get_searcher_from_ota_config(ota_config_copy) is not first_searcher


def test_ticket_issuer(ota_config):
    class FakeTicketIssuer:
        def __init__(self, ota_config: OTAConfig, *args, **kwargs):
            self.ota_config = ota_config

    with mock.patch("marketplace._import_cls", return_value=FakeTicketIssuer):
        first_ticket_issuer = ticket_issuer(ota_config)
        assert ticket_issuer(ota_config) is first_ticket_issuer
        ota_config_copy = deepcopy(ota_config)
        ota_config_copy.circuit_breaker.enabled = True
        assert ticket_issuer(ota_config_copy) is not first_ticket_issuer
