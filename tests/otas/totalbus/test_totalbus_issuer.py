from datetime import UTC, date, datetime, timezone
from decimal import Decimal
from unittest.mock import AsyncMock

import pytest

from marketplace.models import (
    Benefit,
    OTABenefit,
    OTABenefitStatus,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Pax,
    Place,
    YoungBenefit,
)
from marketplace.otas.totalbus.client import (
    TotalbusClient,
)
from marketplace.otas.totalbus.ticket_issuer import TotalbusTicketIssuer
from marketplace.tickets import IssuedTicket, Ticket


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=4,
        name="Totalbus",
        provider="totalbus",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        config={
            "base_url": "https://totalbus.test",
            "username": "test",
            "password": "test",
            "tenant_id": "test",
        },
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="*********01234",
                created_at=now,
                ota_config_id=4,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=4,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
        benefits=[
            OTABenefit(
                ota_config_id=4,
                external_code="4",
                description="JOVEM 100%",
                status=OTABenefitStatus.AVAILABLE,
                created_at=now,
                updated_at=now,
                type=Benefit.YOUNG_100,
            ),
            OTABenefit(
                ota_config_id=4,
                external_code="1",
                description="NORMAL",
                status=OTABenefitStatus.AVAILABLE,
                created_at=now,
                updated_at=now,
                type=Benefit.NORMAL,
            ),
        ],
    )


@pytest.fixture
def mock_client():
    return AsyncMock(spec=TotalbusClient)


@pytest.fixture
def issuer(ota_config, mock_client):
    issuer = TotalbusTicketIssuer(ota_config=ota_config)
    issuer._session = mock_client

    return issuer


@pytest.fixture
def ticket(ota_config) -> Ticket:
    return Ticket(
        id=1,
        client_code="ABC123",
        tags={"promo": "yes", "season": "summer"},
        valid_until=datetime(2025, 12, 31, 23, 59, 59),
        pax_name="John Doe",
        pax_doc_type=Pax.DocType.RG,
        pax_doc="*********",
        pax_cpf="11122233344",
        pax_phone="+5511999999999",
        pax_birthdate=date(1990, 5, 20),
        pax_benefit=YoungBenefit(
            type=Benefit.YOUNG_50,
            number="Y12345",
            issue_date=date(2020, 1, 1),
            expiration_date=date(2030, 1, 1),
        ),
        code="TRIP001",
        departure_at=datetime(2025, 9, 10, 15, 0),
        arrival_at=datetime(2025, 9, 10, 20, 0),
        seat_type="executive",
        travel_extra={"wifi": True, "snacks": False},
        seat_number="12A",
        seat_floor=1,
        seat_row=12,
        seat_column=1,
        seat_extra=None,
        seat_block_key="BLOCK123",
        price=Decimal("150.00"),
        created_at=datetime.now(UTC),
        ota_config=ota_config,
        company=OTACompany(
            name="Test Company",
            external_id=388,
            cnpj="*********01234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=4,
        ),
        origin=Place(slug="sao-paulo", name="São Paulo"),
        destination=Place(slug="rio-de-janeiro", name="Rio de Janeiro"),
        expired=False,
    )


async def test_issuer(issuer, mock_client, ticket):
    mock_client.confirmar_venda.return_value = {"external_id": "ABC123"}

    result = await issuer.issue_ticket(ticket)

    assert result == IssuedTicket(
        localizador=None,
        numero_pedido=None,
        numero_bilhete=None,
        numero_bpe=None,
        chave_bpe=None,
        serie_bpe=None,
        protocolo_autorizacao=None,
        data_autorizacao=None,
        nome_agencia=None,
        emissao_em_contigencia=None,
        bpe_qrcode=None,
        monitriip_code=None,
        numero_embarque_code=None,
        embarque_code=None,
        tipo_embarque=None,
        preco=None,
        preco_pedagio=None,
        preco_taxa_embarque=None,
        preco_seguro=None,
        preco_desconto=None,
        preco_total=None,
        outros_tributos=None,
        poltrona=None,
        plataforma=None,
        linha=None,
        prefixo=None,
        servico=None,
        cnpj=None,
        endereco_empresa=None,
        extra={"external_id": "ABC123"},
    )
    mock_client.confirmar_venda.assert_called_once_with(
        transacao=ticket.seat_block_key,
        nome_passageiro=ticket.pax_name,
        documento_passageiro=ticket.pax_cpf,
        tipo_documento_passageiro="CPF",
        telefone=ticket.pax_phone,
        data_nascimento=ticket.pax_birthdate.isoformat(),
    )
