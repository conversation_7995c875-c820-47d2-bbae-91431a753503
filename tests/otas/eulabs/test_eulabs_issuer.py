from dataclasses import asdict
from datetime import UTC, date, datetime, timezone
from decimal import Decimal
from unittest.mock import AsyncMock

import pytest
from dacite import from_dict

from marketplace.models import (
    Benefit,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Pax,
    Place,
    YoungBenefit,
)
from marketplace.otas.eulabs.client import (
    BPEResponse,
    CancelConditionResponse,
    CancelSaleResponse,
    EulabsClient,
    SaleInfoResponse,
)
from marketplace.otas.eulabs.ticket_issuer import EulabsTicketIssuer
from marketplace.tickets import (
    IssuedTicket,
    RefreshedTicket,
    Ticket,
    TicketStatus,
    TicketStatusHistory,
)
from tests.otas.eulabs.test_eulabs_client import BPE_RESPONSE, SALES_INFO_RESPONSE


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=2,
        name="Eula<PERSON>",
        provider="eulabs",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        config={
            "base_url": "https://eulabs.test",
            "api_id": "test",
            "api_key": "test",
        },
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="*********01234",
                created_at=now,
                ota_config_id=2,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=2,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
    )


@pytest.fixture
def mock_client():
    return AsyncMock(spec=EulabsClient)


@pytest.fixture
def issuer(ota_config, mock_client):
    issuer = EulabsTicketIssuer(ota_config=ota_config)
    issuer._session = mock_client

    return issuer


@pytest.fixture
def ticket(ota_config) -> Ticket:
    return Ticket(
        id=1,
        client_code="ABC123",
        tags={"promo": "yes", "season": "summer"},
        valid_until=datetime(2025, 12, 31, 23, 59, 59),
        pax_name="John Doe",
        pax_doc_type=Pax.DocType.RG,
        pax_doc="*********",
        pax_cpf="11122233344",
        pax_phone="+5511999999999",
        pax_birthdate=date(1990, 5, 20),
        pax_benefit=YoungBenefit(
            type=Benefit.YOUNG_50,
            number="Y12345",
            issue_date=date(2020, 1, 1),
            expiration_date=date(2030, 1, 1),
        ),
        code="TRIP001",
        departure_at=datetime(2025, 9, 10, 15, 0),
        arrival_at=datetime(2025, 9, 10, 20, 0),
        seat_type="executive",
        travel_extra={"wifi": True, "snacks": False},
        seat_number="12A",
        seat_floor=1,
        seat_row=12,
        seat_column=1,
        seat_extra=None,
        seat_block_key="BLOCK123",
        price=Decimal("150.00"),
        created_at=datetime.now(UTC),
        ota_config=ota_config,
        company=OTACompany(
            name="Test Company",
            external_id=388,
            cnpj="*********01234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=4,
        ),
        origin=Place(slug="sao-paulo", name="São Paulo"),
        destination=Place(slug="rio-de-janeiro", name="Rio de Janeiro"),
        expired=False,
    )


async def test_refresh_ticket(issuer, mock_client):
    mock_client.get_sales_info.return_value = from_dict(
        SaleInfoResponse, SALES_INFO_RESPONSE
    )
    mock_client.get_bpe_info.return_value = from_dict(BPEResponse, BPE_RESPONSE)

    result = await issuer.refresh_ticket_from_extra(
        {"sale_id": 123, "item": {"key": "ITEM123"}}
    )

    assert result == RefreshedTicket(
        chave_bpe="14250934473546000343630000000722521338821081",
        numero_bpe="0000072252",
        serie_bpe="000",
        bpe_qrcode="https://dfe-portal.svrs.rs.gov.br/bpe/qrCode?chBPe=14250934473546000343630000000722521338821081&tpAmb=1",
        linha="TUBARÃO x APARECIDA",
        prefixo="SCSP0018028",
        outros_tributos="ICMS 36.81 (12%) OUTROS TRIB: 78.07 (25.45%)",
        embarque_code="eucaturmobile/boarding?type=manual&identification=*********&code=0241&line=16128&departure_travel=2025-10-26 07:35:00&departure=2025-10-25 17:00:00&direction=Volta",
        numero_embarque_code="",
        preco_taxa_embarque=Decimal("8.99"),
        preco_seguro=Decimal(0),
        preco_pedagio=Decimal(0),
    )


async def test_cancel_ticket(issuer, mock_client, ticket):
    mock_client.get_cancel_conditions.return_value = CancelConditionResponse(
        fine_amount=0,
        refund_amount=520.99,
        status="authorized",
        type=["Cancel"],
        key="eyJhbGciOiJIUzM4NCIsInR5cCI6IkpXVCJ9.eyJhbGxvd19yZWZ1bmQiOmZhbHNlLCJhbW91bnQiOjUyMC45OSwiYnBlX3NhbGVfaXRlbV9icGVfaWQiOjAsImNyZWF0ZWRfYXQiOiIyMDI1LTA5LTA4IDExOjUzOjAyIiwiZXhwaXJhX2F0IjoiMjAyNS0wOS0wOCAxNDowNDowMiIsImZpZGVsaXR5X2Rpc2NvdW50IjowLCJmaW5lX2Ftb3VudCI6MCwiZmluZV9wZXJjZW50IjowLCJoYXNfYnBlIjpmYWxzZSwiaGFzX2ZpZGVsaXR5IjpmYWxzZSwia2V5IjoiVklCLTMzNDk0MjEyIiwicmVmdW5kX2Ftb3VudCI6NTIwLjk5LCJzdGF0dXMiOiJhdXRob3JpemVkIiwidHlwZSI6IkNhbmNlbCIsInZhbHVlX3BvaW50cyI6MH0._y7hA8tZS2URXU_dhwynPBPTsZ7nkx_DZa3K2D6zf7fFMvK4s3p5Jwlvjv2UlnIL",
        allow_refund=False,
        amount=520.99,
        availability_digital_wallet=False,
    )
    mock_client.post_cancel_sale.return_value = CancelSaleResponse(
        type_this_refund="onSolicitationPortais"
    )

    ticket.status_history = [
        TicketStatusHistory(
            status=TicketStatus.ISSUED,
            created_at=datetime.now(UTC),
            result=asdict(
                IssuedTicket(
                    localizador=None,
                    numero_pedido=None,
                    numero_bilhete=None,
                    numero_bpe=None,
                    chave_bpe=None,
                    serie_bpe=None,
                    protocolo_autorizacao=None,
                    data_autorizacao=None,
                    nome_agencia=None,
                    emissao_em_contigencia=None,
                    bpe_qrcode=None,
                    monitriip_code=None,
                    numero_embarque_code=None,
                    embarque_code=None,
                    tipo_embarque=None,
                    preco=None,
                    preco_pedagio=None,
                    preco_taxa_embarque=None,
                    preco_seguro=None,
                    preco_desconto=None,
                    preco_total=None,
                    outros_tributos=None,
                    poltrona=None,
                    plataforma=None,
                    linha=None,
                    prefixo=None,
                    servico=None,
                    cnpj=None,
                    endereco_empresa=None,
                    extra={"items": [{"key": "VIB-0000001"}]},
                )
            ),
        )
    ]

    response = await issuer.cancel_ticket(ticket)
    # dict combando o retorno dos 2 endpoints
    assert response == {
        "type_this_refund": "onSolicitationPortais",
        "fine_amount": 0,
        "refund_amount": 520.99,
        "status": "authorized",
        "type": ["Cancel"],
        "key": "eyJhbGciOiJIUzM4NCIsInR5cCI6IkpXVCJ9.eyJhbGxvd19yZWZ1bmQiOmZhbHNlLCJhbW91bnQiOjUyMC45OSwiYnBlX3NhbGVfaXRlbV9icGVfaWQiOjAsImNyZWF0ZWRfYXQiOiIyMDI1LTA5LTA4IDExOjUzOjAyIiwiZXhwaXJhX2F0IjoiMjAyNS0wOS0wOCAxNDowNDowMiIsImZpZGVsaXR5X2Rpc2NvdW50IjowLCJmaW5lX2Ftb3VudCI6MCwiZmluZV9wZXJjZW50IjowLCJoYXNfYnBlIjpmYWxzZSwiaGFzX2ZpZGVsaXR5IjpmYWxzZSwia2V5IjoiVklCLTMzNDk0MjEyIiwicmVmdW5kX2Ftb3VudCI6NTIwLjk5LCJzdGF0dXMiOiJhdXRob3JpemVkIiwidHlwZSI6IkNhbmNlbCIsInZhbHVlX3BvaW50cyI6MH0._y7hA8tZS2URXU_dhwynPBPTsZ7nkx_DZa3K2D6zf7fFMvK4s3p5Jwlvjv2UlnIL",
        "allow_refund": False,
        "amount": 520.99,
        "availability_digital_wallet": False,
    }


async def test_cancel_ticket_invalid_issued_ticket(issuer, ticket):
    ticket.status_history = [
        TicketStatusHistory(
            status=TicketStatus.ISSUED,
            created_at=datetime.now(UTC),
            result={"invalid": "result"},
        )
    ]
    with pytest.raises(ValueError, match="ticket issue result is invalid"):
        await issuer.cancel_ticket(ticket)


async def test_cancel_ticket_no_issue_history(issuer, ticket):
    with pytest.raises(ValueError, match="Ticket has no issue history"):
        await issuer.cancel_ticket(ticket)
