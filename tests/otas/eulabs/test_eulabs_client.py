from datetime import date

import httpx
import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.eulabs.client import (
    BlockSeatRequest,
    BPEResponse,
    CancelConditionResponse,
    CancelSaleResponse,
    ClassInfo,
    Company,
    EulabsClient,
    FreeSeatBenefit,
    Item,
    SaleBenefitYoungRequest,
    SaleBoardingLandingResponse,
    SaleInfoResponse,
    SaleItemRequest,
    SaleItemResponse,
    SaleItemRoadRequest,
    SaleResponse,
    SaleStatusResponse,
    SaleUtilizerRequest,
    SaleUtilizerResponse,
    Seat,
    SeatingMap,
    Sectional,
    Travel,
    TravelDetail,
    TravelLeg,
)
from marketplace.otas.exception import OTAIssuerInvalidResponse, TravelUnavailable

SALES_INFO_RESPONSE = {
    "id": 98753107,
    "sectional_id": 6588,
    "sectional_cod": "9193",
    "sectional_name": "BUSER BRASIL TECNOLOGIA LTDA",
    "person_id": 0,
    "person_client_id": 0,
    "person_salesman_id": 257251,
    "user_id": 3829,
    "operation_date": "2025-10-10",
    "type": "Venda",
    "system": "portais",
    "confirmed": True,
    "sectional_request_id": 0,
    "person_request_id": 0,
    "created_at": "2025-10-10 14:50:57",
    "amount": 315.73,
    "items": [
        {
            "key": "VIB-34051965",
            "sale_id": 98753107,
            "localizer": "00053-VOUCHER-D31A4-0919391732",
            "transaction": "",
            "created_at": "2025-10-10 14:50:57",
            "bpe": {
                "id": 0,
                "contingency": "",
                "access_key": "",
                "protocol": "",
                "authorization": "",
                "public_url": "",
                "qrcode": "",
            },
            "nfse": {"id": 0},
            "road": {
                "travel_item_id": 0,
                "ticket_item_id": *********,
                "travel_item_service_travel": "",
                "datetime_start": "2025-10-25 17:00:00",
                "datetime_departure": "2025-10-25 20:10:00",
                "datetime_arrival": "2025-10-26 07:35:00",
                "duration": "685",
                "eticket_number": "",
                "seat_number": 49,
                "direction": "1",
                "status": "confirmed",
                "company_issued": {"acronym_state": ""},
                "boarding": {
                    "id": 82,
                    "code": "0241",
                    "name": "FLORIANÓPOLIS",
                    "locality": {
                        "name": "FLORIANÓPOLIS",
                        "state": {"acronym": "SC"},
                    },
                },
                "landing": {
                    "id": 1020,
                    "code": "6451",
                    "name": "SÃO PAULO - TIETÊ",
                    "locality": {"name": "SÃO PAULO", "state": {"acronym": "SP"}},
                },
                "company": {
                    "id": 88126,
                    "code": "00053",
                    "usual_name": "SOLIMOES",
                    "logo": "https://res.cloudinary.com/eumais/image/upload/v1574099142/logo/companies/eucatur.png",
                },
                "line": {
                    "code": "",
                    "prefix": "SCSP0018028",
                    "description": "TUBARÃO x APARECIDA",
                    "federal_code": "16128",
                    "federal_description": "TUBARÃO x APARECIDA",
                },
                "tribute": {
                    "icms_percentage": 12,
                    "icms_value": 36.81,
                    "other_taxes_percentage": 25.45,
                    "other_taxes_value": 78.07,
                },
                "centralizing_company": {
                    "name": "SOLIMÕES TRANSPORTE DE PASSAGENS E CARGAS EIRELLI",
                    "cnpj": "07.549.414/0047-04",
                    "state_registration": "*********",
                    "municipal_registration": "123",
                    "address": {
                        "public_place": "AV GOVERNADOR IVO SILVEIR",
                        "number": "2735",
                        "neighborhood": "CAPOEIRAS",
                        "cep": "88085001",
                        "complement": "TERREO",
                        "locality": {
                            "name": "FLORIANÓPOLIS",
                            "ibge_code": "4205407",
                            "state": {"acronym": "SC", "icms_percentage": "12"},
                        },
                    },
                },
                "vehicle": {
                    "bpe_code": "4",
                    "bpe_description": "Leito com ar condicionado",
                },
                "electronic_boarding": {
                    "qrcode_bpe": "iVBORw0KGgoAAAANSUhEUgAAAQAAAAEAAQMAAABmvDolAAAABlBMVEX///8AAABVwtN+AAAD1ElEQVR42uyZMY4zoRKECxEQ9g2Gi4zMtQhWwtIGXAuLi8zcgJAATT01/9pPG77APAdLZK2+YGa6uquaxd/5O//jSeRlTlJI1kpSWkcaCCRXARG4+bNR7m4AngcbomO5AfI5QBg3cwkbEms9Ni9k7rbc/LUUwCaN+Q5gA0QQ3dhXA9sGaY53Sx5oZE8DSwHgZg4tTLqHYi4IHfuOX9V8M6Cq3jYRyfm71vM8Wusx7vsv2f+/AT3mEmHOZDEnQX2f8WsEvBmI+36DYWPuqZInoYOg7NhMWweEx3npk/XEysuzudyBfxVeBSColluErbwMofpGeFyeqwDYAn+2Bpc5gM2DPaZir+1ZrQ8AXLGXOYRA+i7wahhAGuGC4SogBj4Oo5bVse/6kJg/zfFU1PsBVwJPki2RxZ4kGlIuu+HPGFwBsGDbpCEijX3zbCrw3T7Ok6uAGIp2N5j5XcJJbagYd3v5Qz4GSGP/9xYua344POi6G/Zx/Mh6ARBDCYcnSG0sfynAHmrZ1gEIWk4VddIBPHtrGgPZVgGuDh2D+qVGKJuh9pUbtjy9ewGQCsx5NsgdXzNHNWHmsDx+JswnAAiVKp7513oBDdPGHzyXAa4Wc2rEUynPsCfurjVeCERL8hD9ULaSl2+c85lPRS0AEAa8PlrOrMXwoHSXteVfxXo7kDh0CRLJ969QT50wcLnsN8/PAaIdUJeA619W3wLN5Rn2noFzAaDiudCkx8T5C4ha4mshsIfH4SmSe+JQgYvLd51AzwzzfsDpAuJJuUdg38zRCHfHf4u1Aqi6djSB69iDhl72pFvz6yE/AeCYSUuyOqc5PFRcu+VzO1gAJFb1Td0Whx0wB+l61Gr6tgpAUP0KJXc77GEOzGqq1GUh8DjmhqgBwl9epKceSlCvXAVg7hrSIr6mH0tz1GKdz/b+ACDNRqeuk9jDAa+TOA/L88ezVgDD8tog0t13Lf7w0qgr7WtTWwC4YsvmG6VHhLHBt8a70wC4EBh7OGd8ct9Fs8Q0hpnBsQrAHmZ3a9a1xVxeB0yqtfhn2PsAIFVe5hII+5fW1Tf0pAOR6wA3YFRRJL+rTqMmiEB48MAqALbczIXWotOQeczgzRK4EgCwGVU1bC06iWX6FF7VfD+QNDH5hhbjl62nzpWu69vtk4B52/z8kuXHuzPHK5mvAMK46RSUrnagUqa2f5j3iAsBw9aY88CuGRwzUt2wyUpgM01aysPWeZns8h3QJ18GADeY1jT91jHvSFVEfLwuDz8AmP99MOqV8cvOa0R2x7H/kv2bgb/zd17nPwEAAP//hhQSbC0xsKkAAAAASUVORK5CYII=",
                    "TicketGate": {
                        "type": "QRCode",
                        "number": "",
                        "type_tariff": "",
                        "image": "",
                    },
                },
            },
            "amount": 315.73,
            "utilizers": [
                {
                    "first_name": "Fernando",
                    "last_name": "Nakamuta",
                    "full_name": "",
                    "document_type": "RG",
                    "document_number": "386642709",
                    "birth": "",
                }
            ],
            "components": [
                {
                    "id": 7242629,
                    "sale_item_type": "VendaItemBPe",
                    "sale_item_id": 34051965,
                    "type": "boarding_fee",
                    "value": 8.99,
                },
                {
                    "id": 7242628,
                    "sale_item_type": "VendaItemBPe",
                    "sale_item_id": 34051965,
                    "type": "tariff",
                    "value": 306.74,
                },
            ],
            "statuses": [
                {
                    "id": 2301449,
                    "sale_item_type": "VendaItemBPe",
                    "sale_item_id": 34051965,
                    "status": "confirmed",
                    "created_at": "2025-10-10 14:50:57",
                }
            ],
        }
    ],
}


BPE_RESPONSE = {
    "id": 98656664,
    "sectional_id": 6588,
    "sectional_cod": "9193",
    "sectional_name": "BUSER BRASIL TECNOLOGIA LTDA",
    "person_id": 0,
    "person_client_id": 0,
    "person_salesman_id": 257251,
    "user_id": 3829,
    "operation_date": "2025-09-28",
    "type": "Venda",
    "system": "portais",
    "confirmed": True,
    "sectional_request_id": 0,
    "person_request_id": 0,
    "created_at": "2025-09-28 00:35:10",
    "amount": 0,
    "items": [
        {
            "key": "VIB-33882108",
            "sale_id": 98656664,
            "localizer": "00058-BPE0795RR-000-0000072252",
            "transaction": "",
            "created_at": "2025-09-30 18:31:41",
            "bpe": {
                "id": 0,
                "contingency": "",
                "access_key": "14250934473546000343630000000722521338821081",
                "protocol": "314250000299556",
                "authorization": "2025-09-30 19:31:41",
                "public_url": "https://dfe-portal.svrs.rs.gov.br/BPE/Consulta",
                "qrcode": "iVBORw0KGgoAAAANSUhEUgAAAQAAAAEAAQMAAABmvDolAAAABlBMVEX///8AAABVwtN+AAACWklEQVR42uyZvZErIRCEewsDkxAIhcz2p5QYoRAC5hqU+lUPOq3u7vmC94TB3q0+Q1PM9PQgfNZn/ZdrIck7ErkjsSzMwFaT3uaJgBPAan/63OwZj5oAhKmAyLp6WsD22akwPduAAHn3bPrunse0AIIyigfpyzIhoIzyj+JQXZzxjl8p92bAitfnFo4zubKGfCqtflb34AAeddufcQ/8q5wODiijdE6urkDDIvUxIW1DAXptG+nKUnGaGGGrEwEA3DNMBYwTG6ktzwMoOO4+u7qcyUmCEK0Hv1T3+AAQEay6d6Xc+qWmL9U9BCD1ASuQWqQ6r6WVe2bUBMDComTqwb1atf15WBMA8D2joFjNBVGdl8VxKADYdBabJAjAGXeAZZsKUF1YSegY4i6rdg8Z8Xal3ASALy3ckZrk0/Ee5J2/Ke0IgKI41GgXZsdDdbtp+6aTowMLi2VUw2pjVMi+rIH+8g8zANZ51bO4q85Zk7y9TNtIgC/WszSFZCedZDHZvF0pNz6wsPR2pX+ahhObUF79wwQAEF0facksAJoO4V+F9O2AOTH5r2AZdVQJfU1nvOpiBuDRrszpAGvIFjHi7bpumgJwVdVQtzMp6vyYUNw1wkwBWLZZcVg1eDNtl5cbAPiau/v9pDb5NXjuPwbzoYHHLSucnn2klev8Vt0TAP0+ypqaaVRvap63K6MGAGJvV3VjNlcsQ0zp0HwANUZZ0zVvn36FOQWQW1BTk1Vjd0E/rmHfDLz8dtB1Mh7MvmAq4HHLahc1TT3L1Mc/R9opgM/6rH9s/QkAAP//tcj55IYl6JMAAAAASUVORK5CYII=",
            },
            "nfse": {"id": 0},
            "road": {
                "travel_item_id": 0,
                "ticket_item_id": *********,
                "travel_item_service_travel": "",
                "datetime_start": "2025-09-30 19:00:00",
                "datetime_departure": "2025-09-30 19:00:00",
                "datetime_arrival": "2025-10-01 05:30:00",
                "duration": "630",
                "eticket_number": "",
                "seat_number": 15,
                "direction": "2",
                "status": "confirmed",
                "company_issued": {"acronym_state": ""},
                "boarding": {
                    "id": 46,
                    "code": "0795",
                    "name": "BOA VISTA",
                    "locality": {"name": "BOA VISTA", "state": {"acronym": "RR"}},
                },
                "landing": {
                    "id": 58,
                    "code": "0788",
                    "name": "MANAUS",
                    "locality": {"name": "MANAUS", "state": {"acronym": "AM"}},
                },
                "company": {
                    "id": 6050268,
                    "code": "00058",
                    "usual_name": "CAPITAL DO CAFE",
                    "logo": "",
                },
                "line": {
                    "code": "",
                    "prefix": "AMRR1559001",
                    "description": "MANAUS x BOA VISTA",
                    "federal_code": "11406",
                    "federal_description": "MANAUS x BOA VISTA",
                },
                "tribute": {
                    "icms_percentage": 12,
                    "icms_value": 17.56,
                    "other_taxes_percentage": 25.45,
                    "other_taxes_value": 37.24,
                },
                "centralizing_company": {
                    "name": "CAPITAL DO CAFE TRANSPORTE COLETIVO DE PASSAGEIROS LTDA",
                    "cnpj": "34.473.546/0003-43",
                    "state_registration": "*********",
                    "municipal_registration": "3753883",
                    "address": {
                        "public_place": "RUA PACARAIMA",
                        "number": "782",
                        "neighborhood": "SAO VICENTE",
                        "cep": "69303360",
                        "complement": "SALA 03",
                        "locality": {
                            "name": "BOA VISTA",
                            "ibge_code": "1400100",
                            "state": {"acronym": "RR", "icms_percentage": "20"},
                        },
                    },
                },
                "vehicle": {"bpe_code": "3", "bpe_description": "Semileito"},
                "electronic_boarding": {
                    "qrcode_bpe": "iVBORw0KGgoAAAANSUhEUgAAAQAAAAEAAQMAAABmvDolAAAABlBMVEX///8AAABVwtN+AAADzklEQVR42uyZP248rxLECxEQcoPhIqPlWgQrzUgbzLWw5iJwA0ICRD01+8dfv+wXLN7ARGj8scQ2RXc14G/8jf84NrIvjigI0BGOBRVbgyc5CwjAZVEFJQTAZxLF7BvjBbCfA/h2UQmwx77FFS4R2KqOF9enAi4T5A6sElOwYl1nA3C0JG8nyVRoKlZMBYCLyoUIYWOUkBVTTcOP3XwzMFS9WGuP43aeOadSagjr+lP2vwzIGKo2x41R5USaHevPFPBmIPiTOd2Xe5I5FQtDxovKnAVs5xmXZezcRlEUWQWAS3YWEPTJJMkvBKye3UEE3nyHK/OAhgVDMDvgkrOo2w6orvgxgKFkPBBbvUInl2R6NNEZpgFxTEuBaavuC4gq4f3KfRqwNd8d5e+7PpvqKIXHMZKRnQbIfLGEqVfdxnJJitQVZwEBnl0VWxA0TznmpQYT4e9H/jOAremocgF3c2vA4qw1rCukkM0DPJlLAcwt6iTFmzs8v0P9fiB4RkUWuwcdtSiZrFvzcXmVg7cD4ixzsuRRr+u6AJYI5uRXfviHCUAAFCXzb/vVt0WSsni5czjhjwFW3Z1Y3a1uTd+N1lE1v+vF+wET/Vd+pCAyLWAxB4f9xSwgaDFSsGWjrLG7ETNxUa/a/XbANFmPtaxBn6JvWFMNG56mdwIA3y4LQLvj6u9+0vBguzxP90cA6wqXaW0NVx9VcmA11bfXbk4AzEl2xcJjv+phOFmDIdkfdXMCME53F0Xt0G1xyUrHep58/ogJAKCZJRXXjdHnnKwV76njK5IzAP/VXSn2ONoKqERyD/BdPVX9AUCQjKfkq5TxoWqyatnOacBGKdMFkoJW8Q/SLW5N/qfMAsb1QoeUA+gzScWWDOSj6pgFYNURqkjtljSYHAqCoUztRKBLdCz3q24uU4oT1lXlzI8BTIS6m72A9dEZBQnl8+TNAJrucCyWvEXNnMYq/TlMxTzgS2q3PSrguxMPs0OzuzwNEOOdJQ8O1yDuqTxyY7KzgIBVMdPCSC5e4Ehz7Dr+0yX9PrCNCyArRusmLa2zhdXw7K5PA0yDWIUCc/CUuQVlkSo9Te/7Aejok6KYvY3kkNG4mOrPNmkCcL9QEat7Gz0bJcPIQVecBWx33w0JFE+RN+vjlrt8DDBeH+T475vsYXfWjkKG5duJvR0Yrw+WJQQtAidtNUfzr92cBLgE2oM79PhqpFnp3ze9cwCZm/0KzwSxnrus/P+eSN4JABdgGO+tiZ+EeDn4+K9gfht4vD5wtPZSsAuGgVB5HvA3/sZr/C8AAP//aMy6JRY52MEAAAAASUVORK5CYII=",
                    "TicketGate": {
                        "type": "QRCode",
                        "number": "",
                        "type_tariff": "",
                        "image": "",
                    },
                },
            },
            "amount": 0,
            "utilizers": [
                {
                    "first_name": "Luiz",
                    "last_name": "silva",
                    "full_name": "",
                    "document_type": "RG",
                    "document_number": "36139100",
                    "birth": "",
                }
            ],
            "components": None,
            "statuses": None,
        }
    ],
}


@pytest.fixture
def client():
    return EulabsClient(
        base_url="https://eulabs.test",
        api_id="test",
        api_key="test",
    )


async def test_travels_search(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://eulabs.test/road/travels/search"
            "?departure_date=2025-01-09"
            "&origin_sectional_id=1001"
            "&destiny_sectional_id=2001"
        ),
        json=[
            {
                "key": "travel_key_123",
                "id": 1,
                "origin_sectional_id": 1001,
                "destiny_sectional_id": 2001,
                "datetime_departure": "2025-01-09T06:00:00Z",
                "datetime_arrival": "2025-01-09T12:00:00Z",
                "duration": "6:00",
                "price": 150.00,
                "price_promotional": 120.00,
                "free_seats": 38,
                "free_seats_benefits": [{"type": "student", "free": 2, "amount": 38}],
                "items": [
                    {
                        "id": 1,
                        "road_item_id": 101,
                        "gateway_id": 1,
                        "gateway_type": "road",
                        "station_id_origin": 1001,
                        "station_id_destiny": 2001,
                        "service_travel": "EXEC",
                        "reference_travel": "REF123",
                        "datetime_departure": "2025-01-09T06:00:00Z",
                        "datetime_arrival": "2025-01-09T12:00:00Z",
                        "duration": "6:00",
                        "free_seats": 38,
                        "tariff": 120.00,
                        "insurance": 5.00,
                        "fee": 10.00,
                        "travel_item_toll": 15.00,
                        "price": 150.00,
                        "price_promotional": 120.00,
                        "tax": 0.00,
                        "line_code": "LINE123",
                        "direction": "IDA",
                        "vehicle_id": 42,
                        "class": {
                            "id": 1,
                            "long_name": "Executivo",
                            "short_name": "EXEC",
                        },
                        "company": {
                            "id": 1,
                            "name": "Test Company",
                            "code": "TEST",
                            "logo": "",
                        },
                        "tariffs": None,
                    }
                ],
            }
        ],
    )

    response = await client.travels_search(
        departure_date=date(2025, 1, 9),
        origin_sectional_id=1001,
        destiny_sectional_id=2001,
    )

    assert response == [
        Travel(
            key="travel_key_123",
            id=1,
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
            datetime_departure="2025-01-09T06:00:00Z",
            datetime_arrival="2025-01-09T12:00:00Z",
            duration="6:00",
            price=150.00,
            price_promotional=120.00,
            free_seats=38,
            free_seats_benefits=[FreeSeatBenefit(type="student", free=2, amount=38)],
            items=[
                Item(
                    id=1,
                    road_item_id=101,
                    gateway_id=1,
                    gateway_type="road",
                    station_id_origin=1001,
                    station_id_destiny=2001,
                    service_travel="EXEC",
                    reference_travel="REF123",
                    datetime_departure="2025-01-09T06:00:00Z",
                    datetime_arrival="2025-01-09T12:00:00Z",
                    duration="6:00",
                    free_seats=38,
                    tariff=120.00,
                    insurance=5.00,
                    fee=10.00,
                    travel_item_toll=15.00,
                    price=150.00,
                    price_promotional=120.00,
                    tax=0.00,
                    line_code="LINE123",
                    direction="IDA",
                    vehicle_id=42,
                    class_info=ClassInfo(
                        id=1, long_name="Executivo", short_name="EXEC"
                    ),
                    company=Company(id=1, code="TEST", name="Test Company", logo=""),
                    tariffs=None,
                )
            ],
        )
    ]


async def test_travels_search_raises_http_error_if_status_404(
    httpx_mock: HTTPXMock, client: EulabsClient
):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://eulabs.test/road/travels/search"
            "?departure_date=2025-01-09"
            "&origin_sectional_id=1001"
            "&destiny_sectional_id=2001"
        ),
        status_code=404,
    )
    with pytest.raises(httpx.HTTPStatusError) as exc:
        await client.travels_search(
            departure_date=date(2025, 1, 9),
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
        )
        assert exc.value.response.status_code == 404


async def test_list_road_travels_summary(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/summary?initial_departure_date=2025-01-09&final_departure_date=2025-01-10",
        json=[
            {
                "id": 1,
                "line_code": "LINE123",
                "departure_date": "2025-01-09",
                "departure_time": "06:00",
                "description": "Test Travel",
                "company_id": 1,
                "schedule_id": 101,
            }
        ],
    )

    summaries = await client.list_road_travels_summary(
        initial_departure_date=date(2025, 1, 9),
        final_departure_date=date(2025, 1, 10),
    )

    assert len(summaries) == 1
    summary = summaries[0]
    assert summary.line_code == "LINE123"
    assert summary.departure_date == "2025-01-09"


async def test_seating_map(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/travel_key_123/seating-map",
        json=[
            {
                "floor_1": [
                    {
                        "number": 1,
                        "line": 1,
                        "column": 1,
                        "busy": False,
                        "category": "EXEC",
                        "tariff": 120.00,
                        "amount": 150.00,
                        "aditional_amount": 0.00,
                        "price_discount": 0.00,
                        "woman_space": False,
                        "benefits_values": [],
                    }
                ],
                "floor_2": None,
                "free_seats": 41,
            }
        ],
    )

    seating_maps = await client.seating_map("travel_key_123")

    assert seating_maps == [
        SeatingMap(
            floor_1=[
                Seat(
                    number=1,
                    line=1,
                    column=1,
                    busy=False,
                    category="EXEC",
                    tariff=120.00,
                    amount=150.00,
                    aditional_amount=0.00,
                    price_discount=0.00,
                    woman_space=False,
                    benefits_values=[],
                )
            ],
            floor_2=None,
            free_seats=41,
        )
    ]


async def test_seating_map_invalid_travel(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/travel_key_123/seating-map",
        json={"message": "Viagem informada é inválida!"},
        status_code=400,
    )

    with pytest.raises(TravelUnavailable):
        await client.seating_map("travel_key_123")


async def test_block_seats(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="POST",
        url="https://eulabs.test/road/travels/travel_key_123/seats",
        json={"selected_seat_Key": "seat_key_123"},
    )
    block_seats_request = [BlockSeatRequest(seat=1)]

    response = await client.block_seats("travel_key_123", block_seats_request)

    assert response.selected_seat_Key == "seat_key_123"


async def test_unblock_seats(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="DELETE",
        url="https://eulabs.test/road/travels/travel_key_123/selected_seats/seat_key_123",
        status_code=204,
    )

    await client.unblock_seats("travel_key_123", "seat_key_123")


async def test_sectionals(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/sectionals?id=1&code=TEST&is_road_station=false&locality_id=100",
        json=[
            {"code": "TEST", "description": "Test Station", "id": 1, "uf_acronym": "SP"}
        ],
    )

    sectionals = await client.sectionals(
        id=1, code="TEST", is_road_station=False, locality_id=100
    )

    assert len(sectionals) == 1
    assert sectionals[0] == Sectional(
        code="TEST", description="Test Station", id=1, uf_acronym="SP"
    )


async def test_get_road_travel_summary(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/summary/123",
        json=[
            {
                "arrival_zone": "SP",
                "departure_time_zone": "SP",
                "local_arrival_date_time": "2025-01-09T12:00:00",
                "local_exit": "2025-01-09T06:00:00",
                "seccional_code": "TEST",
                "seccional_id": 1,
                "seccional_name": "Test Station",
                "stop_time": "00:00",
                "total_time": "06:00",
                "total_km": 300.0,
                "uf_acronym": "SP",
            }
        ],
    )

    summary = await client.get_road_travel_summary(123)

    assert summary == [
        TravelLeg(
            arrival_zone="SP",
            departure_time_zone="SP",
            local_arrival_date_time="2025-01-09T12:00:00",
            local_exit="2025-01-09T06:00:00",
            seccional_code="TEST",
            seccional_id=1,
            seccional_name="Test Station",
            stop_time="00:00",
            total_time="06:00",
            total_km=300.0,
            uf_acronym="SP",
        )
    ]


async def test_get_road_travel_detail(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/detail/123",
        json=[
            {
                "origin_id": 1,
                "origin_name": "Origin City",
                "destination_id": 2,
                "destination_name": "Destination City",
                "class": "EXEC",
                "seats": 42,
                "capacity": 42,
                "class_description": "Executivo",
            }
        ],
    )

    details = await client.get_road_travel_detail(123)

    assert details == [
        TravelDetail(
            origin_id=1,
            origin_name="Origin City",
            destination_id=2,
            destination_name="Destination City",
            class_code="EXEC",
            seats=42,
            capacity=42,
            class_description="Executivo",
        )
    ]


async def test_travels_search_with_none_items(
    httpx_mock: HTTPXMock, client: EulabsClient
):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://eulabs.test/road/travels/search"
            "?departure_date=2025-01-09"
            "&origin_sectional_id=1001"
            "&destiny_sectional_id=2001"
        ),
        json=[
            {
                "key": "travel_key_123",
                "id": 1,
                "origin_sectional_id": 1001,
                "destiny_sectional_id": 2001,
                "datetime_departure": "2025-01-09T06:00:00Z",
                "datetime_arrival": "2025-01-09T12:00:00Z",
                "duration": "6:00",
                "price": 150.00,
                "price_promotional": 120.00,
                "free_seats": 38,
                "free_seats_benefits": None,
                "items": None,
            }
        ],
    )

    response = await client.travels_search(
        departure_date=date(2025, 1, 9),
        origin_sectional_id=1001,
        destiny_sectional_id=2001,
    )

    assert response == [
        Travel(
            key="travel_key_123",
            id=1,
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
            datetime_departure="2025-01-09T06:00:00Z",
            datetime_arrival="2025-01-09T12:00:00Z",
            duration="6:00",
            price=150.00,
            price_promotional=120.00,
            free_seats=38,
            free_seats_benefits=None,
            items=None,
        )
    ]


async def test_post_sales(httpx_mock: HTTPXMock, client: EulabsClient):
    json_response = {
        "sale_id": 94249193,
        "amount": 0,
        "items": [
            {
                "seat": 37,
                "key": "VIB-23609113",
                "localizer": "00053-VOUCHER-D7AA5-0984284236",
                "amount": 0,
                "utilizer": {
                    "first_name": "EZIO",
                    "last_name": "MORET",
                    "full_name": "",
                    "document_type": "RG",
                    "document_number": "454456",
                    "cpf": "393.409.520-81",
                    "phone": "(69) 99280-8522",
                    "birth": "1948-11-21",
                },
                "boarding": {"id": 5, "name": "PORTO VELHO", "uf": "RO"},
                "landing": {"id": 218, "name": "CASCAVEL", "uf": "PR"},
                "statuses": [
                    {
                        "id": 930115,
                        "sale_item_type": "VendaItemBPe",
                        "sale_item_id": 23609113,
                        "status": "confirmed",
                        "created_at": "2024-06-05 21:01:30",
                    }
                ],
            }
        ],
    }
    httpx_mock.add_response(
        method="POST",
        url="https://eulabs.test/sales?device_type=desktop",
        json=json_response,
    )
    sales_request = [
        SaleItemRequest(
            road=SaleItemRoadRequest(
                selected_seat_key="abc123xyz",
                utilizer=SaleUtilizerRequest(
                    first_name="John",
                    last_name="Doe",
                    full_name="John Doe",
                    document_type=SaleUtilizerRequest.DocType.RG,
                    document_number="123456789",
                    cpf="123.456.789-00",
                    phone="(11) 99999-8888",
                    birth=date(1990, 5, 15),
                    benefit=SaleBenefitYoungRequest(
                        type=SaleBenefitYoungRequest.Type.YOUNG,
                        benefit_number="YNG-2025-0001",
                        benefit_issue_date=date(2024, 3, 1),
                        benefit_expiration=date(2025, 3, 1),
                    ),
                ),
            )
        )
    ]

    response = await client.post_sales(sales_request)
    assert response == SaleResponse(
        sale_id=94249193,
        amount=0,
        items=[
            SaleItemResponse(
                seat=37,
                key="VIB-23609113",
                localizer="00053-VOUCHER-D7AA5-0984284236",
                amount=0,
                utilizer=SaleUtilizerResponse(
                    first_name="EZIO",
                    last_name="MORET",
                    full_name="",
                    document_type="RG",
                    document_number="454456",
                    cpf="393.409.520-81",
                    phone="(69) 99280-8522",
                    birth="1948-11-21",
                ),
                boarding=SaleBoardingLandingResponse(id=5, name="PORTO VELHO", uf="RO"),
                landing=SaleBoardingLandingResponse(id=218, name="CASCAVEL", uf="PR"),
                statuses=[
                    SaleStatusResponse(
                        id=930115,
                        sale_item_type="VendaItemBPe",
                        sale_item_id=23609113,
                        status="confirmed",
                        created_at="2024-06-05 21:01:30",
                    )
                ],
            )
        ],
    )


async def test_post_sales_invalid_response(httpx_mock: HTTPXMock, client: EulabsClient):
    json_response = {
        "items": [
            {
                "seat": 37,
                "camelo": 10,
                "boarding": {"id": 5},
                "landing": {"id": 218},
                "statuses": {
                    "id": 930115,
                    "sale_item_type": "VendaItemBPe",
                    "sale_item_id": 23609113,
                    "status": "confirmed",
                    "created_at": "2024-06-05 21:01:30",
                },
            }
        ],
    }
    httpx_mock.add_response(
        method="POST",
        url="https://eulabs.test/sales?device_type=desktop",
        json=json_response,
    )
    sales_request = [
        SaleItemRequest(
            road=SaleItemRoadRequest(
                selected_seat_key="abc123xyz",
                utilizer=SaleUtilizerRequest(
                    first_name="John",
                    last_name="Doe",
                    full_name="John Doe",
                    document_type=SaleUtilizerRequest.DocType.RG,
                    document_number="123456789",
                    cpf="123.456.789-00",
                    phone="(11) 99999-8888",
                    birth=date(1990, 5, 15),
                    benefit=SaleBenefitYoungRequest(
                        type=SaleBenefitYoungRequest.Type.YOUNG,
                        benefit_number="YNG-2025-0001",
                        benefit_issue_date=date(2024, 3, 1),
                        benefit_expiration=date(2025, 3, 1),
                    ),
                ),
            )
        )
    ]
    with pytest.raises(OTAIssuerInvalidResponse):
        await client.post_sales(sales_request)


async def test_get_cancel_conditions(httpx_mock: HTTPXMock, client: EulabsClient):
    response = {
        "fine_amount": 0,
        "refund_amount": 520.99,
        "status": "authorized",
        "type": ["Cancel"],
        "key": "eyJhbGciOiJIUzM4NCIsInR5cCI6IkpXVCJ9.eyJhbGxvd19yZWZ1bmQiOmZhbHNlLCJhbW91bnQiOjUyMC45OSwiYnBlX3NhbGVfaXRlbV9icGVfaWQiOjAsImNyZWF0ZWRfYXQiOiIyMDI1LTA5LTA4IDExOjUzOjAyIiwiZXhwaXJhX2F0IjoiMjAyNS0wOS0wOCAxNDowNDowMiIsImZpZGVsaXR5X2Rpc2NvdW50IjowLCJmaW5lX2Ftb3VudCI6MCwiZmluZV9wZXJjZW50IjowLCJoYXNfYnBlIjpmYWxzZSwiaGFzX2ZpZGVsaXR5IjpmYWxzZSwia2V5IjoiVklCLTMzNDk0MjEyIiwicmVmdW5kX2Ftb3VudCI6NTIwLjk5LCJzdGF0dXMiOiJhdXRob3JpemVkIiwidHlwZSI6IkNhbmNlbCIsInZhbHVlX3BvaW50cyI6MH0._y7hA8tZS2URXU_dhwynPBPTsZ7nkx_DZa3K2D6zf7fFMvK4s3p5Jwlvjv2UlnIL",
        "allow_refund": False,
        "amount": 520.99,
        "availability_digital_wallet": False,
    }
    item_key = "VIB-33494212"
    httpx_mock.add_response(
        method="GET",
        url=f"https://eulabs.test/sales/cancel_conditions/{item_key}",
        json=response,
    )

    cancel_conditions = await client.get_cancel_conditions(item_key)

    assert cancel_conditions == CancelConditionResponse(
        fine_amount=0,
        refund_amount=520.99,
        status="authorized",
        type=["Cancel"],
        key="eyJhbGciOiJIUzM4NCIsInR5cCI6IkpXVCJ9.eyJhbGxvd19yZWZ1bmQiOmZhbHNlLCJhbW91bnQiOjUyMC45OSwiYnBlX3NhbGVfaXRlbV9icGVfaWQiOjAsImNyZWF0ZWRfYXQiOiIyMDI1LTA5LTA4IDExOjUzOjAyIiwiZXhwaXJhX2F0IjoiMjAyNS0wOS0wOCAxNDowNDowMiIsImZpZGVsaXR5X2Rpc2NvdW50IjowLCJmaW5lX2Ftb3VudCI6MCwiZmluZV9wZXJjZW50IjowLCJoYXNfYnBlIjpmYWxzZSwiaGFzX2ZpZGVsaXR5IjpmYWxzZSwia2V5IjoiVklCLTMzNDk0MjEyIiwicmVmdW5kX2Ftb3VudCI6NTIwLjk5LCJzdGF0dXMiOiJhdXRob3JpemVkIiwidHlwZSI6IkNhbmNlbCIsInZhbHVlX3BvaW50cyI6MH0._y7hA8tZS2URXU_dhwynPBPTsZ7nkx_DZa3K2D6zf7fFMvK4s3p5Jwlvjv2UlnIL",
        allow_refund=False,
        amount=520.99,
        availability_digital_wallet=False,
    )


async def test_post_cancel_sale(httpx_mock: HTTPXMock, client: EulabsClient):
    json_response = {"type_this_refund": "onSolicitationPortais"}
    cancel_key = "eyJhbGciOiJIUzM4NCIsInR5cCI6IkpXVCJ9.eyJhbGxvd19yZWZ1bmQiOmZhbHNlLCJhbW91bnQiOjUyMC45OSwiYnBlX3NhbGVfaXRlbV9icGVfaWQiOjAsImNyZWF0ZWRfYXQiOiIyMDI1LTA5LTA4IDExOjUzOjAyIiwiZXhwaXJhX2F0IjoiMjAyNS0wOS0wOCAxNDowNDowMiIsImZpZGVsaXR5X2Rpc2NvdW50IjowLCJmaW5lX2Ftb3VudCI6MCwiZmluZV9wZXJjZW50IjowLCJoYXNfYnBlIjpmYWxzZSwiaGFzX2ZpZGVsaXR5IjpmYWxzZSwia2V5IjoiVklCLTMzNDk0MjEyIiwicmVmdW5kX2Ftb3VudCI6NTIwLjk5LCJzdGF0dXMiOiJhdXRob3JpemVkIiwidHlwZSI6IkNhbmNlbCIsInZhbHVlX3BvaW50cyI6MH0._y7hA8tZS2URXU_dhwynPBPTsZ7nkx_DZa3K2D6zf7fFMvK4s3p5Jwlvjv2UlnIL"
    httpx_mock.add_response(
        method="POST",
        url=f"https://eulabs.test/sales/cancel_item/{cancel_key}/Cancel",
        json=json_response,
    )

    response = await client.post_cancel_sale(cancel_key)

    assert response == CancelSaleResponse(type_this_refund="onSolicitationPortais")


async def test_get_sales_info(httpx_mock: HTTPXMock, client: EulabsClient):
    json_response = SALES_INFO_RESPONSE
    sale_id = 94249193
    httpx_mock.add_response(
        method="GET",
        url=f"https://eulabs.test/sales/find/{sale_id}",
        json=json_response,
    )

    response = await client.get_sales_info(sale_id)

    assert isinstance(response, SaleInfoResponse)
    assert response.items
    assert response.items[0].road
    assert response.items[0].road.datetime_start == "2025-10-25 17:00:00"


async def test_get_bpe_info(httpx_mock: HTTPXMock, client: EulabsClient):
    json_response = BPE_RESPONSE
    sale_id = 94249193
    item_key = "VIB-33882108"
    httpx_mock.add_response(
        method="GET",
        url=f"https://eulabs.test/sales/{sale_id}/items/{item_key}/request_bpe",
        json=json_response,
    )

    response = await client.get_bpe_info(sale_id, item_key)

    assert isinstance(response, BPEResponse)
    assert response.items
    assert response.items[0].road
    assert response.items[0].road.datetime_start == "2025-09-30 19:00:00"
