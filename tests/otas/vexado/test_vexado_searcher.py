from datetime import date, datetime, timezone
from unittest import mock
from unittest.mock import AsyncMock, MagicMock

import pytest

from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    PlaceStatus,
    Seat,
    Stopover,
    Travel,
)
from marketplace.otas.exception import OTATimeoutException, SeatUnavailable
from marketplace.otas.vexado.client import (
    Assento,
    BloqueioResponse,
    Cidade,
    CidadesConexao,
    Fileira,
    ItinerarioResponse,
    MapaVeiculoDto,
    Passagem,
    Poltrona,
    Rota,
    SearchResponse,
    ServiceToken,
    TipoAjusteItinerario,
    TipoConfiguracaoPreco,
    TipoPrecoPadrao,
    TipoVeiculo,
    Trecho,
    VeiculoDto,
    VendaGratuidade,
    VexadoClient,
)
from marketplace.otas.vexado.searcher import VexadoSearcher


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=4,
        name="Vexado",
        provider="vexado",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=388,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=4,
            ),
            OTACompany(
                name="Test Company",
                external_id=833,
                cnpj="43214321432133",
                created_at=now,
                ota_config_id=4,
            ),
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=4,
                name="SEMI_LEITO",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="semi leito",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
        config={"username": "test", "password": "test"},
    )


@pytest.fixture
def mock_client():
    client = AsyncMock(spec=VexadoClient)
    client.auth_signin.return_value = MagicMock(
        service_token=ServiceToken(
            token=(
                "eyJhbGciOiJIUzUxMiJ9.eyJudXMiOiJNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJub21lVXN1YXJpb0NvbU51bWVyby"
                "I6IjE0MDAgLSBNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJzdWIiOiIxNDAwIiwiaWF0IjoxNzM2OTY2MDkxLCJleHAiO"
                "jE3MzY5ODc2OTF9.s93imRGqxeitXGzna3mT97x3R6_5feZEKhHzK5l526vPRkitXV72T0CxK1qPJaOyLwxItSAeei3T1KxWe94vdQ"
            ),
            refresh_token="refresh_token",
            user_fingerprint="",
            roles=None,
        )
    )
    return client


@pytest.fixture
def searcher(ota_config, mock_client):
    searcher = VexadoSearcher(
        ota_config=ota_config,
        base_url="https://vexado.test",
        username="test",
        password="test",
    )
    searcher._client = mock_client

    return searcher


async def test_search(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=4,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=4,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )
    departure_date = date(2025, 1, 8)
    mock_client.search.return_value = SearchResponse(
        passagensIda=[
            Passagem(
                idItinerario=335852,
                dataPartida="16/abr.",
                horaPartida="20:00",
                dataHoraPartida="2025-04-16T20:00:00",
                dataHoraChegada="2025-04-17T04:27:00",
                dataChegada="17/abr.",
                horaChegada="04:27",
                tipoVeiculo="SEMI_LEITO",
                descricaoTipoVeiculo="Semi Leito",
                assentosDisponiveis=10,
                idEmpresa=388,
                nomeEmpresa="VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
                preco="152.00",
                precoMeia="76.00",
                embarque="Terminal Rodoviário",
                andar=2,
                taxaEmbarque="7.17",
                pedagio="0.00",
                tarifaSeguro="0.00",
                precoANTT="199.00",
                precoANTTMeia="99.50",
                descRota="Brasília à Angra dos Reis",
                descontoMaximoUsuarioRota=None,
                veiculo="PLACA RMH7J37 26 POLTRONAS ",
                veiculoId=3696,
                duracao="8h 27m ",
                qtdDias=0,
                trechoOrigemId=29747,
                trechoDestinoId=29753,
                idRota=2426,
                seAplicaAjuste=False,
                nomePrestador=None,
                seTemConexao=False,
                nomesCidadesComConexao=[],
                cidadesComConexao=[],
                empresaHabilitadoValorAntt=False,
                usuarioEhHabilitadoVenderValorANTT=False,
                tipoCategoriaItinerario="UNICA",
                latitudeOrigem=None,
                longitudeOrigem=None,
                latitudeDestino=None,
                longitudeDestino=None,
                nota=4.8,
                pontoEmbarque="Terminal Rodoviário",
                pontoDesembarque="Terminal Rodoviário",
                integracao=False,
                fareClass=None,
                idFareClass=None,
                idEstacaoOrigem=None,
                idEstacaoDestino=None,
                codeEmpresa=None,
                api="VEXADO",
                acrescimoMaximoPassagem="00.00",
                valorAcrescimoMaximoPassagem="0.00",
                electronicTicketAvailable=False,
                imagemVeiculo=None,
                tipoImagemVeiculo="ONIBUS",
                vendaGratuidade=VendaGratuidade(
                    totalIdJovem100=0,
                    totalIdoso100=0,
                    totalIdJovem50=0,
                    totalIdoso50=0,
                    totalDeficiente=0,
                    vendeGratuidade=False,
                ),
                percentualDescontoPrice=None,
                valorSemDesconto="152.00",
                valorSemDescontoAntt="199.00",
                urlImagemEmpresa="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/0da338c4-3b9c-4e71-9504-61087bc01f09",
            )
        ],
        passagensRetorno=[],
        datasIdaProximas=None,
        datasRetornoProximas=None,
        origem=None,
        destino=None,
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="vexado",
        ota_config_id=4,
        code="388:2426:335852",
        group_class_code="335852",
        service_code="335852",
        company=OTACompany(
            ota_config_id=4,
            external_id=388,
            name="Test Company",
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        itinerary=[],
        origin=Place(
            slug="origin_city",
            name="Origin City",
            id=1,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        departure_at=datetime(2025, 4, 16, 20, 0),
        destination=Place(
            slug="destination_city",
            name="Destination City",
            id=2,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        arrival_at=datetime(2025, 4, 17, 4, 27),
        seat_type=OTASeatType(
            ota_config_id=4,
            name="SEMI_LEITO",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="semi leito",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        available_seats=10,
        total_seats=10,
        last_synced=mock.ANY,
        extra=dict(
            itinerario_id=335852,
            rota_id=2426,
            empresa_id=388,
            trechoOrigemId=29747,
            trechoDestinoId=29753,
            origem_id=origin.extra["id"],
            destino_id=destination.extra["id"],
            dataHoraPartida="2025-04-16T20:00:00",
            tipoVeiculo="SEMI_LEITO",
        ),
        price=152.0 + 7.17,
        single_ticket_connection=False,
    )
    assert results[0] == expected_travel


async def test_search_stopovers(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=4,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=4,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )
    departure_date = date(2025, 1, 8)
    mock_client.search.return_value = SearchResponse(
        passagensIda=[
            Passagem(
                idItinerario=335852,
                dataPartida="16/abr.",
                horaPartida="20:00",
                dataHoraPartida="2025-04-16T20:00:00",
                dataHoraChegada="2025-04-17T04:27:00",
                dataChegada="17/abr.",
                horaChegada="04:27",
                tipoVeiculo="SEMI_LEITO",
                descricaoTipoVeiculo="Semi Leito",
                assentosDisponiveis=10,
                idEmpresa=388,
                nomeEmpresa="VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
                preco="152.00",
                precoMeia="76.00",
                embarque="Terminal Rodoviário",
                andar=2,
                taxaEmbarque="7.17",
                pedagio="0.00",
                tarifaSeguro="0.00",
                precoANTT="199.00",
                precoANTTMeia="99.50",
                descRota="Brasília à Angra dos Reis",
                descontoMaximoUsuarioRota=None,
                veiculo="PLACA RMH7J37 26 POLTRONAS ",
                veiculoId=3696,
                duracao="8h 27m ",
                qtdDias=0,
                trechoOrigemId=29747,
                trechoDestinoId=29753,
                idRota=2426,
                seAplicaAjuste=False,
                nomePrestador=None,
                seTemConexao=False,
                nomesCidadesComConexao=[],
                cidadesComConexao=[
                    CidadesConexao(
                        idCidadeOrigem=5500,
                        idCidadeDestino=4742,
                        nomeCidade="Agudos do Sul - PR",
                    )
                ],
                empresaHabilitadoValorAntt=False,
                usuarioEhHabilitadoVenderValorANTT=False,
                tipoCategoriaItinerario="UNICA",
                latitudeOrigem=None,
                longitudeOrigem=None,
                latitudeDestino=None,
                longitudeDestino=None,
                nota=4.8,
                pontoEmbarque="Terminal Rodoviário",
                pontoDesembarque="Terminal Rodoviário",
                integracao=False,
                fareClass=None,
                idFareClass=None,
                idEstacaoOrigem=None,
                idEstacaoDestino=None,
                codeEmpresa=None,
                api="VEXADO",
                acrescimoMaximoPassagem="00.00",
                valorAcrescimoMaximoPassagem="0.00",
                electronicTicketAvailable=False,
                imagemVeiculo=None,
                tipoImagemVeiculo="ONIBUS",
                vendaGratuidade=VendaGratuidade(
                    totalIdJovem100=0,
                    totalIdoso100=0,
                    totalIdJovem50=0,
                    totalIdoso50=0,
                    totalDeficiente=0,
                    vendeGratuidade=False,
                ),
                percentualDescontoPrice=None,
                valorSemDesconto="152.00",
                valorSemDescontoAntt="199.00",
                urlImagemEmpresa="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/0da338c4-3b9c-4e71-9504-61087bc01f09",
            )
        ],
        passagensRetorno=[],
        datasIdaProximas=None,
        datasRetornoProximas=None,
        origem=None,
        destino=None,
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="vexado",
        ota_config_id=4,
        code="388:2426:335852",
        group_class_code="335852",
        service_code="335852",
        company=OTACompany(
            ota_config_id=4,
            external_id=388,
            name="Test Company",
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        itinerary=[],
        origin=Place(
            slug="origin_city",
            name="Origin City",
            id=1,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        departure_at=datetime(2025, 4, 16, 20, 0),
        destination=Place(
            slug="destination_city",
            name="Destination City",
            id=2,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        arrival_at=datetime(2025, 4, 17, 4, 27),
        seat_type=OTASeatType(
            ota_config_id=4,
            name="SEMI_LEITO",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="semi leito",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        available_seats=10,
        total_seats=10,
        last_synced=mock.ANY,
        extra=dict(
            itinerario_id=335852,
            rota_id=2426,
            empresa_id=388,
            trechoOrigemId=29747,
            trechoDestinoId=29753,
            origem_id=origin.extra["id"],
            destino_id=destination.extra["id"],
            dataHoraPartida="2025-04-16T20:00:00",
            tipoVeiculo="SEMI_LEITO",
        ),
        price=152.0 + 7.17,
        single_ticket_connection=True,
        stopovers=[
            Stopover(origin_ota_place_id=1001, destination_ota_place_id=5500),
            Stopover(origin_ota_place_id=5500, destination_ota_place_id=4742),
        ],
    )
    assert results[0] == expected_travel


async def test__has_few_seats_travel(searcher):
    search_response = [
        Passagem(
            idItinerario=335852,
            dataPartida="16/abr.",
            horaPartida="20:00",
            dataHoraPartida="2025-04-16T20:00:00",
            dataHoraChegada="2025-04-17T04:27:00",
            dataChegada="17/abr.",
            horaChegada="04:27",
            tipoVeiculo="SEMI_LEITO",
            descricaoTipoVeiculo="Semi Leito",
            assentosDisponiveis=10,
            idEmpresa=388,
            nomeEmpresa="VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
            preco="152.00",
            precoMeia="76.00",
            embarque="Terminal Rodoviário",
            andar=2,
            taxaEmbarque="7.17",
            pedagio="0.00",
            tarifaSeguro="0.00",
            precoANTT="199.00",
            precoANTTMeia="99.50",
            descRota="Brasília à Angra dos Reis",
            descontoMaximoUsuarioRota=None,
            veiculo="PLACA RMH7J37 26 POLTRONAS ",
            veiculoId=3696,
            duracao="8h 27m ",
            qtdDias=0,
            trechoOrigemId=29747,
            trechoDestinoId=29753,
            idRota=2426,
            seAplicaAjuste=False,
            nomePrestador=None,
            seTemConexao=False,
            nomesCidadesComConexao=[],
            cidadesComConexao=[],
            empresaHabilitadoValorAntt=False,
            usuarioEhHabilitadoVenderValorANTT=False,
            tipoCategoriaItinerario="UNICA",
            latitudeOrigem=None,
            longitudeOrigem=None,
            latitudeDestino=None,
            longitudeDestino=None,
            nota=4.8,
            pontoEmbarque="Terminal Rodoviário",
            pontoDesembarque="Terminal Rodoviário",
            integracao=False,
            fareClass=None,
            idFareClass=None,
            idEstacaoOrigem=None,
            idEstacaoDestino=None,
            codeEmpresa=None,
            api="VEXADO",
            acrescimoMaximoPassagem="00.00",
            valorAcrescimoMaximoPassagem="0.00",
            electronicTicketAvailable=False,
            imagemVeiculo=None,
            tipoImagemVeiculo="ONIBUS",
            vendaGratuidade=VendaGratuidade(
                totalIdJovem100=0,
                totalIdoso100=0,
                totalIdJovem50=0,
                totalIdoso50=0,
                totalDeficiente=0,
                vendeGratuidade=False,
            ),
            percentualDescontoPrice=None,
            valorSemDesconto="152.00",
            valorSemDescontoAntt="199.00",
            urlImagemEmpresa="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/0da338c4-3b9c-4e71-9504-61087bc01f09",
        )
    ]
    assert searcher._has_few_seats_travel(search_response) is False
    searcher.ota_config.search_cache.few_seats_threshold = 15
    assert searcher._has_few_seats_travel(search_response) is True


async def test_list_places(searcher, mock_client):
    cities = [
        Cidade(
            id=1135,
            uf="BA",
            descricaoUf="Bahia",
            nome="Itagimirim",
            nomeComUf="Itagimirim - BA",
            nomeComUfNormalizado="itagimirim-ba",
            codigoIbge="2915304",
            codigoAntt="4766",
            imagem=None,
            urlImagem=None,
        ),
        Cidade(
            id=5828,
            uf="SP",
            descricaoUf="São Paulo",
            nome="Santos",
            nomeComUf="Santos - SP",
            nomeComUfNormalizado="santos-sp",
            codigoIbge="3548500",
            codigoAntt="2130",
            imagem=None,
            urlImagem=None,
        ),
    ]

    mock_client.list_cities_by_company.return_value = cities

    places = await searcher.list_places()

    assert len(places) == 2
    ota_places = [
        OTAPlace(
            ota_config_id=searcher.ota_config.id,
            name=f"{city.nome} - {city.uf}",
            extra=city,
        )
        for city in cities
    ]
    assert all(ota_place in places for ota_place in ota_places)
    assert set(
        [
            args[1]["company_id"]
            for args in mock_client.list_cities_by_company.call_args_list
        ]
    ) == {
        388,
        833,
    }


async def test_list_places_with_config_company_id(searcher, mock_client):
    cities = [
        Cidade(
            id=1135,
            uf="BA",
            descricaoUf="Bahia",
            nome="Itagimirim",
            nomeComUf="Itagimirim - BA",
            nomeComUfNormalizado="itagimirim-ba",
            codigoIbge="2915304",
            codigoAntt="4766",
            imagem=None,
            urlImagem=None,
        ),
        Cidade(
            id=5828,
            uf="SP",
            descricaoUf="São Paulo",
            nome="Santos",
            nomeComUf="Santos - SP",
            nomeComUfNormalizado="santos-sp",
            codigoIbge="3548500",
            codigoAntt="2130",
            imagem=None,
            urlImagem=None,
        ),
    ]

    mock_client.list_cities_by_company.return_value = cities
    searcher.ota_config.config["company_id"] = 87882

    places = await searcher.list_places()

    assert len(places) == 2
    mock_client.list_cities_by_company.assert_called_once()
    assert mock_client.list_cities_by_company.call_args[1]["company_id"] == 87882


async def test_search_travels_timeout(searcher, mock_client):
    mock_client.search.side_effect = OTATimeoutException("error")
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)

    with pytest.raises(OTATimeoutException):
        await searcher._search_travels(
            mock_client.auth_signin(), date(2025, 1, 1), 1, 2
        )


async def test_travel_itinerary(searcher, mock_client):
    mock_client.itinerary.return_value = ItinerarioResponse(
        id=342345,
        dataPartida="2025-06-03",
        horaSaida="17:00",
        dataHoraPartida="03/06/2025 17:00",
        dataHoraChegada="04/06/2025 14:15",
        veiculoId=None,
        veiculoDto=VeiculoDto(
            id=3781,
            descricao="12016 - QKH9I47",
            empresaDto=None,
            mapaVeiculoDto=MapaVeiculoDto(
                id=249,
                fileirasPrimeiroAndar=[],
                fileirasSegundoAndar=[],
                seDoisAndares=False,
                quantidadeAssentos=60,
                modelo="DD 60 LUGARES PISO SUPERIOR 48 Poltronas e PISO INFERIOR 12 Poltronas",
                tipoVeiculo="ONIBUS",
            ),
            idRastreamento=None,
            seDoisAndares=False,
            imagens=None,
            placa="QKH9I47",
            modelo="Marcopolo Paradiso DD",
            tipoVeiculo=TipoVeiculo(chave="ONIBUS", valor="ônibus"),
            quilometragem=None,
            chassi=None,
            renavam="0110531996-0",
            anoFabricacao="2016",
            numeroEixo=3,
            dataVencimentoCsv="26/09/2025",
            exibirDetalhe=False,
            tipoServico=[],
        ),
        rotaId=None,
        rotaDto=Rota(
            id=4931,
            empresaDto=None,
            cidadeOrigem=Cidade(
                id=5500,
                uf="SP",
                descricaoUf="São Paulo",
                nome="Guarulhos",
                nomeComUf="Guarulhos - SP",
                nomeComUfNormalizado="guarulhos-sp",
                codigoIbge="3518800",
                codigoAntt="3561",
                imagem=None,
                urlImagem=None,
            ),
            cidadeDestino=Cidade(
                id=4742,
                uf="RS",
                descricaoUf="Rio Grande do Sul",
                nome="Porto Alegre",
                nomeComUf="Porto Alegre - RS",
                nomeComUfNormalizado="portoalegre-rs",
                codigoIbge="4314902",
                codigoAntt="63",
                imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                urlImagem=None,
            ),
            trechosDto=[
                Trecho(
                    id=52628,
                    idEmpresa=751,
                    cidadeDestino=Cidade(
                        id=5500,
                        uf="SP",
                        descricaoUf="São Paulo",
                        nome="Guarulhos",
                        nomeComUf="Guarulhos - SP",
                        nomeComUfNormalizado="guarulhos-sp",
                        codigoIbge="3518800",
                        codigoAntt="3561",
                        imagem=None,
                        urlImagem=None,
                    ),
                    cidadeOrigem=None,
                    cidadeOrigemAlternativa=None,
                    cidadeDestinoAlternativa=None,
                    rotaId=None,
                    rotaDto=Rota(
                        id=4931,
                        empresaDto=None,
                        cidadeOrigem=Cidade(
                            id=5500,
                            uf="SP",
                            descricaoUf="São Paulo",
                            nome="Guarulhos",
                            nomeComUf="Guarulhos - SP",
                            nomeComUfNormalizado="guarulhos-sp",
                            codigoIbge="3518800",
                            codigoAntt="3561",
                            imagem=None,
                            urlImagem=None,
                        ),
                        cidadeDestino=Cidade(
                            id=4742,
                            uf="RS",
                            descricaoUf="Rio Grande do Sul",
                            nome="Porto Alegre",
                            nomeComUf="Porto Alegre - RS",
                            nomeComUfNormalizado="portoalegre-rs",
                            codigoIbge="4314902",
                            codigoAntt="63",
                            imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                            urlImagem=None,
                        ),
                        trechosDto=[],
                        conexoes=[],
                        opcoesTrechoAlternativo=None,
                        prefixo="RSSP1544002",
                        delimitacao=None,
                        descricao=None,
                        nomeFantasiaEmpresa=None,
                        empresaId=None,
                    ),
                    ordem=1,
                    duracao="00:00",
                    pontoEmbarque="TERMINAL RODOVIÁRIO DE GUARULHOS",
                    plataformaEmbarque=None,
                    taxaEmbarque="0,00",
                    pedagio="0,00",
                    quilometragem="0,00",
                    habilitadoVenderSemConexao=False,
                ),
                Trecho(
                    id=52629,
                    idEmpresa=751,
                    cidadeDestino=Cidade(
                        id=5848,
                        uf="SP",
                        descricaoUf="São Paulo",
                        nome="São Paulo",
                        nomeComUf="São Paulo - SP",
                        nomeComUfNormalizado="saopaulo-sp",
                        codigoIbge="3550308",
                        codigoAntt="70",
                        imagem="Vexado passagem de ônibus para São Paulo-SP-4872.webp",
                        urlImagem=None,
                    ),
                    cidadeOrigem=None,
                    cidadeOrigemAlternativa=None,
                    cidadeDestinoAlternativa=None,
                    rotaId=None,
                    rotaDto=Rota(
                        id=4931,
                        empresaDto=None,
                        cidadeOrigem=Cidade(
                            id=5500,
                            uf="SP",
                            descricaoUf="São Paulo",
                            nome="Guarulhos",
                            nomeComUf="Guarulhos - SP",
                            nomeComUfNormalizado="guarulhos-sp",
                            codigoIbge="3518800",
                            codigoAntt="3561",
                            imagem=None,
                            urlImagem=None,
                        ),
                        cidadeDestino=Cidade(
                            id=4742,
                            uf="RS",
                            descricaoUf="Rio Grande do Sul",
                            nome="Porto Alegre",
                            nomeComUf="Porto Alegre - RS",
                            nomeComUfNormalizado="portoalegre-rs",
                            codigoIbge="4314902",
                            codigoAntt="63",
                            imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                            urlImagem=None,
                        ),
                        trechosDto=[],
                        conexoes=[],
                        opcoesTrechoAlternativo=None,
                        prefixo="RSSP1544002",
                        delimitacao=None,
                        descricao=None,
                        nomeFantasiaEmpresa=None,
                        empresaId=None,
                    ),
                    ordem=2,
                    duracao="01:00",
                    pontoEmbarque="TERMINAL RODOVIÁRIO BARRA FUNDA",
                    plataformaEmbarque=None,
                    taxaEmbarque="0,00",
                    pedagio="0,00",
                    quilometragem="21,00",
                    habilitadoVenderSemConexao=False,
                ),
                Trecho(
                    id=52630,
                    idEmpresa=751,
                    cidadeDestino=Cidade(
                        id=5673,
                        uf="SP",
                        descricaoUf="São Paulo",
                        nome="Osasco",
                        nomeComUf="Osasco - SP",
                        nomeComUfNormalizado="osasco-sp",
                        codigoIbge="3534401",
                        codigoAntt="1848",
                        imagem=None,
                        urlImagem=None,
                    ),
                    cidadeOrigem=None,
                    cidadeOrigemAlternativa=None,
                    cidadeDestinoAlternativa=None,
                    rotaId=None,
                    rotaDto=Rota(
                        id=4931,
                        empresaDto=None,
                        cidadeOrigem=Cidade(
                            id=5500,
                            uf="SP",
                            descricaoUf="São Paulo",
                            nome="Guarulhos",
                            nomeComUf="Guarulhos - SP",
                            nomeComUfNormalizado="guarulhos-sp",
                            codigoIbge="3518800",
                            codigoAntt="3561",
                            imagem=None,
                            urlImagem=None,
                        ),
                        cidadeDestino=Cidade(
                            id=4742,
                            uf="RS",
                            descricaoUf="Rio Grande do Sul",
                            nome="Porto Alegre",
                            nomeComUf="Porto Alegre - RS",
                            nomeComUfNormalizado="portoalegre-rs",
                            codigoIbge="4314902",
                            codigoAntt="63",
                            imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                            urlImagem=None,
                        ),
                        trechosDto=[],
                        conexoes=[],
                        opcoesTrechoAlternativo=None,
                        prefixo="RSSP1544002",
                        delimitacao=None,
                        descricao=None,
                        nomeFantasiaEmpresa=None,
                        empresaId=None,
                    ),
                    ordem=3,
                    duracao="00:30",
                    pontoEmbarque="TERMINAL RODOVIÁRIO DE OSASCO",
                    plataformaEmbarque=None,
                    taxaEmbarque="2,50",
                    pedagio="0,00",
                    quilometragem="15,00",
                    habilitadoVenderSemConexao=False,
                ),
            ],
            conexoes=[],
            opcoesTrechoAlternativo=None,
            prefixo="RSSP1544002",
            delimitacao="Guarulhos-SP x Porto Alegre-RS",
            descricao=None,
            nomeFantasiaEmpresa=None,
            empresaId=None,
        ),
        motoristas=[],
        usuariosEmpresaBloqueado=[],
        assentosDisponiveis=60,
        idEmpresa=751,
        nomeEmpresa="CATARINENSE AGENCIA DE VIAGENS LTDA",
        nomeFantasiaEmpresa="TURISSUL",
        poltronas=[],
        descricaoTipoVeiculo=None,
        quantidadeAssentos=None,
        preco=None,
        quilometragem="1402,00",
        tipoAjusteItinerario=TipoAjusteItinerario(
            descricao="Desconto", nameEnum="DESCONTO"
        ),
        valorAjuste="40.00",
        tipoPreco=None,
        andar=1,
        trechosBloqueados=[],
        cidadesBloqueados=[],
        trechosBloqueadosDesconto=[],
        habilitadoSite=True,
        assentosVendidos=None,
        sePodeEmbarcar=False,
        tipoConfiguracaoPreco=TipoConfiguracaoPreco(descricao="Normal", valor="NORMAL"),
        horaPartidaRegra=None,
        quantidadePoltronasRegra=None,
        ativo=True,
        prestador=None,
        complemento=None,
        tipoCategoria="MISTA",
        valorTipoPrecoPadrao="SEMI_LEITO_ESPECIAL_1",
        tipoPrecoPadrao=TipoPrecoPadrao(
            tipoPreco="SEMI_LEITO_ESPECIAL_1",
            descricaoTipoPreco="Semi Leito especial 1",
        ),
        passouTempoParaPartida=False,
        pricePorUsuario=[],
    )

    travel = InputTravel(
        ota_config_id=2,
        extra={
            "itinerario_id": 335852,
            "empresa_id": 388,
            "origem_id": 5848,
            "destino_id": 5673,
            "dataHoraPartida": "2025-04-16T20:00:00",
            "rota_id": 123,
            "trechoOrigemId": 4,
            "trechoDestinoId": 5,
            "tipoVeiculo": "SEMI_LEITO",
        },
    )
    checkpoints = await searcher.travel_itinerary(travel)

    assert checkpoints == [
        Checkpoint(
            name="São Paulo - SP",
            departure_at=datetime(2025, 4, 16, 20, 0),
            distance_km=0,
        ),
        Checkpoint(
            name="Osasco - SP",
            departure_at=datetime(2025, 4, 16, 20, 30),
            distance_km=15,
        ),
    ]


async def test_available_seats(searcher, mock_client):
    mock_client.seating_map.return_value = [
        Fileira(
            id=4,
            numero=4,
            exibeCorredor=False,
            assentos=[
                Assento(
                    id=36,
                    numero=3,
                    ordem=0,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214989,
                        numero=3,
                        reservada=False,
                        bloqueada=False,
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria=None,
                        preco="10.00",
                    ),
                ),
                Assento(
                    id=37,
                    numero=7,
                    ordem=1,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214990,
                        numero=7,
                        reservada=False,
                        bloqueada=True,
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria=None,
                        preco=None,
                    ),
                ),
            ],
        ),
        Fileira(
            id=3,
            numero=2,
            exibeCorredor=True,
            assentos=[
                Assento(
                    id=25,
                    numero=4,
                    ordem=0,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214978,
                        numero=4,
                        reservada=False,
                        bloqueada=True,
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria=None,
                        preco="10.00",
                    ),
                ),
                Assento(
                    id=26,
                    numero=8,
                    ordem=1,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214979,
                        numero=8,
                        reservada=False,
                        bloqueada=True,
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria=None,
                        preco="10.00",
                    ),
                ),
            ],
        ),
    ]
    travel = InputTravel(
        ota_config_id=2,
        extra={
            "itinerario_id": 335852,
            "empresa_id": 388,
            "origem_id": 5848,
            "destino_id": 5673,
            "dataHoraPartida": "2025-04-16T20:00:00",
            "rota_id": 123,
            "trechoOrigemId": 4,
            "trechoDestinoId": 5,
            "tipoVeiculo": "SEMI_LEITO",
        },
    )
    seat_map = await searcher.available_seats(travel)
    assert seat_map.base_price == 10.00
    assert seat_map.seats == [
        Seat(
            number="3",
            floor=1,
            row=1,
            column=5,
            available=True,
            category="SEMI_LEITO",
            seat_type="semi leito",
            benefits=[Benefit.NORMAL],
            price=10.00,
            extra={"id": 11214989},
        ),
        Seat(
            number="7",
            floor=1,
            row=2,
            column=5,
            available=False,
            category="SEMI_LEITO",
            seat_type="semi leito",
            benefits=[Benefit.NORMAL],
            price=None,
            extra={"id": 11214990},
        ),
        Seat(
            number="4",
            floor=1,
            row=1,
            column=2,
            available=False,
            category="SEMI_LEITO",
            seat_type="semi leito",
            benefits=[Benefit.NORMAL],
            price=10.00,
            extra={"id": 11214978},
        ),
        Seat(
            number="8",
            floor=1,
            row=2,
            column=2,
            available=False,
            category="SEMI_LEITO",
            seat_type="semi leito",
            benefits=[Benefit.NORMAL],
            price=10.00,
            extra={"id": 11214979},
        ),
    ]


async def test_block_seat(searcher, mock_client):
    mock_client.bloquear_poltrona.return_value = BloqueioResponse(
        id=1,
        poltronaId=10,
        uuid="uuid",
        preco="10.00",
        precoANTT="10.00",
        precoMeia="10.00",
        precoANTTMeia="10.00",
        taxaEmbarque="10.00",
        pedagio="10.00",
        tarifaSeguro="10.00",
        garantiaPrecoId=1,
        expiresAt=None,
    )
    travel = InputTravel(
        ota_config_id=2,
        extra={
            "itinerario_id": 335852,
            "origem_id": 5848,
            "destino_id": 5673,
            "rota_id": 123,
            "trechoOrigemId": 4,
            "trechoDestinoId": 5,
            "tipoVeiculo": "SEMI_LEITO",
        },
    )
    seat = Seat(
        number="3",
        floor=1,
        row=1,
        column=5,
        available=True,
        category="SEMI_LEITO",
        seat_type="semi leito",
        benefits=[Benefit.NORMAL],
        price=10.00,
        extra={"id": 11214989},
    )
    blocked_seat = await searcher.block_seat(travel, seat)
    assert blocked_seat == BlockedSeat(
        number="3",
        floor=1,
        row=1,
        column=5,
        available=True,
        category="SEMI_LEITO",
        seat_type="semi leito",
        benefits=[Benefit.NORMAL],
        price=10.00,
        extra={"id": 11214989},
        best_before=mock.ANY,
        block_key=1,
        extra_rodoviaria={"uuid": "uuid", "poltronaId": 10},
    )
    assert mock_client.bloquear_poltrona.call_args == mock.call(
        service_token=mock.ANY,
        poltrona_id=11214989,
        itinerario_id=335852,
        trecho_origem_id=4,
        trecho_destino_id=5,
    )


async def test_block_seat_seat_already_taken(searcher, mock_client):
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)
    mock_client.bloquear_poltrona.side_effect = SeatUnavailable
    travel = InputTravel(
        ota_config_id=2,
        extra={
            "itinerario_id": 335852,
            "origem_id": 5848,
            "destino_id": 5673,
            "rota_id": 123,
            "trechoOrigemId": 4,
            "trechoDestinoId": 5,
            "tipoVeiculo": "SEMI_LEITO",
        },
    )
    seat = Seat(
        number="3",
        floor=1,
        row=1,
        column=5,
        available=True,
        category="SEMI_LEITO",
        seat_type="semi leito",
        benefits=[Benefit.NORMAL],
        price=10.00,
        extra={"id": 11214989},
    )
    with pytest.raises(SeatUnavailable):
        await searcher.block_seat(travel, seat)


async def test_unblock_seat(searcher, mock_client):
    mock_client.liberar_poltrona.return_value = None
    travel = InputTravel(
        ota_config_id=2,
        extra={
            "itinerario_id": 335852,
            "origem_id": 5848,
            "destino_id": 5673,
            "rota_id": 123,
            "trechoOrigemId": 4,
            "trechoDestinoId": 5,
            "tipoVeiculo": "SEMI_LEITO",
        },
    )
    blocked_seat = BlockedSeat(
        number="3",
        floor=1,
        row=1,
        column=5,
        available=True,
        category="SEMI_LEITO",
        seat_type="semi leito",
        benefits=[Benefit.NORMAL],
        price=10.00,
        extra={"id": 11214989},
        best_before=mock.ANY,
        block_key=1,
    )
    result = await searcher.unblock_seat(travel, blocked_seat)
    assert result is True
