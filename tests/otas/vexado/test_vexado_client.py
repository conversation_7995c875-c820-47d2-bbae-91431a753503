from datetime import date

import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.exception import SeatUnavailable, TravelUnavailable
from marketplace.otas.vexado.client import (
    Assento,
    BloqueioResponse,
    Cidade,
    CidadesConexao,
    Fileira,
    ImagemVeiculo,
    ItinerarioResponse,
    MapaVeiculoDto,
    Passagem,
    Poltrona,
    Rota,
    SearchResponse,
    ServiceToken,
    TipoAjusteItinerario,
    TipoConfiguracaoPreco,
    TipoPrecoPadrao,
    TipoVeiculo,
    Trecho,
    VeiculoDto,
    VendaGratuidade,
    VexadoClient,
)


@pytest.fixture
def client():
    return VexadoClient(base_url="https://vexado.test")


async def test_buscar_corrida(httpx_mock: HTTPXMock, client: VexadoClient):
    origin_id = 1
    destination_id = 2
    date_travel = date(2025, 4, 20)
    httpx_mock.add_response(
        method="GET",
        url=f"https://vexado.test/passagem/origem/{origin_id}/destino/{destination_id}/dataIda/{date_travel.isoformat()}",
        json={
            "passagensIda": [
                {
                    "idItinerario": 335856,
                    "dataPartida": "16/abr.",
                    "horaPartida": "20:00",
                    "dataHoraPartida": "2025-04-16T20:00:00",
                    "dataHoraChegada": "2025-04-17T04:27:00",
                    "dataChegada": "17/abr.",
                    "horaChegada": "04:27",
                    "tipoVeiculo": "SEMI_LEITO",
                    "descricaoTipoVeiculo": "Semi Leito",
                    "assentosDisponiveis": 3,
                    "idEmpresa": 388,
                    "nomeEmpresa": "VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
                    "preco": "152.00",
                    "precoMeia": "76.00",
                    "embarque": "Terminal Rodoviário",
                    "andar": 1,
                    "taxaEmbarque": "7.17",
                    "pedagio": "0.00",
                    "tarifaSeguro": "0.00",
                    "precoANTT": "199.00",
                    "precoANTTMeia": "99.50",
                    "descRota": "Brasília à Angra dos Reis",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "PLACA RMH7J37 26 POLTRONAS ",
                    "veiculoId": 3696,
                    "duracao": "8h 27m ",
                    "qtdDias": 0,
                    "trechoOrigemId": 29747,
                    "trechoDestinoId": 29753,
                    "idRota": 2426,
                    "seAplicaAjuste": False,
                    "nomePrestador": None,
                    "seTemConexao": False,
                    "nomesCidadesComConexao": [],
                    "cidadesComConexao": [],
                    "empresaHabilitadoValorAntt": False,
                    "usuarioEhHabilitadoVenderValorANTT": False,
                    "tipoCategoriaItinerario": "UNICA",
                    "latitudeOrigem": None,
                    "longitudeOrigem": None,
                    "latitudeDestino": None,
                    "longitudeDestino": None,
                    "nota": 4.8,
                    "pontoEmbarque": "Terminal Rodoviário",
                    "pontoDesembarque": "Terminal Rodoviário",
                    "integracao": False,
                    "fareClass": None,
                    "idFareClass": None,
                    "idEstacaoOrigem": None,
                    "idEstacaoDestino": None,
                    "codeEmpresa": None,
                    "api": "VEXADO",
                    "acrescimoMaximoPassagem": "00.00",
                    "valorAcrescimoMaximoPassagem": "0.00",
                    "electronicTicketAvailable": False,
                    "imagemVeiculo": {
                        "id": 21,
                        "nome": "WhatsApp Image 2020-02-03 at 17.27.44.jpeg",
                        "imagem": "83d2379d-ae24-4073-aa86-fe2e3642068d",
                        "principal": True,
                        "urlImagem": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/83d2379d-ae24-4073-aa86-fe2e3642068d",
                        "tipoVeiculo": "ONIBUS",
                    },
                    "tipoImagemVeiculo": "ONIBUS",
                    "vendaGratuidade": {
                        "totalIdJovem100": 0,
                        "totalIdoso100": 0,
                        "totalIdJovem50": 0,
                        "totalIdoso50": 0,
                        "totalDeficiente": 0,
                        "vendeGratuidade": False,
                    },
                    "percentualDescontoPrice": None,
                    "urlImagemEmpresa": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/0da338c4-3b9c-4e71-9504-61087bc01f09",
                    "valorSemDescontoAntt": "199.00",
                    "valorSemDesconto": "152.00",
                }
            ],
            "passagensRetorno": [],
            "datasIdaProximas": None,
            "datasRetornoProximas": None,
            "origem": None,
            "destino": None,
        },
    )
    service_token = ServiceToken(
        token=(
            "eyJhbGciOiJIUzUxMiJ9.eyJudXMiOiJNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJub21lVXN1YXJpb0NvbU51bWVyby"
            "I6IjE0MDAgLSBNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJzdWIiOiIxNDAwIiwiaWF0IjoxNzM2OTY2MDkxLCJleHAiO"
            "jE3MzY5ODc2OTF9.s93imRGqxeitXGzna3mT97x3R6_5feZEKhHzK5l526vPRkitXV72T0CxK1qPJaOyLwxItSAeei3T1KxWe94vdQ"
        ),
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    search_response = await client.search(
        service_token=service_token,
        origem_id=origin_id,
        destino_id=destination_id,
        data_ida=date_travel,
    )

    assert search_response == SearchResponse(
        passagensIda=[
            Passagem(
                idItinerario=335856,
                dataPartida="16/abr.",
                horaPartida="20:00",
                dataHoraPartida="2025-04-16T20:00:00",
                dataHoraChegada="2025-04-17T04:27:00",
                dataChegada="17/abr.",
                horaChegada="04:27",
                tipoVeiculo="SEMI_LEITO",
                descricaoTipoVeiculo="Semi Leito",
                assentosDisponiveis=3,
                idEmpresa=388,
                nomeEmpresa="VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
                preco="152.00",
                precoMeia="76.00",
                embarque="Terminal Rodoviário",
                andar=1,
                taxaEmbarque="7.17",
                pedagio="0.00",
                tarifaSeguro="0.00",
                precoANTT="199.00",
                precoANTTMeia="99.50",
                descRota="Brasília à Angra dos Reis",
                descontoMaximoUsuarioRota=None,
                veiculo="PLACA RMH7J37 26 POLTRONAS ",
                veiculoId=3696,
                duracao="8h 27m ",
                qtdDias=0,
                trechoOrigemId=29747,
                trechoDestinoId=29753,
                idRota=2426,
                seAplicaAjuste=False,
                nomePrestador=None,
                seTemConexao=False,
                nomesCidadesComConexao=[],
                cidadesComConexao=[],
                empresaHabilitadoValorAntt=False,
                usuarioEhHabilitadoVenderValorANTT=False,
                tipoCategoriaItinerario="UNICA",
                latitudeOrigem=None,
                longitudeOrigem=None,
                latitudeDestino=None,
                longitudeDestino=None,
                nota=4.8,
                pontoEmbarque="Terminal Rodoviário",
                pontoDesembarque="Terminal Rodoviário",
                integracao=False,
                fareClass=None,
                idFareClass=None,
                idEstacaoOrigem=None,
                idEstacaoDestino=None,
                codeEmpresa=None,
                api="VEXADO",
                acrescimoMaximoPassagem="00.00",
                valorAcrescimoMaximoPassagem="0.00",
                electronicTicketAvailable=False,
                imagemVeiculo=ImagemVeiculo(
                    id=21,
                    nome="WhatsApp Image 2020-02-03 at 17.27.44.jpeg",
                    imagem="83d2379d-ae24-4073-aa86-fe2e3642068d",
                    principal=True,
                    urlImagem="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/83d2379d-ae24-4073-aa86-fe2e3642068d",
                    tipoVeiculo="ONIBUS",
                ),
                tipoImagemVeiculo="ONIBUS",
                vendaGratuidade=VendaGratuidade(
                    totalIdJovem100=0,
                    totalIdoso100=0,
                    totalIdJovem50=0,
                    totalIdoso50=0,
                    totalDeficiente=0,
                    vendeGratuidade=False,
                ),
                percentualDescontoPrice=None,
                valorSemDesconto="152.00",
                valorSemDescontoAntt="199.00",
                urlImagemEmpresa="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/0da338c4-3b9c-4e71-9504-61087bc01f09",
            )
        ],
        passagensRetorno=[],
        datasIdaProximas=None,
        datasRetornoProximas=None,
        origem=None,
        destino=None,
    )


async def test_buscar_corrida_conexao(httpx_mock: HTTPXMock, client: VexadoClient):
    origin_id = 1
    destination_id = 2
    date_travel = date(2025, 4, 20)
    httpx_mock.add_response(
        method="GET",
        url=f"https://vexado.test/passagem/origem/{origin_id}/destino/{destination_id}/dataIda/{date_travel.isoformat()}",
        json={
            "passagensIda": [
                {
                    "idItinerario": 335797,
                    "dataPartida": "30/mai.",
                    "horaPartida": "18:00",
                    "dataHoraPartida": "2025-05-30T18:00:00",
                    "dataHoraChegada": "2025-05-31T06:30:00",
                    "dataChegada": "31/mai.",
                    "horaChegada": "06:30",
                    "tipoVeiculo": "LEITO_INDIVIDUAL",
                    "descricaoTipoVeiculo": "Leito Individual",
                    "assentosDisponiveis": 13,
                    "idEmpresa": 751,
                    "nomeEmpresa": "CATARINENSE AGENCIA DE VIAGENS LTDA",
                    "preco": "219.00",
                    "precoMeia": "109.50",
                    "embarque": "TERMINAL RODOVIÁRIO BARRA FUNDA",
                    "andar": 1,
                    "taxaEmbarque": "0.00",
                    "pedagio": "0.00",
                    "tarifaSeguro": "0.00",
                    "precoANTT": "199.00",
                    "precoANTTMeia": "99.50",
                    "descRota": "Guarulhos à Porto Alegre",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "12017 - QHY5F80",
                    "veiculoId": 3776,
                    "duracao": "12h 30m ",
                    "qtdDias": 0,
                    "trechoOrigemId": 52629,
                    "trechoDestinoId": 52654,
                    "idRota": 4931,
                    "seAplicaAjuste": False,
                    "nomePrestador": None,
                    "seTemConexao": True,
                    "nomesCidadesComConexao": ["Agudos do Sul - PR"],
                    "cidadesComConexao": [
                        {
                            "idCidadeOrigem": 5500,
                            "idCidadeDestino": 4742,
                            "nomeCidade": "Agudos do Sul - PR",
                        }
                    ],
                    "empresaHabilitadoValorAntt": False,
                    "usuarioEhHabilitadoVenderValorANTT": False,
                    "tipoCategoriaItinerario": "MISTA",
                    "latitudeOrigem": None,
                    "longitudeOrigem": None,
                    "latitudeDestino": None,
                    "longitudeDestino": None,
                    "nota": 4.7,
                    "pontoEmbarque": "TERMINAL RODOVIÁRIO BARRA FUNDA",
                    "pontoDesembarque": "TERMINAL RODOVIÁRIO DE FLORIANÓPOLIS",
                    "integracao": False,
                    "fareClass": None,
                    "idFareClass": None,
                    "idEstacaoOrigem": None,
                    "idEstacaoDestino": None,
                    "codeEmpresa": None,
                    "api": "VEXADO",
                    "acrescimoMaximoPassagem": "00.00",
                    "valorAcrescimoMaximoPassagem": "0.00",
                    "electronicTicketAvailable": False,
                    "imagemVeiculo": {
                        "id": 21,
                        "nome": "WhatsApp Image 2020-02-03 at 17.27.44.jpeg",
                        "imagem": "83d2379d-ae24-4073-aa86-fe2e3642068d",
                        "principal": True,
                        "urlImagem": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/83d2379d-ae24-4073-aa86-fe2e3642068d",
                        "tipoVeiculo": "ONIBUS",
                    },
                    "tipoImagemVeiculo": "ONIBUS",
                    "vendaGratuidade": {
                        "totalIdJovem100": 0,
                        "totalIdoso100": 0,
                        "totalIdJovem50": 0,
                        "totalIdoso50": 0,
                        "totalDeficiente": 0,
                        "vendeGratuidade": False,
                    },
                    "percentualDescontoPrice": None,
                    "valorSemDesconto": "219.00",
                    "urlImagemEmpresa": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/turissul (2)-5152.webp",
                    "valorSemDescontoAntt": "199.00",
                }
            ],
            "passagensRetorno": [],
            "datasIdaProximas": None,
            "datasRetornoProximas": None,
            "origem": None,
            "destino": None,
        },
    )
    service_token = ServiceToken(
        token=(
            "eyJhbGciOiJIUzUxMiJ9.eyJudXMiOiJNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJub21lVXN1YXJpb0NvbU51bWVyby"
            "I6IjE0MDAgLSBNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJzdWIiOiIxNDAwIiwiaWF0IjoxNzM2OTY2MDkxLCJleHAiO"
            "jE3MzY5ODc2OTF9.s93imRGqxeitXGzna3mT97x3R6_5feZEKhHzK5l526vPRkitXV72T0CxK1qPJaOyLwxItSAeei3T1KxWe94vdQ"
        ),
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    search_response = await client.search(
        service_token=service_token,
        origem_id=origin_id,
        destino_id=destination_id,
        data_ida=date_travel,
    )

    assert search_response == SearchResponse(
        passagensIda=[
            Passagem(
                idItinerario=335797,
                dataPartida="30/mai.",
                horaPartida="18:00",
                dataHoraPartida="2025-05-30T18:00:00",
                dataHoraChegada="2025-05-31T06:30:00",
                dataChegada="31/mai.",
                horaChegada="06:30",
                tipoVeiculo="LEITO_INDIVIDUAL",
                descricaoTipoVeiculo="Leito Individual",
                assentosDisponiveis=13,
                idEmpresa=751,
                nomeEmpresa="CATARINENSE AGENCIA DE VIAGENS LTDA",
                preco="219.00",
                precoMeia="109.50",
                embarque="TERMINAL RODOVIÁRIO BARRA FUNDA",
                andar=1,
                taxaEmbarque="0.00",
                pedagio="0.00",
                tarifaSeguro="0.00",
                precoANTT="199.00",
                precoANTTMeia="99.50",
                descRota="Guarulhos à Porto Alegre",
                descontoMaximoUsuarioRota=None,
                veiculo="12017 - QHY5F80",
                veiculoId=3776,
                duracao="12h 30m ",
                qtdDias=0,
                trechoOrigemId=52629,
                trechoDestinoId=52654,
                idRota=4931,
                seAplicaAjuste=False,
                nomePrestador=None,
                seTemConexao=True,
                nomesCidadesComConexao=["Agudos do Sul - PR"],
                cidadesComConexao=[
                    CidadesConexao(
                        idCidadeOrigem=5500,
                        idCidadeDestino=4742,
                        nomeCidade="Agudos do Sul - PR",
                    )
                ],
                empresaHabilitadoValorAntt=False,
                usuarioEhHabilitadoVenderValorANTT=False,
                tipoCategoriaItinerario="MISTA",
                latitudeOrigem=None,
                longitudeOrigem=None,
                latitudeDestino=None,
                longitudeDestino=None,
                nota=4.7,
                pontoEmbarque="TERMINAL RODOVIÁRIO BARRA FUNDA",
                pontoDesembarque="TERMINAL RODOVIÁRIO DE FLORIANÓPOLIS",
                integracao=False,
                fareClass=None,
                idFareClass=None,
                idEstacaoOrigem=None,
                idEstacaoDestino=None,
                codeEmpresa=None,
                api="VEXADO",
                acrescimoMaximoPassagem="00.00",
                valorAcrescimoMaximoPassagem="0.00",
                electronicTicketAvailable=False,
                imagemVeiculo=ImagemVeiculo(
                    id=21,
                    nome="WhatsApp Image 2020-02-03 at 17.27.44.jpeg",
                    imagem="83d2379d-ae24-4073-aa86-fe2e3642068d",
                    principal=True,
                    urlImagem="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/83d2379d-ae24-4073-aa86-fe2e3642068d",
                    tipoVeiculo="ONIBUS",
                ),
                tipoImagemVeiculo="ONIBUS",
                vendaGratuidade=VendaGratuidade(
                    totalIdJovem100=0,
                    totalIdoso100=0,
                    totalIdJovem50=0,
                    totalIdoso50=0,
                    totalDeficiente=0,
                    vendeGratuidade=False,
                ),
                percentualDescontoPrice=None,
                valorSemDesconto="219.00",
                valorSemDescontoAntt="199.00",
                urlImagemEmpresa="https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/turissul (2)-5152.webp",
            )
        ],
        passagensRetorno=[],
        datasIdaProximas=None,
        datasRetornoProximas=None,
        origem=None,
        destino=None,
    )


async def test_list_cities_by_company(httpx_mock: HTTPXMock, client: VexadoClient):
    company_id = 893
    httpx_mock.add_response(
        method="GET",
        url=f"https://vexado.test/public/cidades/empresa/{company_id}/buscar-cidades",
        json=[
            {
                "id": 155,
                "uf": "GO",
                "descricaoUf": "Goiás",
                "nome": "Itumbiara",
                "nomeComUf": "Itumbiara - GO",
                "nomeComUfNormalizado": "itumbiara-go",
                "codigoIbge": "5211503",
                "codigoAntt": "2379",
                "imagem": "b6e2874a-470a-4f57-907c-402d0841d22d",
                "urlImagem": None,
            },
            {
                "id": 5828,
                "uf": "SP",
                "descricaoUf": "São Paulo",
                "nome": "Santos",
                "nomeComUf": "Santos - SP",
                "nomeComUfNormalizado": "santos-sp",
                "codigoIbge": "3548500",
                "codigoAntt": "2130",
                "imagem": None,
                "urlImagem": None,
            },
        ],
    )
    service_token = ServiceToken(
        token=(
            "eyJhbGciOiJIUzUxMiJ9.eyJudXMiOiJNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJub21lVXN1YXJpb0NvbU51bWVyby"
            "I6IjE0MDAgLSBNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJzdWIiOiIxNDAwIiwiaWF0IjoxNzM2OTY2MDkxLCJleHAiO"
            "jE3MzY5ODc2OTF9.s93imRGqxeitXGzna3mT97x3R6_5feZEKhHzK5l526vPRkitXV72T0CxK1qPJaOyLwxItSAeei3T1KxWe94vdQ"
        ),
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    cities_response = await client.list_cities_by_company(
        service_token=service_token, company_id=company_id
    )

    assert cities_response == [
        Cidade(
            id=155,
            uf="GO",
            descricaoUf="Goiás",
            nome="Itumbiara",
            nomeComUf="Itumbiara - GO",
            nomeComUfNormalizado="itumbiara-go",
            codigoIbge="5211503",
            codigoAntt="2379",
            imagem="b6e2874a-470a-4f57-907c-402d0841d22d",
            urlImagem=None,
        ),
        Cidade(
            id=5828,
            uf="SP",
            descricaoUf="São Paulo",
            nome="Santos",
            nomeComUf="Santos - SP",
            nomeComUfNormalizado="santos-sp",
            codigoIbge="3548500",
            codigoAntt="2130",
            imagem=None,
            urlImagem=None,
        ),
    ]


async def test_itinerary(httpx_mock: HTTPXMock, client: VexadoClient):
    company_id = 893
    itinerary_id = 829
    httpx_mock.add_response(
        method="GET",
        url=f"https://vexado.test/itinerario/{itinerary_id}/empresa/{company_id}",
        json={
            "id": 342345,
            "dataPartida": "2025-06-03",
            "horaSaida": "17:00",
            "dataHoraPartida": "03/06/2025 17:00",
            "dataHoraChegada": "04/06/2025 14:15",
            "veiculoId": None,
            "veiculoDto": {
                "id": 3781,
                "descricao": "12016 - QKH9I47",
                "empresaDto": None,
                "mapaVeiculoDto": {
                    "id": 249,
                    "fileirasPrimeiroAndar": [],
                    "fileirasSegundoAndar": [],
                    "seDoisAndares": False,
                    "quantidadeAssentos": 60,
                    "modelo": "DD 60 LUGARES PISO SUPERIOR 48 Poltronas e PISO INFERIOR 12 Poltronas",
                    "tipoVeiculo": "ONIBUS",
                },
                "idRastreamento": None,
                "seDoisAndares": False,
                "imagens": None,
                "placa": "QKH9I47",
                "modelo": "Marcopolo Paradiso DD",
                "tipoVeiculo": {"chave": "ONIBUS", "valor": "ônibus"},
                "quilometragem": None,
                "chassi": None,
                "renavam": "0110531996-0",
                "anoFabricacao": "2016",
                "numeroEixo": 3,
                "dataVencimentoCsv": "26/09/2025",
                "exibirDetalhe": False,
                "tipoServico": [],
            },
            "rotaId": None,
            "rotaDto": {
                "id": 4931,
                "empresaDto": None,
                "cidadeOrigem": {
                    "id": 5500,
                    "uf": "SP",
                    "descricaoUf": "São Paulo",
                    "nome": "Guarulhos",
                    "nomeComUf": "Guarulhos - SP",
                    "nomeComUfNormalizado": "guarulhos-sp",
                    "codigoIbge": "3518800",
                    "codigoAntt": "3561",
                    "imagem": None,
                    "urlImagem": None,
                },
                "cidadeDestino": {
                    "id": 4742,
                    "uf": "RS",
                    "descricaoUf": "Rio Grande do Sul",
                    "nome": "Porto Alegre",
                    "nomeComUf": "Porto Alegre - RS",
                    "nomeComUfNormalizado": "portoalegre-rs",
                    "codigoIbge": "4314902",
                    "codigoAntt": "63",
                    "imagem": "Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                    "urlImagem": None,
                },
                "trechosDto": [
                    {
                        "id": 52628,
                        "idEmpresa": 751,
                        "cidadeDestino": {
                            "id": 5500,
                            "uf": "SP",
                            "descricaoUf": "São Paulo",
                            "nome": "Guarulhos",
                            "nomeComUf": "Guarulhos - SP",
                            "nomeComUfNormalizado": "guarulhos-sp",
                            "codigoIbge": "3518800",
                            "codigoAntt": "3561",
                            "imagem": None,
                            "urlImagem": None,
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaId": None,
                        "rotaDto": {
                            "id": 4931,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 5500,
                                "uf": "SP",
                                "descricaoUf": "São Paulo",
                                "nome": "Guarulhos",
                                "nomeComUf": "Guarulhos - SP",
                                "nomeComUfNormalizado": "guarulhos-sp",
                                "codigoIbge": "3518800",
                                "codigoAntt": "3561",
                                "imagem": None,
                                "urlImagem": None,
                            },
                            "cidadeDestino": {
                                "id": 4742,
                                "uf": "RS",
                                "descricaoUf": "Rio Grande do Sul",
                                "nome": "Porto Alegre",
                                "nomeComUf": "Porto Alegre - RS",
                                "nomeComUfNormalizado": "portoalegre-rs",
                                "codigoIbge": "4314902",
                                "codigoAntt": "63",
                                "imagem": "Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                                "urlImagem": None,
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": "RSSP1544002",
                            "delimitacao": None,
                            "descricao": None,
                            "nomeFantasiaEmpresa": None,
                            "empresaId": None,
                        },
                        "ordem": 1,
                        "duracao": "00:00",
                        "pontoEmbarque": "TERMINAL RODOVIÁRIO DE GUARULHOS",
                        "plataformaEmbarque": None,
                        "taxaEmbarque": "0,00",
                        "pedagio": "0,00",
                        "configuracaoBilhete": [],
                        "quilometragem": "0,00",
                        "conexaoId": None,
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                        "latitude": None,
                        "longitude": None,
                        "habilitadoVenderSemConexao": False,
                        "configuracaoBilheteId": None,
                        "nomeInstalacao": None,
                        "codigoInstalacao": "SP00116",
                        "conexaoAtivaTrechoDestino": [],
                        "cidadesOrigensVendaDireta": [],
                    },
                    {
                        "id": 52629,
                        "idEmpresa": 751,
                        "cidadeDestino": {
                            "id": 5848,
                            "uf": "SP",
                            "descricaoUf": "São Paulo",
                            "nome": "São Paulo",
                            "nomeComUf": "São Paulo - SP",
                            "nomeComUfNormalizado": "saopaulo-sp",
                            "codigoIbge": "3550308",
                            "codigoAntt": "70",
                            "imagem": "Vexado passagem de ônibus para São Paulo-SP-4872.webp",
                            "urlImagem": None,
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaId": None,
                        "rotaDto": {
                            "id": 4931,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 5500,
                                "uf": "SP",
                                "descricaoUf": "São Paulo",
                                "nome": "Guarulhos",
                                "nomeComUf": "Guarulhos - SP",
                                "nomeComUfNormalizado": "guarulhos-sp",
                                "codigoIbge": "3518800",
                                "codigoAntt": "3561",
                                "imagem": None,
                                "urlImagem": None,
                            },
                            "cidadeDestino": {
                                "id": 4742,
                                "uf": "RS",
                                "descricaoUf": "Rio Grande do Sul",
                                "nome": "Porto Alegre",
                                "nomeComUf": "Porto Alegre - RS",
                                "nomeComUfNormalizado": "portoalegre-rs",
                                "codigoIbge": "4314902",
                                "codigoAntt": "63",
                                "imagem": "Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                                "urlImagem": None,
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": "RSSP1544002",
                            "delimitacao": None,
                            "descricao": None,
                            "nomeFantasiaEmpresa": None,
                            "empresaId": None,
                        },
                        "ordem": 2,
                        "duracao": "01:00",
                        "pontoEmbarque": "TERMINAL RODOVIÁRIO BARRA FUNDA",
                        "plataformaEmbarque": None,
                        "taxaEmbarque": "0,00",
                        "pedagio": "0,00",
                        "configuracaoBilhete": [],
                        "quilometragem": "21,00",
                        "conexaoId": None,
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                        "latitude": None,
                        "longitude": None,
                        "habilitadoVenderSemConexao": False,
                        "configuracaoBilheteId": None,
                        "nomeInstalacao": "TERMINAL RODOVIÁRIO BARRA FUNDA",
                        "codigoInstalacao": "SP00033",
                        "conexaoAtivaTrechoDestino": [],
                        "cidadesOrigensVendaDireta": [],
                    },
                    {
                        "id": 52630,
                        "idEmpresa": 751,
                        "cidadeDestino": {
                            "id": 5673,
                            "uf": "SP",
                            "descricaoUf": "São Paulo",
                            "nome": "Osasco",
                            "nomeComUf": "Osasco - SP",
                            "nomeComUfNormalizado": "osasco-sp",
                            "codigoIbge": "3534401",
                            "codigoAntt": "1848",
                            "imagem": None,
                            "urlImagem": None,
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaId": None,
                        "rotaDto": {
                            "id": 4931,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 5500,
                                "uf": "SP",
                                "descricaoUf": "São Paulo",
                                "nome": "Guarulhos",
                                "nomeComUf": "Guarulhos - SP",
                                "nomeComUfNormalizado": "guarulhos-sp",
                                "codigoIbge": "3518800",
                                "codigoAntt": "3561",
                                "imagem": None,
                                "urlImagem": None,
                            },
                            "cidadeDestino": {
                                "id": 4742,
                                "uf": "RS",
                                "descricaoUf": "Rio Grande do Sul",
                                "nome": "Porto Alegre",
                                "nomeComUf": "Porto Alegre - RS",
                                "nomeComUfNormalizado": "portoalegre-rs",
                                "codigoIbge": "4314902",
                                "codigoAntt": "63",
                                "imagem": "Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                                "urlImagem": None,
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": "RSSP1544002",
                            "delimitacao": None,
                            "descricao": None,
                            "nomeFantasiaEmpresa": None,
                            "empresaId": None,
                        },
                        "ordem": 3,
                        "duracao": "00:30",
                        "pontoEmbarque": "TERMINAL RODOVIÁRIO DE OSASCO",
                        "plataformaEmbarque": None,
                        "taxaEmbarque": "2,50",
                        "pedagio": "0,00",
                        "configuracaoBilhete": [],
                        "quilometragem": "15,00",
                        "conexaoId": None,
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                        "latitude": None,
                        "longitude": None,
                        "habilitadoVenderSemConexao": False,
                        "configuracaoBilheteId": None,
                        "nomeInstalacao": "TERMINAL RODOVIÁRIO DE OSASCO",
                        "codigoInstalacao": "SP00567",
                        "conexaoAtivaTrechoDestino": [],
                        "cidadesOrigensVendaDireta": [],
                    },
                ],
                "conexoes": [],
                "opcoesTrechoAlternativo": None,
                "prefixo": "RSSP1544002",
                "delimitacao": "Guarulhos-SP x Porto Alegre-RS",
                "descricao": None,
                "nomeFantasiaEmpresa": None,
                "empresaId": None,
            },
            "motoristas": [],
            "usuariosEmpresaBloqueado": [],
            "assentosDisponiveis": 60,
            "idEmpresa": 751,
            "nomeEmpresa": "CATARINENSE AGENCIA DE VIAGENS LTDA",
            "nomeFantasiaEmpresa": "TURISSUL",
            "poltronas": [],
            "descricaoTipoVeiculo": None,
            "quantidadeAssentos": None,
            "preco": None,
            "quilometragem": "1402,00",
            "tipoAjusteItinerario": {"descricao": "Desconto", "nameEnum": "DESCONTO"},
            "valorAjuste": "40.00",
            "tipoPreco": None,
            "andar": 1,
            "trechosBloqueados": [],
            "cidadesBloqueados": [],
            "trechosBloqueadosDesconto": [],
            "habilitadoSite": True,
            "assentosVendidos": None,
            "sePodeEmbarcar": False,
            "tipoConfiguracaoPreco": {"descricao": "Normal", "valor": "NORMAL"},
            "horaPartidaRegra": None,
            "quantidadePoltronasRegra": None,
            "ativo": True,
            "prestador": None,
            "complemento": None,
            "tipoCategoria": "MISTA",
            "valorTipoPrecoPadrao": "SEMI_LEITO_ESPECIAL_1",
            "tipoPrecoPadrao": {
                "tipoPreco": "SEMI_LEITO_ESPECIAL_1",
                "descricaoTipoPreco": "Semi Leito especial 1",
            },
            "passouTempoParaPartida": False,
            "pricePorUsuario": [],
        },
    )
    service_token = ServiceToken(
        token=(
            "eyJhbGciOiJIUzUxMiJ9.eyJudXMiOiJNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJub21lVXN1YXJpb0NvbU51bWVyby"
            "I6IjE0MDAgLSBNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJzdWIiOiIxNDAwIiwiaWF0IjoxNzM2OTY2MDkxLCJleHAiO"
            "jE3MzY5ODc2OTF9.s93imRGqxeitXGzna3mT97x3R6_5feZEKhHzK5l526vPRkitXV72T0CxK1qPJaOyLwxItSAeei3T1KxWe94vdQ"
        ),
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    itinerary_response = await client.itinerary(
        service_token=service_token, company_id=company_id, itinerario_id=itinerary_id
    )

    assert itinerary_response == ItinerarioResponse(
        id=342345,
        dataPartida="2025-06-03",
        horaSaida="17:00",
        dataHoraPartida="03/06/2025 17:00",
        dataHoraChegada="04/06/2025 14:15",
        veiculoId=None,
        veiculoDto=VeiculoDto(
            id=3781,
            descricao="12016 - QKH9I47",
            empresaDto=None,
            mapaVeiculoDto=MapaVeiculoDto(
                id=249,
                fileirasPrimeiroAndar=[],
                fileirasSegundoAndar=[],
                seDoisAndares=False,
                quantidadeAssentos=60,
                modelo="DD 60 LUGARES PISO SUPERIOR 48 Poltronas e PISO INFERIOR 12 Poltronas",
                tipoVeiculo="ONIBUS",
            ),
            idRastreamento=None,
            seDoisAndares=False,
            imagens=None,
            placa="QKH9I47",
            modelo="Marcopolo Paradiso DD",
            tipoVeiculo=TipoVeiculo(chave="ONIBUS", valor="ônibus"),
            quilometragem=None,
            chassi=None,
            renavam="0110531996-0",
            anoFabricacao="2016",
            numeroEixo=3,
            dataVencimentoCsv="26/09/2025",
            exibirDetalhe=False,
            tipoServico=[],
        ),
        rotaId=None,
        rotaDto=Rota(
            id=4931,
            empresaDto=None,
            cidadeOrigem=Cidade(
                id=5500,
                uf="SP",
                descricaoUf="São Paulo",
                nome="Guarulhos",
                nomeComUf="Guarulhos - SP",
                nomeComUfNormalizado="guarulhos-sp",
                codigoIbge="3518800",
                codigoAntt="3561",
                imagem=None,
                urlImagem=None,
            ),
            cidadeDestino=Cidade(
                id=4742,
                uf="RS",
                descricaoUf="Rio Grande do Sul",
                nome="Porto Alegre",
                nomeComUf="Porto Alegre - RS",
                nomeComUfNormalizado="portoalegre-rs",
                codigoIbge="4314902",
                codigoAntt="63",
                imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                urlImagem=None,
            ),
            trechosDto=[
                Trecho(
                    id=52628,
                    idEmpresa=751,
                    cidadeDestino=Cidade(
                        id=5500,
                        uf="SP",
                        descricaoUf="São Paulo",
                        nome="Guarulhos",
                        nomeComUf="Guarulhos - SP",
                        nomeComUfNormalizado="guarulhos-sp",
                        codigoIbge="3518800",
                        codigoAntt="3561",
                        imagem=None,
                        urlImagem=None,
                    ),
                    cidadeOrigem=None,
                    cidadeOrigemAlternativa=None,
                    cidadeDestinoAlternativa=None,
                    rotaId=None,
                    rotaDto=Rota(
                        id=4931,
                        empresaDto=None,
                        cidadeOrigem=Cidade(
                            id=5500,
                            uf="SP",
                            descricaoUf="São Paulo",
                            nome="Guarulhos",
                            nomeComUf="Guarulhos - SP",
                            nomeComUfNormalizado="guarulhos-sp",
                            codigoIbge="3518800",
                            codigoAntt="3561",
                            imagem=None,
                            urlImagem=None,
                        ),
                        cidadeDestino=Cidade(
                            id=4742,
                            uf="RS",
                            descricaoUf="Rio Grande do Sul",
                            nome="Porto Alegre",
                            nomeComUf="Porto Alegre - RS",
                            nomeComUfNormalizado="portoalegre-rs",
                            codigoIbge="4314902",
                            codigoAntt="63",
                            imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                            urlImagem=None,
                        ),
                        trechosDto=[],
                        conexoes=[],
                        opcoesTrechoAlternativo=None,
                        prefixo="RSSP1544002",
                        delimitacao=None,
                        descricao=None,
                        nomeFantasiaEmpresa=None,
                        empresaId=None,
                    ),
                    ordem=1,
                    duracao="00:00",
                    pontoEmbarque="TERMINAL RODOVIÁRIO DE GUARULHOS",
                    plataformaEmbarque=None,
                    taxaEmbarque="0,00",
                    pedagio="0,00",
                    quilometragem="0,00",
                    habilitadoVenderSemConexao=False,
                ),
                Trecho(
                    id=52629,
                    idEmpresa=751,
                    cidadeDestino=Cidade(
                        id=5848,
                        uf="SP",
                        descricaoUf="São Paulo",
                        nome="São Paulo",
                        nomeComUf="São Paulo - SP",
                        nomeComUfNormalizado="saopaulo-sp",
                        codigoIbge="3550308",
                        codigoAntt="70",
                        imagem="Vexado passagem de ônibus para São Paulo-SP-4872.webp",
                        urlImagem=None,
                    ),
                    cidadeOrigem=None,
                    cidadeOrigemAlternativa=None,
                    cidadeDestinoAlternativa=None,
                    rotaId=None,
                    rotaDto=Rota(
                        id=4931,
                        empresaDto=None,
                        cidadeOrigem=Cidade(
                            id=5500,
                            uf="SP",
                            descricaoUf="São Paulo",
                            nome="Guarulhos",
                            nomeComUf="Guarulhos - SP",
                            nomeComUfNormalizado="guarulhos-sp",
                            codigoIbge="3518800",
                            codigoAntt="3561",
                            imagem=None,
                            urlImagem=None,
                        ),
                        cidadeDestino=Cidade(
                            id=4742,
                            uf="RS",
                            descricaoUf="Rio Grande do Sul",
                            nome="Porto Alegre",
                            nomeComUf="Porto Alegre - RS",
                            nomeComUfNormalizado="portoalegre-rs",
                            codigoIbge="4314902",
                            codigoAntt="63",
                            imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                            urlImagem=None,
                        ),
                        trechosDto=[],
                        conexoes=[],
                        opcoesTrechoAlternativo=None,
                        prefixo="RSSP1544002",
                        delimitacao=None,
                        descricao=None,
                        nomeFantasiaEmpresa=None,
                        empresaId=None,
                    ),
                    ordem=2,
                    duracao="01:00",
                    pontoEmbarque="TERMINAL RODOVIÁRIO BARRA FUNDA",
                    plataformaEmbarque=None,
                    taxaEmbarque="0,00",
                    pedagio="0,00",
                    quilometragem="21,00",
                    habilitadoVenderSemConexao=False,
                ),
                Trecho(
                    id=52630,
                    idEmpresa=751,
                    cidadeDestino=Cidade(
                        id=5673,
                        uf="SP",
                        descricaoUf="São Paulo",
                        nome="Osasco",
                        nomeComUf="Osasco - SP",
                        nomeComUfNormalizado="osasco-sp",
                        codigoIbge="3534401",
                        codigoAntt="1848",
                        imagem=None,
                        urlImagem=None,
                    ),
                    cidadeOrigem=None,
                    cidadeOrigemAlternativa=None,
                    cidadeDestinoAlternativa=None,
                    rotaId=None,
                    rotaDto=Rota(
                        id=4931,
                        empresaDto=None,
                        cidadeOrigem=Cidade(
                            id=5500,
                            uf="SP",
                            descricaoUf="São Paulo",
                            nome="Guarulhos",
                            nomeComUf="Guarulhos - SP",
                            nomeComUfNormalizado="guarulhos-sp",
                            codigoIbge="3518800",
                            codigoAntt="3561",
                            imagem=None,
                            urlImagem=None,
                        ),
                        cidadeDestino=Cidade(
                            id=4742,
                            uf="RS",
                            descricaoUf="Rio Grande do Sul",
                            nome="Porto Alegre",
                            nomeComUf="Porto Alegre - RS",
                            nomeComUfNormalizado="portoalegre-rs",
                            codigoIbge="4314902",
                            codigoAntt="63",
                            imagem="Vexado Passagem de ônibus mais barata Porto Alegre_14-1531.webp",
                            urlImagem=None,
                        ),
                        trechosDto=[],
                        conexoes=[],
                        opcoesTrechoAlternativo=None,
                        prefixo="RSSP1544002",
                        delimitacao=None,
                        descricao=None,
                        nomeFantasiaEmpresa=None,
                        empresaId=None,
                    ),
                    ordem=3,
                    duracao="00:30",
                    pontoEmbarque="TERMINAL RODOVIÁRIO DE OSASCO",
                    plataformaEmbarque=None,
                    taxaEmbarque="2,50",
                    pedagio="0,00",
                    quilometragem="15,00",
                    habilitadoVenderSemConexao=False,
                ),
            ],
            conexoes=[],
            opcoesTrechoAlternativo=None,
            prefixo="RSSP1544002",
            delimitacao="Guarulhos-SP x Porto Alegre-RS",
            descricao=None,
            nomeFantasiaEmpresa=None,
            empresaId=None,
        ),
        motoristas=[],
        usuariosEmpresaBloqueado=[],
        assentosDisponiveis=60,
        idEmpresa=751,
        nomeEmpresa="CATARINENSE AGENCIA DE VIAGENS LTDA",
        nomeFantasiaEmpresa="TURISSUL",
        poltronas=[],
        descricaoTipoVeiculo=None,
        quantidadeAssentos=None,
        preco=None,
        quilometragem="1402,00",
        tipoAjusteItinerario=TipoAjusteItinerario(
            descricao="Desconto", nameEnum="DESCONTO"
        ),
        valorAjuste="40.00",
        tipoPreco=None,
        andar=1,
        trechosBloqueados=[],
        cidadesBloqueados=[],
        trechosBloqueadosDesconto=[],
        habilitadoSite=True,
        assentosVendidos=None,
        sePodeEmbarcar=False,
        tipoConfiguracaoPreco=TipoConfiguracaoPreco(descricao="Normal", valor="NORMAL"),
        horaPartidaRegra=None,
        quantidadePoltronasRegra=None,
        ativo=True,
        prestador=None,
        complemento=None,
        tipoCategoria="MISTA",
        valorTipoPrecoPadrao="SEMI_LEITO_ESPECIAL_1",
        tipoPrecoPadrao=TipoPrecoPadrao(
            tipoPreco="SEMI_LEITO_ESPECIAL_1",
            descricaoTipoPreco="Semi Leito especial 1",
        ),
        passouTempoParaPartida=False,
        pricePorUsuario=[],
    )


async def test_seating_map(httpx_mock: HTTPXMock, client: VexadoClient):
    itinerary_id = 123
    origin_id = 456
    destination_id = 789
    httpx_mock.add_response(
        method="GET",
        url=f"https://vexado.test/mapas-veiculos/itinerario/{itinerary_id}/origem/{origin_id}/destino/{destination_id}",
        json=[
            {
                "id": 4,
                "numero": 4,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 36,
                        "numero": 3,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 11214989,
                            "numero": 3,
                            "reservada": False,
                            "bloqueada": True,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO",
                            "descricaoTipoCategoria": None,
                            "bloqueios": [
                                {
                                    "id": 103748,
                                    "numeroPoltrona": 3,
                                    "trechoOrigem": "Planaltina - DF",
                                    "trechoDestino": "Estreito - MA",
                                    "dataBloqueio": "07/07/2025 20:37",
                                    "usuarioResponsavel": "Suporte Vexado",
                                    "motivo": None,
                                }
                            ],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                            "preco": "10.00",
                        },
                    },
                    {
                        "id": 37,
                        "numero": 7,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 11214990,
                            "numero": 7,
                            "reservada": False,
                            "bloqueada": True,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO",
                            "descricaoTipoCategoria": None,
                            "bloqueios": [
                                {
                                    "id": 103749,
                                    "numeroPoltrona": 7,
                                    "trechoOrigem": "Planaltina - DF",
                                    "trechoDestino": "Estreito - MA",
                                    "dataBloqueio": "07/07/2025 20:37",
                                    "usuarioResponsavel": "Suporte Vexado",
                                    "motivo": None,
                                }
                            ],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                            "preco": "10.00",
                        },
                    },
                ],
            },
            {
                "id": 3,
                "numero": 3,
                "exibeCorredor": True,
                "assentos": [
                    {
                        "id": 25,
                        "numero": 4,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 11214978,
                            "numero": 4,
                            "reservada": False,
                            "bloqueada": True,
                            "dataSaida": None,
                            "tipoCategoria": None,
                            "descricaoTipoCategoria": None,
                            "bloqueios": [
                                {
                                    "id": 103759,
                                    "numeroPoltrona": 4,
                                    "trechoOrigem": "Planaltina - DF",
                                    "trechoDestino": "Estreito - MA",
                                    "dataBloqueio": "07/07/2025 20:37",
                                    "usuarioResponsavel": "Suporte Vexado",
                                    "motivo": None,
                                },
                                {
                                    "id": 105351,
                                    "numeroPoltrona": 4,
                                    "trechoOrigem": "Estreito - MA",
                                    "trechoDestino": "Lago da Pedra - MA",
                                    "dataBloqueio": "08/07/2025 10:50",
                                    "usuarioResponsavel": "RUTE FERREIRA GOIS",
                                    "motivo": None,
                                },
                            ],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                            "preco": "10.00",
                        },
                    },
                    {
                        "id": 26,
                        "numero": 8,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 11214979,
                            "numero": 8,
                            "reservada": False,
                            "bloqueada": True,
                            "dataSaida": None,
                            "tipoCategoria": None,
                            "descricaoTipoCategoria": None,
                            "bloqueios": [
                                {
                                    "id": 103760,
                                    "numeroPoltrona": 8,
                                    "trechoOrigem": "Planaltina - DF",
                                    "trechoDestino": "Estreito - MA",
                                    "dataBloqueio": "07/07/2025 20:37",
                                    "usuarioResponsavel": "Suporte Vexado",
                                    "motivo": None,
                                }
                            ],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                            "preco": "10.00",
                        },
                    },
                ],
            },
        ],
    )
    service_token = ServiceToken(
        token=(
            "eyJhbGciOiJIUzUxMiJ9.eyJudXMiOiJNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJub21lVXN1YXJpb0NvbU51bWVyby"
            "I6IjE0MDAgLSBNYXJrZXRpbmcgUGxhY2UgQnVzZXIiLCJzdWIiOiIxNDAwIiwiaWF0IjoxNzM2OTY2MDkxLCJleHAiO"
            "jE3MzY5ODc2OTF9.s93imRGqxeitXGzna3mT97x3R6_5feZEKhHzK5l526vPRkitXV72T0CxK1qPJaOyLwxItSAeei3T1KxWe94vdQ"
        ),
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    seating_map = await client.seating_map(
        service_token=service_token,
        itinerary_id=itinerary_id,
        origin_id=origin_id,
        destination_id=destination_id,
    )

    assert seating_map == [
        Fileira(
            id=4,
            numero=4,
            exibeCorredor=False,
            assentos=[
                Assento(
                    id=36,
                    numero=3,
                    ordem=0,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214989,
                        numero=3,
                        reservada=False,
                        bloqueada=True,
                        preco="10.00",
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria="LEITO",
                    ),
                ),
                Assento(
                    id=37,
                    numero=7,
                    ordem=1,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214990,
                        numero=7,
                        reservada=False,
                        bloqueada=True,
                        tipoPreco=None,
                        preco="10.00",
                        dataSaida=None,
                        andar=0,
                        tipoCategoria="LEITO",
                    ),
                ),
            ],
        ),
        Fileira(
            id=3,
            numero=3,
            exibeCorredor=True,
            assentos=[
                Assento(
                    id=25,
                    numero=4,
                    ordem=0,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214978,
                        numero=4,
                        reservada=False,
                        bloqueada=True,
                        preco="10.00",
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria=None,
                    ),
                ),
                Assento(
                    id=26,
                    numero=8,
                    ordem=1,
                    tipoAssento="NORMAL",
                    poltrona=Poltrona(
                        id=11214979,
                        numero=8,
                        reservada=False,
                        bloqueada=True,
                        preco="10.00",
                        tipoPreco=None,
                        dataSaida=None,
                        andar=0,
                        tipoCategoria=None,
                    ),
                ),
            ],
        ),
    ]


async def test_bloquear_poltrona(client, httpx_mock: HTTPXMock):
    httpx_mock.add_response(
        method="POST",
        url="https://vexado.test/bloqueio-poltrona-temporario",
        json={
            "id": 1,
            "poltronaId": 1,
            "uuid": "uuid",
            "preco": "10.00",
            "precoANTT": "10.00",
            "precoMeia": "10.00",
            "precoANTTMeia": "10.00",
            "taxaEmbarque": "10.00",
            "pedagio": "10.00",
            "tarifaSeguro": "10.00",
            "garantiaPrecoId": 1,
            "expiresAt": None,
        },
    )
    service_token = ServiceToken(
        token="token",
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    response = await client.bloquear_poltrona(
        service_token=service_token,
        poltrona_id=1,
        itinerario_id=1,
        trecho_origem_id=1,
        trecho_destino_id=1,
    )

    assert response == BloqueioResponse(
        id=1,
        poltronaId=1,
        uuid="uuid",
        preco="10.00",
        precoANTT="10.00",
        precoMeia="10.00",
        precoANTTMeia="10.00",
        taxaEmbarque="10.00",
        pedagio="10.00",
        tarifaSeguro="10.00",
        garantiaPrecoId=1,
        expiresAt=None,
    )


async def test_bloquear_poltrona_raises_seat_already_taken(
    client, httpx_mock: HTTPXMock
):
    httpx_mock.add_response(
        method="POST",
        url="https://vexado.test/bloqueio-poltrona-temporario",
        status_code=412,
        json={
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "poltronaReservada",
                    "mensagem": "Poltrona já está reservada para o trecho informado",
                }
            ]
        },
    )
    service_token = ServiceToken(
        token="token",
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    with pytest.raises(SeatUnavailable):
        await client.bloquear_poltrona(
            service_token=service_token,
            poltrona_id=1,
            itinerario_id=1,
            trecho_origem_id=1,
            trecho_destino_id=1,
        )


async def test_bloquear_poltrona_raises_travel_unavailable(
    client, httpx_mock: HTTPXMock
):
    httpx_mock.add_response(
        method="POST",
        url="https://vexado.test/bloqueio-poltrona-temporario",
        status_code=412,
        json={
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "BloqueioPoltronaTemporarioDto",
                    "mensagem": ("bloqueado temporariamente para venda!"),
                }
            ]
        },
    )
    service_token = ServiceToken(
        token="token",
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    with pytest.raises(TravelUnavailable):
        await client.bloquear_poltrona(
            service_token=service_token,
            poltrona_id=1,
            itinerario_id=1,
            trecho_origem_id=1,
            trecho_destino_id=1,
        )


async def test_liberar_poltrona(client, httpx_mock: HTTPXMock):
    httpx_mock.add_response(
        method="POST",
        url="https://vexado.test/bloqueio-poltrona-temporario/liberar",
        json=None,
    )
    service_token = ServiceToken(
        token="token",
        refresh_token="refresh_token",
        user_fingerprint="",
        roles=None,
    )
    response = await client.liberar_poltrona(
        service_token=service_token,
        id_bloqueio=1,
    )

    assert response is None
