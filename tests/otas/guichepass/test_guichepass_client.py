from datetime import date
from decimal import Decimal

import httpx
import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.exception import IncorrectPrice
from marketplace.otas.guichepass.client import (
    AuthResponse,
    BarCode,
    BpeInfo,
    BusType,
    Checkpoint,
    ConfirmReserveRequest,
    GuichepassClient,
    Journey,
    JourneyInfo,
    Location,
    PriceInfo,
    Seat,
    TicketInfo,
    TicketType,
)
from marketplace.otas.vexado.exception import SeatAlreadyTaken


@pytest.fixture
def client():
    return GuichepassClient(
        base_url="https://guichepass.test", username="testuser", password="testpass"
    )


@pytest.fixture
def mock_auth_response(httpx_mock: HTTPXMock):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/auth/login",
        json={
            "accessToken": "token123",
            "refreshToken": "refresh",
            "accessTokenSso": "sso",
            "refreshTokenSso": "refreshSso",
            "expiresAt": "2025-12-01T00:00:00Z",
            "expiresSec": 3600,
            "currentDate": "2025-06-24T00:00:00Z",
        },
    )


async def test_auth_login(httpx_mock: HTTPXMock, client: GuichepassClient):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/auth/login",
        json={
            "accessToken": "token123",
            "refreshToken": "refresh",
            "accessTokenSso": "sso",
            "refreshTokenSso": "refreshSso",
            "expiresAt": "2025-12-01T00:00:00Z",
            "expiresSec": 3600,
            "currentDate": "2025-06-24T00:00:00Z",
        },
    )

    result = await client.auth_login("user", "pass")
    assert result == AuthResponse(
        accessToken="token123",
        refreshToken="refresh",
        accessTokenSso="sso",
        refreshTokenSso="refreshSso",
        expiresAt="2025-12-01T00:00:00Z",
        expiresSec=3600,
        currentDate="2025-06-24T00:00:00Z",
    )


async def test_locations(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="GET",
        url="https://guichepass.test/web-sale/leg-stops/no-page",
        json=[
            {
                "id": 1,
                "name": "Terminal Rodoviário",
                "city": "Goiânia",
                "state": "GO",
                "ibgeCityCode": "5208707",
            }
        ],
    )

    result = await client.locations()
    assert result == [
        Location(
            id=1,
            name="Terminal Rodoviário",
            city="Goiânia",
            state="GO",
            ibgeCityCode="5208707",
        )
    ]


async def test_journeys_search(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/search",
        json=[
            {
                "origin": "1",
                "destination": "2",
                "departure": "2025-07-01T08:00",
                "arrival": "2025-07-01T12:00",
                "service": "Semi-Leito",
                "busCompany": "ViaNova",
                "busType": "C",
                "operationType": "NORMAL",
                "amenities": None,
                "distance": 350.0,
                "stopover": False,
                "freeSeats": 20,
                "price": 79.90,
                "kind": None,
                "message": "",
                "hasIntinerary": True,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0.0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            }
        ],
    )

    result = await client.journeys_search(
        origin=1, destination=2, departure_date=date(2025, 7, 1)
    )
    assert result == [
        Journey(
            origin="1",
            destination="2",
            departure="2025-07-01T08:00",
            arrival="2025-07-01T12:00",
            service="Semi-Leito",
            busCompany="ViaNova",
            busType="C",
            operationType="NORMAL",
            amenities=None,
            distance=350.0,
            stopover=False,
            freeSeats=20,
            price=79.9,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=False,
        )
    ]


async def test_journeys_search_unauthorized_refresh_token(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/search",
        json={},
        status_code=401,
    )

    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/auth/login",
        json={
            "accessToken": "token123",
            "refreshToken": "refresh",
            "accessTokenSso": "sso",
            "refreshTokenSso": "refreshSso",
            "expiresAt": "2025-12-01T00:00:00Z",
            "expiresSec": 3600,
            "currentDate": "2025-06-24T00:00:00Z",
        },
    )

    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/search",
        json=[
            {
                "origin": "1",
                "destination": "2",
                "departure": "2025-07-01T08:00",
                "arrival": "2025-07-01T12:00",
                "service": "Semi-Leito",
                "busCompany": "ViaNova",
                "busType": "C",
                "operationType": "NORMAL",
                "amenities": None,
                "distance": 350.0,
                "stopover": False,
                "freeSeats": 20,
                "price": 79.90,
                "kind": None,
                "message": "",
                "hasIntinerary": True,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0.0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            }
        ],
    )

    result = await client.journeys_search(
        origin=1, destination=2, departure_date=date(2025, 7, 1)
    )
    assert result == [
        Journey(
            origin="1",
            destination="2",
            departure="2025-07-01T08:00",
            arrival="2025-07-01T12:00",
            service="Semi-Leito",
            busCompany="ViaNova",
            busType="C",
            operationType="NORMAL",
            amenities=None,
            distance=350.0,
            stopover=False,
            freeSeats=20,
            price=79.9,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=False,
        )
    ]


async def test_journeys_search_unauthorized_twice(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/search",
        json={},
        status_code=401,
    )

    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/auth/login",
        json={
            "accessToken": "token123",
            "refreshToken": "refresh",
            "accessTokenSso": "sso",
            "refreshTokenSso": "refreshSso",
            "expiresAt": "2025-12-01T00:00:00Z",
            "expiresSec": 3600,
            "currentDate": "2025-06-24T00:00:00Z",
        },
    )

    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/search",
        json={},
        status_code=401,
    )
    with pytest.raises(httpx.HTTPStatusError):
        await client.journeys_search(
            origin=1, destination=2, departure_date=date(2025, 7, 1)
        )


async def test_get_itinerary(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="GET",
        url="https://guichepass.test/web-sale/service/SL123/itinerary",
        json=[{"id": 10, "name": "Uberlândia", "sequence": 1, "travelTime": "02:00"}],
    )

    result = await client.get_itinerary(service="SL123")
    assert result == [
        Checkpoint(id=10, name="Uberlândia", sequence=1, travelTime="02:00")
    ]


async def test_get_bus_layout(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/bus-layout",
        json={
            "seats": [
                {
                    "status": "FREE",
                    "x": 0,
                    "y": 1,
                    "z": 0,
                    "number": "1A",
                    "description": "Poltrona comum",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "Passagem",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 79.9,
                            "isNominalSale": False,
                            "allowsPromotion": False,
                            "code": "PAX",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                }
            ],
            "travel": {
                "origin": "1",
                "destination": "2",
                "departure": "2025-07-01T08:00",
                "arrival": "2025-07-01T12:00",
                "service": "Semi-Leito",
                "busCompany": "ViaNova",
                "busType": "C",
                "operationType": "NORMAL",
                "amenities": None,
                "distance": 350.0,
                "stopover": False,
                "freeSeats": 20,
                "price": 79.90,
                "kind": None,
                "message": "",
                "hasIntinerary": True,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0.0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            },
            "priceInfo": {
                "basePrice": 70.0,
                "insurancePrice": 2.0,
                "taxPrice": 1.0,
                "otherPrice": 0.0,
                "tollPrice": 0.0,
                "boardingPrice": 1.0,
                "commission": 5.9,
                "companyDiscount": 0.0,
                "discounts": [],
                "cancelationFee": None,
                "price": 79.9,
                "totalDiscount": 0.0,
                "priceWithoutInsurance": 77.9,
                "totalCompanyDiscount": 0.0,
                "originalPriceWithoutInsurance": 77.9,
                "priceWithBusCompanyDiscount": 77.9,
                "priceWithInsurance": 79.9,
                "originalPrice": 79.9,
            },
            "listQuotas": [],
        },
    )

    response = await client.get_bus_layout(
        departure_date=date(2025, 7, 1),
        origin=1,
        destination=2,
        company_id="123",
        service="Semi-Leito",
    )

    assert response == JourneyInfo(
        seats=[
            Seat(
                status="FREE",
                x=0,
                y=1,
                z=0,
                number="1A",
                description="Poltrona comum",
                ticketType=[
                    TicketType(
                        id=1,
                        description="Passagem",
                        salesStrategy="DEFAULT",
                        priceValue=79.9,
                        isNominalSale=False,
                        allowsPromotion=False,
                        code="PAX",
                        validateRule=None,
                        numberSeat=None,
                        amount=None,
                        quotType=None,
                    )
                ],
            )
        ],
        travel=Journey(
            origin="1",
            destination="2",
            departure="2025-07-01T08:00",
            arrival="2025-07-01T12:00",
            service="Semi-Leito",
            busCompany="ViaNova",
            busType="C",
            operationType="NORMAL",
            amenities=None,
            distance=350.0,
            stopover=False,
            freeSeats=20,
            price=79.9,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=False,
        ),
        priceInfo=PriceInfo(
            basePrice=70.0,
            insurancePrice=2.0,
            taxPrice=1.0,
            otherPrice=0.0,
            tollPrice=0.0,
            boardingPrice=1.0,
            commission=5.9,
            companyDiscount=0.0,
            discounts=[],
            cancelationFee=None,
            price=79.9,
            totalDiscount=0.0,
            priceWithoutInsurance=77.9,
            totalCompanyDiscount=0.0,
            originalPriceWithoutInsurance=77.9,
            priceWithBusCompanyDiscount=77.9,
            priceWithInsurance=79.9,
            originalPrice=79.9,
            priceClassification=None,
        ),
        listQuotas=[],
    )


async def test_get_bus_types(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="GET",
        url="https://guichepass.test/web-sale/seatTypes",
        json=[{"name": "Leito", "id": 1}],
    )

    result = await client.get_bus_types()
    assert result == [BusType(name="Leito", id=1)]


async def test_create_reserve(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/create-reserve",
        json={
            "origin": "RECIFE",
            "destination": "UBERLANDIA",
            "departure": "2021-02-24T06:00:00",
            "arrival": "2021-02-24T21:30:00",
            "service": "790",
            "busCompany": "2",
            "busType": "1",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 62714,
            "seat": "3",
            "priceInfo": {
                "basePrice": 88,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 8.8,
                "discounts": [],
                "cancelationFee": None,
                "priceClassification": None,
                "priceWithoutInsurance": 82.52,
                "totalCompanyDiscount": 0,
                "originalPrice": 82.52,
                "originalPriceWithoutInsurance": 82.52,
                "priceWithBusCompanyDiscount": 82.52,
                "priceWithInsurance": 82.52,
                "price": 82.52,
                "totalDiscount": 0,
            },
            "alternativePrices": [],
            "status": "RESERVED",
            "name": None,
            "document": None,
            "ticket": None,
            "ticketNumber": None,
            "message": None,
            "metaData": {},
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": None,
            "timestamp": "2021-02-24T10:15:49.000",
            "bpeInfo": None,
            "channel": None,
            "insuranceSelected": False,
        },
    )

    response = await client.create_reserve(
        departure_date=date(2025, 7, 1),
        origin=1,
        destination=2,
        company_id="123",
        service="Semi-Leito",
        seat=10,
    )

    assert response == TicketInfo(
        origin="RECIFE",
        destination="UBERLANDIA",
        departure="2021-02-24T06:00:00",
        arrival="2021-02-24T21:30:00",
        service="790",
        busCompany="2",
        busType="1",
        operationType="DEFAULT",
        amenities=None,
        distance=None,
        stopover=False,
        id=62714,
        seat="3",
        priceInfo=PriceInfo(
            basePrice=88,
            insurancePrice=0,
            taxPrice=0,
            otherPrice=0,
            tollPrice=0,
            boardingPrice=3.32,
            commission=0,
            companyDiscount=8.8,
            discounts=[],
            cancelationFee=None,
            priceClassification=None,
            priceWithoutInsurance=82.52,
            totalCompanyDiscount=0,
            originalPrice=82.52,
            originalPriceWithoutInsurance=82.52,
            priceWithBusCompanyDiscount=82.52,
            priceWithInsurance=82.52,
            price=82.52,
            totalDiscount=0,
        ),
        alternativePrices=[],
        status="RESERVED",
        name=None,
        document=None,
        ticket=None,
        ticketNumber=None,
        message=None,
        metaData={},
        orderId=None,
        buyerInfoId=None,
        optInInsurance=False,
        noSeatNumberRequired=False,
        barCodes=None,
        timestamp="2021-02-24T10:15:49.000",
        bpeInfo=None,
        channel=None,
        insuranceSelected=False,
    )


async def test_create_reserve_seat_already_taken(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/create-reserve",
        status_code=422,
        json={
            "text": "Assento Reservado",
            "code": "reserved-seat.reserved",
            "status": "UNPROCESSABLE_ENTITY",
            "children": None,
            "properties": [
                {"name": "description", "value": "Assento Reservado", "type": "string"}
            ],
            "timestamp": "2021-09-09T09:27:10.337",
        },
    )
    with pytest.raises(SeatAlreadyTaken):
        await client.create_reserve(
            departure_date=date(2025, 7, 1),
            origin=1,
            destination=2,
            company_id="123",
            service="Semi-Leito",
            seat=10,
        )


async def test_cancel_reserve(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/cancel-reserve",
        json={
            "origin": "RECIFE",
            "destination": "UBERLANDIA",
            "departure": "2021-02-24T20:00:00",
            "arrival": "2021-02-25T08:00:00",
            "service": "834",
            "busCompany": "1",
            "busType": "5",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 62624,
            "seat": "3",
            "priceInfo": {
                "basePrice": 3.33,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 0,
                "discounts": [],
                "cancelationFee": 0,
                "price": 6.65,
                "totalCompanyDiscount": 0,
                "originalPriceWithoutInsurance": 6.65,
                "priceWithBusCompanyDiscount": 6.65,
                "priceWithoutInsurance": 6.65,
                "originalPrice": 6.65,
                "totalDiscount": 0,
                "priceWithInsurance": 6.65,
            },
            "alternativePrices": [],
            "status": "CANCELED",
            "name": "ALINE CAPPELLI -FORCE",
            "document": "4572080",
            "ticket": None,
            "ticketNumber": "1T5X6M",
            "message": None,
            "metaData": {},
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": [
                {
                    "id": None,
                    "value": "1T5X6M",
                    "application": "BOARDING_PASS",
                    "standard": "CODE_128",
                }
            ],
            "timestamp": "2021-02-24T10:22:15.000",
            "bpeInfo": None,
            "channel": None,
            "insuranceSelected": False,
        },
    )
    response = await client.cancel_reserve(reserve_id=123)
    assert response == TicketInfo(
        origin="RECIFE",
        destination="UBERLANDIA",
        departure="2021-02-24T20:00:00",
        arrival="2021-02-25T08:00:00",
        service="834",
        busCompany="1",
        busType="5",
        operationType="DEFAULT",
        amenities=None,
        distance=None,
        stopover=False,
        id=62624,
        seat="3",
        priceInfo=PriceInfo(
            basePrice=3.33,
            insurancePrice=0,
            taxPrice=0,
            otherPrice=0,
            tollPrice=0,
            boardingPrice=3.32,
            commission=0,
            companyDiscount=0,
            discounts=[],
            cancelationFee=0,
            price=6.65,
            totalCompanyDiscount=0,
            originalPriceWithoutInsurance=6.65,
            priceWithBusCompanyDiscount=6.65,
            priceWithoutInsurance=6.65,
            originalPrice=6.65,
            totalDiscount=0,
            priceWithInsurance=6.65,
            priceClassification=None,
        ),
        alternativePrices=[],
        status="CANCELED",
        name="ALINE CAPPELLI -FORCE",
        document="4572080",
        ticket=None,
        ticketNumber="1T5X6M",
        message=None,
        metaData={},
        orderId=None,
        buyerInfoId=None,
        optInInsurance=False,
        noSeatNumberRequired=False,
        barCodes=[
            BarCode(
                id=None,
                value="1T5X6M",
                application="BOARDING_PASS",
                standard="CODE_128",
            )
        ],
        timestamp="2021-02-24T10:22:15.000",
        bpeInfo=None,
        channel=None,
        insuranceSelected=False,
    )


async def test_cancel_reserve_expired(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/cancel-reserve",
        status_code=200,
        json={
            "origin": "COLATINA",
            "destination": "BELO HORIZONTE",
            "departure": "2025-11-05T20:30:00",
            "arrival": "2025-11-06T06:20:00",
            "service": "25",
            "busCompany": "1",
            "busType": "3",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 93766,
            "seat": "6",
            "priceInfo": None,
            "alternativePrices": [],
            "status": "EXPIRED",
            "name": None,
            "document": None,
            "ticket": None,
            "ticketNumber": None,
            "message": None,
            "metaData": None,
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": [],
            "timestamp": "2025-10-16T15:54:53.000",
            "date": None,
            "bpeInfo": None,
            "channel": None,
            "insurancePolicy": None,
            "idReservationResponsible": None,
            "limitedSales": None,
            "insuranceSelected": False,
        },
    )
    response = await client.cancel_reserve(reserve_id=123)
    assert response == TicketInfo(
        origin="COLATINA",
        destination="BELO HORIZONTE",
        departure="2025-11-05T20:30:00",
        arrival="2025-11-06T06:20:00",
        service="25",
        busCompany="1",
        busType="3",
        operationType="DEFAULT",
        amenities=None,
        distance=None,
        stopover=False,
        id=93766,
        seat="6",
        priceInfo=None,
        alternativePrices=[],
        status="EXPIRED",
        name=None,
        document=None,
        ticket=None,
        ticketNumber=None,
        message=None,
        metaData=None,
        orderId=None,
        buyerInfoId=None,
        optInInsurance=False,
        noSeatNumberRequired=False,
        barCodes=[],
        timestamp="2025-10-16T15:54:53.000",
        bpeInfo=None,
        channel=None,
        insuranceSelected=False,
    )


async def test_cancel_reserve_ticket_printed(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/cancel-reserve",
        status_code=422,
        json={
            "text": None,
            "code": "cancellation.ticket_printed",
            "status": "UNPROCESSABLE_ENTITY",
            "children": None,
            "properties": [
                {"name": "transactionId", "value": "144398", "type": "string"}
            ],
            "timestamp": "2023-05-03T18:10:47.852",
        },
    )
    with pytest.raises(httpx.HTTPStatusError):
        await client.cancel_reserve(reserve_id=123)


async def test_confirm_reserve(
    mock_auth_response, httpx_mock: HTTPXMock, client: GuichepassClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/confirm-reserve",
        json={
            "origin": "BELO HORIZONTE",
            "destination": "LINHARES",
            "departure": "2024-12-10T20:15:00",
            "arrival": "2024-12-11T07:35:00",
            "service": "23",
            "busCompany": "1",
            "busType": "3",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 59576,
            "seat": "20",
            "priceInfo": {
                "basePrice": 286.05,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 2.45,
                "boardingPrice": 7.55,
                "commission": 0,
                "companyDiscount": 32.10,
                "discounts": [],
                "cancelationFee": 0,
                "priceWithInsurance": 263.95,
                "totalDiscount": 0,
                "priceWithoutInsurance": 263.95,
                "totalCompanyDiscount": 0,
                "originalPriceWithoutInsurance": 263.95,
                "priceWithBusCompanyDiscount": 263.95,
                "originalPrice": 263.95,
                "price": 263.95,
            },
            "alternativePrices": [],
            "status": "CONFIRMED",
            "name": "LEANDRO TESTE",
            "document": "89281929",
            "ticket": None,
            "ticketNumber": "1Q5W6V",
            "message": None,
            "metaData": None,
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": [
                {
                    "id": None,
                    "value": "1Q5W6V",
                    "application": "BOARDING_PASS",
                    "standard": "CODE_128",
                },
                {
                    "id": None,
                    "value": "13106170085813000532",
                    "application": "BUS_STATION",
                    "standard": "QR_CODE",
                },
            ],
            "timestamp": "2025-11-26T17:51:10.000",
            "date": None,
            "bpeInfo": {
                "bpeQrCode": "https://portalbpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31251106789401000230630010000214821080451053&tpAmb=1",
                "platform": "E1",
                "prefix": "06045860",
                "line": "LINHARES/ ES - BELO HORIZONTE/ MG EXE - 06045860",
                "totalAmount": 263.95,
                "discountAmount": 32.10,
                "paymentAmount": 263.95,
                "bpeAccessKey": "BPe31251106789401000230630010000214821080451053",
                "contactTel": "08008802006",
                "specialContactTel": None,
                "bpeQueryUrl": "https://bpe.fazenda.mg.gov.br/bpe/services/BPeConsulta",
                "paymentMethod": "Cartão de Crédito",
                "paymentMethodAmount": 263.95,
                "changeAmount": 0,
                "discountType": "Tarifa promocional",
                "bpeNumber": "21482",
                "bpeSeries": "1",
                "bpeAuthProtocol": "131250284968991",
                "bpeAuthorizationDate": "2025-11-26T17:51:10",
                "systemNumber": "59576",
                "otherTributes": "Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$17.78",
                "contingency": False,
                "anttOriginCode": None,
                "anttDestinationCode": None,
                "bpeMonitriipCode": None,
                "taxAmount": 0,
                "tollAmount": 2.45,
                "boardingTaxAmount": 7.55,
                "insuranceAmount": 0,
                "othersAmounts": 0,
                "agencyHeaderDistrict": None,
                "agencyHeaderCity": None,
                "agencyHeaderCnpj": None,
                "agencyHeaderAddress": None,
                "agencyHeaderNumber": None,
                "agencyHeaderCompanyName": None,
                "agencyHeaderState": None,
                "emitterHeaderDistrict": "VERA CRUZ",
                "emitterHeaderPostalCode": "",
                "emitterHeaderCity": "BELO HORIZONTE",
                "emitterHeaderCnpj": "06789401000230",
                "emitterHeaderAddress": "R PADRE FEIJO",
                "emitterHeaderStateRegistration": "0019365990092",
                "emitterHeaderNumber": "850",
                "emitterHeaderCompanyName": "LNHARES HOTEL E TURISMO LTDA",
                "emitterHeaderState": "MG",
                "optionalMessage": None,
            },
            "channel": None,
            "insurancePolicy": None,
            "idReservationResponsible": None,
            "limitedSales": None,
            "insuranceSelected": False,
        },
    )

    response = await client.confirm_reserve(
        request=ConfirmReserveRequest(
            name="LEANDRO TESTE",
            document="89281929",
            price=Decimal("263.95"),
            reserve_id=59576,
            birthdate="1990-01-01",
            ticketTypeId="1",
        )
    )
    assert response == TicketInfo(
        origin="BELO HORIZONTE",
        destination="LINHARES",
        departure="2024-12-10T20:15:00",
        arrival="2024-12-11T07:35:00",
        service="23",
        busCompany="1",
        busType="3",
        operationType="DEFAULT",
        amenities=None,
        distance=None,
        stopover=False,
        id=59576,
        seat="20",
        priceInfo=PriceInfo(
            basePrice=286.05,
            insurancePrice=0,
            taxPrice=0,
            otherPrice=0,
            tollPrice=2.45,
            boardingPrice=7.55,
            commission=0,
            companyDiscount=32.10,
            discounts=[],
            cancelationFee=0,
            price=263.95,
            totalDiscount=0,
            priceWithoutInsurance=263.95,
            totalCompanyDiscount=0,
            originalPriceWithoutInsurance=263.95,
            priceWithBusCompanyDiscount=263.95,
            priceWithInsurance=263.95,
            originalPrice=263.95,
            priceClassification=None,
        ),
        alternativePrices=[],
        status="CONFIRMED",
        name="LEANDRO TESTE",
        document="89281929",
        ticket=None,
        ticketNumber="1Q5W6V",
        message=None,
        metaData=None,
        orderId=None,
        buyerInfoId=None,
        optInInsurance=False,
        noSeatNumberRequired=False,
        barCodes=[
            BarCode(
                id=None,
                value="1Q5W6V",
                application="BOARDING_PASS",
                standard="CODE_128",
            ),
            BarCode(
                id=None,
                value="13106170085813000532",
                application="BUS_STATION",
                standard="QR_CODE",
            ),
        ],
        timestamp="2025-11-26T17:51:10.000",
        bpeInfo=BpeInfo(
            bpeQrCode="https://portalbpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31251106789401000230630010000214821080451053&tpAmb=1",
            platform="E1",
            prefix="06045860",
            line="LINHARES/ ES - BELO HORIZONTE/ MG EXE - 06045860",
            totalAmount=263.95,
            discountAmount=32.10,
            paymentAmount=263.95,
            bpeAccessKey="BPe31251106789401000230630010000214821080451053",
            contactTel="08008802006",
            specialContactTel=None,
            bpeQueryUrl="https://bpe.fazenda.mg.gov.br/bpe/services/BPeConsulta",
            paymentMethod="Cartão de Crédito",
            paymentMethodAmount=263.95,
            changeAmount=0,
            discountType="Tarifa promocional",
            bpeNumber="21482",
            bpeSeries="1",
            bpeAuthProtocol="131250284968991",
            bpeAuthorizationDate="2025-11-26T17:51:10",
            systemNumber="59576",
            otherTributes="Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$17.78",
            contingency=False,
            anttOriginCode=None,
            anttDestinationCode=None,
            bpeMonitriipCode=None,
            taxAmount=0,
            tollAmount=2.45,
            boardingTaxAmount=7.55,
            insuranceAmount=0,
            othersAmounts=0,
            agencyHeaderDistrict=None,
            agencyHeaderCity=None,
            agencyHeaderCnpj=None,
            agencyHeaderAddress=None,
            agencyHeaderNumber=None,
            agencyHeaderCompanyName=None,
            agencyHeaderState=None,
            emitterHeaderDistrict="VERA CRUZ",
            emitterHeaderPostalCode="",
            emitterHeaderCity="BELO HORIZONTE",
            emitterHeaderCnpj="06789401000230",
            emitterHeaderAddress="R PADRE FEIJO",
            emitterHeaderStateRegistration="0019365990092",
            emitterHeaderNumber="850",
            emitterHeaderCompanyName="LNHARES HOTEL E TURISMO LTDA",
            emitterHeaderState="MG",
            optionalMessage=None,
        ),
        channel=None,
        insuranceSelected=False,
    )


async def test_confirm_reserve_incorrect_price(
    mock_auth_response, client, httpx_mock: HTTPXMock
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/confirm-reserve",
        json={
            "text": "reserve.id: 477978. Expected value: 54.75",
            "code": "reserve.value_invalid",
            "status": "UNPROCESSABLE_ENTITY",
            "children": None,
            "properties": [
                {
                    "name": "description",
                    "value": "reserve.id: 477978. Expected value: 54.75",
                    "type": "string",
                }
            ],
            "timestamp": "2025-11-26T16:43:00.478",
        },
        status_code=422,
    )
    with pytest.raises(IncorrectPrice) as exc_info:
        await client.confirm_reserve(
            request=ConfirmReserveRequest(
                name="LEANDRO TESTE",
                document="89281929",
                price=Decimal("263.95"),
                reserve_id=59576,
                birthdate="1990-01-01",
                ticketTypeId="1",
            )
        )
    assert str(exc_info.value) == "Expected price 54.75, got 263.95"
