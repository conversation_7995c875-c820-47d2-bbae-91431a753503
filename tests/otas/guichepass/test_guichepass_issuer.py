from dataclasses import asdict
from datetime import UTC, date, datetime, timezone
from decimal import Decimal
from unittest.mock import AsyncMock

import pytest

from marketplace.models import (
    Benefit,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Pax,
    Place,
    YoungBenefit,
)
from marketplace.otas.guichepass.client import (
    BarCode,
    BpeInfo,
    ConfirmReserveRequest,
    PriceInfo,
    TicketInfo,
)
from marketplace.otas.guichepass.ticket_issuer import GuichepassTicketIssuer
from marketplace.tickets import IssuedTicket, Ticket


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=10,
        name="Guichepass",
        provider="guichepass",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        config={
            "base_url": "https://test.guichepass.com",
            "client_id": "client123",
            "username": "user",
            "password": "pass",
        },
        companies=[
            OTACompany(
                name="Test Company",
                external_id=388,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=10,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=10,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executive",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=600, stale_after=120),
    )


@pytest.fixture
def mock_client(monkeypatch):
    m = AsyncMock()
    monkeypatch.setattr(
        "marketplace.otas.guichepass.ticket_issuer.GuichepassClient", lambda **kw: m
    )
    return m


@pytest.fixture
def issuer(ota_config, mock_client):
    return GuichepassTicketIssuer(ota_config)


@pytest.fixture
def ticket(ota_config):
    return Ticket(
        id=1,
        client_code="XYZ999",
        tags={},
        valid_until=datetime(2025, 12, 31),
        pax_name="John Doe",
        pax_doc_type=Pax.DocType.RG,
        pax_doc="1234567",
        pax_cpf="11122233344",
        pax_phone="+5511900001111",
        pax_birthdate=date(1990, 1, 1),
        pax_benefit=YoungBenefit(
            type=Benefit.YOUNG_50,
            number="Y0001",
            issue_date=date(2020, 1, 1),
            expiration_date=date(2030, 1, 1),
        ),
        code="TRIP001",
        departure_at=datetime(2025, 9, 10, 15),
        arrival_at=datetime(2025, 9, 10, 20),
        seat_type="executive",
        travel_extra={},
        seat_number="12",
        seat_floor=1,
        seat_row=12,
        seat_column=1,
        seat_extra={"benefits_extra": {"YOUNG_LOW_INCOME_50": {"id": "99"}}},
        seat_block_key="1234",
        price=Decimal("150.00"),
        created_at=datetime.now(UTC),
        ota_config=ota_config,
        company=ota_config.companies[0],
        origin=Place(slug="sao-paulo", name="São Paulo"),
        destination=Place(slug="rio-de-janeiro", name="Rio de Janeiro"),
        expired=False,
    )


@pytest.mark.asyncio
async def test_issue_ticket_missing_block_key(issuer, ticket):
    ticket.seat_block_key = None

    with pytest.raises(ValueError, match="ticket.seat_block_key is None"):
        await issuer.issue_ticket(ticket)


@pytest.mark.asyncio
async def test_issue_ticket(issuer, mock_client, ticket):
    mock_client.confirm_reserve.return_value = TicketInfo(
        origin="BELO HORIZONTE",
        destination="LINHARES",
        departure="2024-12-10T20:15:00",
        arrival="2024-12-11T07:35:00",
        service="23",
        busCompany="1",
        busType="3",
        operationType="DEFAULT",
        amenities=None,
        distance=None,
        stopover=False,
        id=59576,
        seat="20",
        priceInfo=PriceInfo(
            basePrice=286.05,
            insurancePrice=0,
            taxPrice=0,
            otherPrice=0,
            tollPrice=2.45,
            boardingPrice=7.55,
            commission=0,
            companyDiscount=32.10,
            discounts=[],
            cancelationFee=0,
            price=263.95,
            totalDiscount=0,
            priceWithoutInsurance=263.95,
            totalCompanyDiscount=0,
            originalPriceWithoutInsurance=263.95,
            priceWithBusCompanyDiscount=263.95,
            priceWithInsurance=263.95,
            originalPrice=263.95,
            priceClassification=None,
        ),
        alternativePrices=[],
        status="CONFIRMED",
        name="LEANDRO TESTE",
        document="89281929",
        ticket=None,
        ticketNumber="1Q5W6V",
        message=None,
        metaData=None,
        orderId=None,
        buyerInfoId=None,
        optInInsurance=False,
        noSeatNumberRequired=False,
        barCodes=[
            BarCode(
                id=None,
                value="1Q5W6V",
                application="BOARDING_PASS",
                standard="CODE_128",
            ),
            BarCode(
                id=None,
                value="13106170085813000532",
                application="BUS_STATION",
                standard="QR_CODE",
            ),
        ],
        timestamp="2025-11-26T17:51:10.000",
        bpeInfo=BpeInfo(
            bpeQrCode="https://portalbpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31251106789401000230630010000214821080451053&tpAmb=1",
            platform="E1",
            prefix="06045860",
            line="LINHARES/ ES - BELO HORIZONTE/ MG EXE - 06045860",
            totalAmount=263.95,
            discountAmount=32.10,
            paymentAmount=263.95,
            bpeAccessKey="BPe31251106789401000230630010000214821080451053",
            contactTel="08008802006",
            specialContactTel=None,
            bpeQueryUrl="https://bpe.fazenda.mg.gov.br/bpe/services/BPeConsulta",
            paymentMethod="Cartão de Crédito",
            paymentMethodAmount=263.95,
            changeAmount=0,
            discountType="Tarifa promocional",
            bpeNumber="21482",
            bpeSeries="1",
            bpeAuthProtocol="131250284968991",
            bpeAuthorizationDate="2025-11-26T17:51:10",
            systemNumber="59576",
            otherTributes="Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$17.78",
            contingency=False,
            anttOriginCode=None,
            anttDestinationCode=None,
            bpeMonitriipCode=None,
            taxAmount=0,
            tollAmount=2.45,
            boardingTaxAmount=7.55,
            insuranceAmount=0,
            othersAmounts=0,
            agencyHeaderDistrict=None,
            agencyHeaderCity=None,
            agencyHeaderCnpj=None,
            agencyHeaderAddress=None,
            agencyHeaderNumber=None,
            agencyHeaderCompanyName=None,
            agencyHeaderState=None,
            emitterHeaderDistrict="VERA CRUZ",
            emitterHeaderPostalCode="",
            emitterHeaderCity="BELO HORIZONTE",
            emitterHeaderCnpj="06789401000230",
            emitterHeaderAddress="R PADRE FEIJO",
            emitterHeaderStateRegistration="0019365990092",
            emitterHeaderNumber="850",
            emitterHeaderCompanyName="LNHARES HOTEL E TURISMO LTDA",
            emitterHeaderState="MG",
            optionalMessage=None,
        ),
        channel=None,
        insuranceSelected=False,
    )

    result = await issuer.issue_ticket(ticket)

    assert result == IssuedTicket(
        localizador="1Q5W6V",
        numero_pedido=None,
        numero_bilhete="1Q5W6V",
        numero_bpe="21482",
        chave_bpe="BPe31251106789401000230630010000214821080451053",
        serie_bpe="1",
        protocolo_autorizacao="131250284968991",
        data_autorizacao=datetime(2025, 11, 26, 17, 51, 10),
        nome_agencia=None,
        emissao_em_contigencia=None,
        bpe_qrcode="https://portalbpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31251106789401000230630010000214821080451053&tpAmb=1",
        monitriip_code=None,
        numero_embarque_code=None,
        embarque_code=None,
        tipo_embarque=None,
        preco=286.05,
        preco_pedagio=2.45,
        preco_taxa_embarque=7.55,
        preco_seguro=0,
        preco_desconto=0,
        preco_total=263.95,
        outros_tributos="Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$17.78",
        poltrona="20",
        plataforma="E1",
        linha="LINHARES/ ES - BELO HORIZONTE/ MG EXE - 06045860",
        prefixo="06045860",
        servico="23",
        cnpj="06789401000230",
        endereco_empresa="R PADRE FEIJO",
        extra=asdict(mock_client.confirm_reserve.return_value),
    )

    mock_client.confirm_reserve.assert_called_once_with(
        request=ConfirmReserveRequest(
            name="John Doe",
            document="1234567",
            price=Decimal("150.00"),
            reserve_id=1234,
            birthdate="1990-01-01",
            ticketTypeId="99",
        ),
    )


@pytest.mark.asyncio
async def test_cancel_ticket(issuer, mock_client, ticket):
    mock_client.cancel_reserve.return_value = TicketInfo(
        origin="BELO HORIZONTE",
        destination="LINHARES",
        departure="2024-12-10T20:15:00",
        arrival="2024-12-11T07:35:00",
        service="23",
        busCompany="1",
        busType="3",
        operationType="DEFAULT",
        amenities=None,
        distance=None,
        stopover=False,
        id=59576,
        seat="20",
        priceInfo=PriceInfo(
            basePrice=286.05,
            insurancePrice=0,
            taxPrice=0,
            otherPrice=0,
            tollPrice=2.45,
            boardingPrice=7.55,
            commission=0,
            companyDiscount=32.10,
            discounts=[],
            cancelationFee=0,
            price=263.95,
            totalDiscount=0,
            priceWithoutInsurance=263.95,
            totalCompanyDiscount=0,
            originalPriceWithoutInsurance=263.95,
            priceWithBusCompanyDiscount=263.95,
            priceWithInsurance=263.95,
            originalPrice=263.95,
            priceClassification=None,
        ),
        alternativePrices=[],
        status="CONFIRMED",
        name="LEANDRO TESTE",
        document="89281929",
        ticket=None,
        ticketNumber="1Q5W6V",
        message=None,
        metaData=None,
        orderId=None,
        buyerInfoId=None,
        optInInsurance=False,
        noSeatNumberRequired=False,
        barCodes=[
            BarCode(
                id=None,
                value="1Q5W6V",
                application="BOARDING_PASS",
                standard="CODE_128",
            ),
            BarCode(
                id=None,
                value="13106170085813000532",
                application="BUS_STATION",
                standard="QR_CODE",
            ),
        ],
        timestamp="2025-11-26T17:51:10.000",
        bpeInfo=BpeInfo(
            bpeQrCode="https://portalbpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31251106789401000230630010000214821080451053&tpAmb=1",
            platform="E1",
            prefix="06045860",
            line="LINHARES/ ES - BELO HORIZONTE/ MG EXE - 06045860",
            totalAmount=263.95,
            discountAmount=32.10,
            paymentAmount=263.95,
            bpeAccessKey="BPe31251106789401000230630010000214821080451053",
            contactTel="08008802006",
            specialContactTel=None,
            bpeQueryUrl="https://bpe.fazenda.mg.gov.br/bpe/services/BPeConsulta",
            paymentMethod="Cartão de Crédito",
            paymentMethodAmount=263.95,
            changeAmount=0,
            discountType="Tarifa promocional",
            bpeNumber="21482",
            bpeSeries="1",
            bpeAuthProtocol="131250284968991",
            bpeAuthorizationDate="2025-11-26T17:51:10",
            systemNumber="59576",
            otherTributes="Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$17.78",
            contingency=False,
            anttOriginCode=None,
            anttDestinationCode=None,
            bpeMonitriipCode=None,
            taxAmount=0,
            tollAmount=2.45,
            boardingTaxAmount=7.55,
            insuranceAmount=0,
            othersAmounts=0,
            agencyHeaderDistrict=None,
            agencyHeaderCity=None,
            agencyHeaderCnpj=None,
            agencyHeaderAddress=None,
            agencyHeaderNumber=None,
            agencyHeaderCompanyName=None,
            agencyHeaderState=None,
            emitterHeaderDistrict="VERA CRUZ",
            emitterHeaderPostalCode="",
            emitterHeaderCity="BELO HORIZONTE",
            emitterHeaderCnpj="06789401000230",
            emitterHeaderAddress="R PADRE FEIJO",
            emitterHeaderStateRegistration="0019365990092",
            emitterHeaderNumber="850",
            emitterHeaderCompanyName="LNHARES HOTEL E TURISMO LTDA",
            emitterHeaderState="MG",
            optionalMessage=None,
        ),
        channel=None,
        insuranceSelected=False,
    )

    result = await issuer.cancel_ticket(ticket)

    assert result == asdict(mock_client.cancel_reserve.return_value)
    mock_client.cancel_reserve.assert_called_once_with(int(ticket.seat_block_key))


@pytest.mark.asyncio
async def test_cancel_ticket_no_block_key(issuer, ticket):
    ticket.seat_block_key = None

    with pytest.raises(ValueError, match="ticket.seat_block_key is None"):
        await issuer.cancel_ticket(ticket)
