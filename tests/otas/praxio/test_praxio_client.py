from datetime import datetime

import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.praxio.client import (
    Conexao,
    Gerenciamento,
    LaypoltronaXml,
    ListaPartidasTFOResponse,
    Localidade,
    LocalidadePartida,
    LoginResponse,
    Partida,
    PartidasResponse,
    PartidaTFO,
    Poltrona,
    PraxioClient,
    PraxioClientError,
    RetornaPoltronasResponse,
    VerificaPoltronaResponse,
    ViagemTFO,
    ViagemTFOConexao,
)


@pytest.fixture
def client():
    return PraxioClient(
        base_url="https://praxio.test",
    )


async def test_efetua_login(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Login/EfetuaLogin",
        json={
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0.0,
            "Sucesso": True,
            "Advertencia": False,
            "ParametrosEntrada": {},
            "Comissao": 0.0,
            "RelatorioAgencia": 0,
            "Telefone": None,
            "Email": None,
            "MaxDescManual": 0.0,
            "MaxAcresManual": 0.0,
            "PermDescManualOperador": False,
            "PermAcresManualOperador": False,
            "DescontoPercentual": 0.0,
            "AcrescimoPercentual": 0.0,
            "IdOperador": 1,
            "IdSessaoOp": "ABC123",
            "Nome": "Test User",
            "Super": 0,
            "TempoInativarOperadorAposTempo": 0,
            "TempoSessao": 0,
            "VisualizouLGPD": None,
            "IdJuncao": 0,
            "RelSuperOp": False,
            "LiberarCancTEF": False,
            "LiberarReimpPass": False,
            "LiberarMarcarVoucher": False,
            "InativarOperadorAposTempo": False,
            "VisualizouTermoLGPD": None,
            "VendDevOutroCaixaSemCaixa": False,
            "UsuarioConsulta": False,
            "ApenasBiometria": None,
            "AlterarSenhaOperadorXDias": False,
            "TempoAlterarSenhaOperadorXDias": 0,
            "PassouQtdDiasAlterarSenha": False,
            "ObrigaCadastroPos": None,
            "EstabelecimentoPadrao": 0,
            "EstabelecimentoXml": {
                "IDEstabelecimento": 1,
                "Id": 1,
                "NomeFantasia": "Test Company",
                "RazaoSocial": "Test Company LTDA",
                "Cnpj": "12345678901234",
                "Endereco": "Test Street",
                "Numero": "123",
                "Complemento": "Suite 1",
                "Bairro": "Test District",
                "Cidade": "Test City",
                "Uf": "SP",
                "Cep": "12345678",
                "Telefone": None,
                "IMunicipal": "",
                "IEstadual": "",
                "IdEmpresa": 1,
                "IdCidade": 1,
                "NomeCidade": "Test City",
                "AtualizaSegurancaCertificado": None,
                "IdMoeda": 1,
                "AmbienteProducao": False,
                "SerieBpe": 1,
                "CupomEmbarqueVoucher": False,
                "NaoEmbarqueAutomatico": False,
                "NaoEmbarqueAutomaticoAbertura": False,
                "UtilizarBpe": None,
                "FormatoLogo": None,
                "Logo": None,
                "FormaPgto": [],
                "CodigoExterno": None,
                "CodigoExterno2": None,
                "InscricaoJuntac": None,
                "Agenciarod": None,
                "NumeracaoMatriz": None,
                "DadosMatriz": None,
                "EstabelecimentoEmissor": None,
                "NumeroN": None,
                "AmbienteBpe": None,
                "AgenciaBloqueada": 0,
                "MensagemAlerta": "",
                "EstornarComissaoDevolucao": 0.0,
                "TaxaEmbarqueRepasse": False,
                "ComputarTaxaPorDataViagem": False,
                "ComputarTxRepasseVoucher": False,
                "LocalidadeRepasseTaxaEmbarque": 0,
                "DescLocalidadeRepasse": None,
                "ListarEstabSemGrupoAgencia": False,
                "IdGrupoAgencia": None,
                "NaoCobrarTaxaEmbarque": False,
                "EmpresaTef": 0,
                "ImprimirTxEmbW2iSemDll": False,
                "BloqueioPartidas": False,
                "PartidasBloqueio": False,
                "GeraQrcodePix": 0,
                "TipoChavePix": 0,
                "ChavePix": None,
                "ECommerce": 0,
                "OcultarAgenciaRelatorio": False,
                "PagamentoPixTef": False,
                "VendaManualCartao": False,
                "VendaManualPix": False,
                "UtilizaSolAutReimp": None,
                "UtilizaSolAutReimpExcesso": None,
                "PassagemWhatsapp": False,
                "CelularWhatsapp": None,
                "TokenPlugSocial": None,
                "CadastraTerminalTef": 0,
                "ListaTerminalTef": None,
                "ImpressaoPadrao": 0,
                "AbrirCaixaAuto": 0,
                "RetornoSimples": False,
                "OrigemPadrao": 0,
                "SerieNaoFiscal": "",
                "DadosEmissaoBPe": None,
                "BlocoEletronico": 0,
                "QtdCupomEmbarqueNFiscal": None,
                "ControlarSaldoFechamento": 0,
                "DadosControlarSaldoFechamento": None,
                "ExibirStatusImpressora": None,
                "TaxaConveniencia": None,
                "PercTaxaConveniencia": None,
                "MensagemTaxaConveniencia": None,
                "AliquotaIssTaxaConveniencia": None,
                "BlocoBarco": 0,
                "TokenFidelidade": None,
                "NaoAplicarTxEmbarque": False,
                "Latitude": "",
                "Longitude": "",
                "ChavePixBB": None,
                "DevAppKeyPixBB": None,
                "ClientIdPixBB": None,
                "ClientSecretPixBB": None,
                "BasicTokenPixBB": None,
                "UrlLoginPixBB": None,
                "UrlApiPixBB": None,
                "ConsiderarApenasBPe": False,
                "ReplicarCertificadoGrupoAgencia": False,
                "ComissaoDataAcerto": False,
                "DataComissao": None,
                "NaoEmbApenasSolAut": False,
                "ImprimeBlocoEletronicoPdf": False,
                "ApuracaoComissaoConsulta": False,
                "PdvFidelidade": False,
            },
            "GaragemXml": {
                "IDGaragem": 1,
                "Descricao": "Test Garage",
                "Situacao": 1,
                "CodigoExterno": 1,
            },
            "EmpresaXml": {
                "NomeFantasia": "Test Company",
                "RazaoSocial": "Test Company LTDA",
                "Email": None,
                "Uf": "SP",
                "Cnpj": "12345678901234",
                "IMunicipal": "",
                "IEstadual": "",
                "Endereco": "Test Street",
                "Numero": "123",
                "Complemento": "Suite 1",
                "Bairro": "Test District",
                "IdCidade": 1,
                "NomeCidade": "Test City",
                "Cep": "12345678",
                "Telefone": None,
                "TokenFidelidade": "",
                "CodigoIndicacaoFidelidade": "",
                "Numero1": "",
                "PoliticaPrivacidade": "",
            },
            "IdPerfilOperador": 1,
            "Xml": None,
            "IdErro": "",
            "Mensagem": "",
            "MensagemDetalhada": "",
        },
    )

    response = await client.efetua_login(
        nome="test",
        senha="test123",
        cliente="CLIENTE1",
        empresa="EMPRESA1",
    )

    assert response == LoginResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0.0,
        Sucesso=True,
        Advertencia=False,
        ParametrosEntrada={},
        Comissao=0.0,
        RelatorioAgencia=0,
        Telefone=None,
        Email=None,
        MaxDescManual=0.0,
        MaxAcresManual=0.0,
        PermDescManualOperador=False,
        PermAcresManualOperador=False,
        DescontoPercentual=0.0,
        AcrescimoPercentual=0.0,
        IdOperador=1,
        IdSessaoOp="ABC123",
        Nome="Test User",
        Super=0,
        TempoInativarOperadorAposTempo=0,
        TempoSessao=0,
        VisualizouLGPD=None,
        IdJuncao=0,
        RelSuperOp=False,
        LiberarCancTEF=False,
        LiberarReimpPass=False,
        LiberarMarcarVoucher=False,
        InativarOperadorAposTempo=False,
        VisualizouTermoLGPD=None,
        VendDevOutroCaixaSemCaixa=False,
        UsuarioConsulta=False,
        ApenasBiometria=None,
        AlterarSenhaOperadorXDias=False,
        TempoAlterarSenhaOperadorXDias=0,
        PassouQtdDiasAlterarSenha=False,
        ObrigaCadastroPos=None,
        EstabelecimentoPadrao=0,
        EstabelecimentoXml=response.EstabelecimentoXml,
        GaragemXml=response.GaragemXml,
        EmpresaXml=response.EmpresaXml,
        IdPerfilOperador=1,
        Xml=None,
        IdErro="",
        Mensagem="",
        MensagemDetalhada="",
    )


async def test_partidas_origens(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Partidas/Origens",
        json={
            "Xml": {
                "NewDataSet": {
                    "Table": [
                        {
                            "IdLocalidade": "1",
                            "Descricao": "CURITIBA",
                            "IdCidade": "1",
                        }
                    ]
                }
            }
        },
    )

    origens = await client.partidas_origens("ABC123")

    assert origens == [
        Localidade(
            IdLocalidade="1",
            Descricao="CURITIBA",
            IdCidade="1",
        )
    ]


async def test_partidas_destinos(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Partidas/Origens",
        json={
            "Xml": {
                "NewDataSet": {
                    "Table": [
                        {
                            "IdLocalidade": "2",
                            "Descricao": "FLORIANOPOLIS",
                            "IdCidade": "2",
                        }
                    ]
                }
            }
        },
    )

    destinos = await client.partidas_destinos("ABC123", 1)

    assert len(destinos) == 1
    assert destinos[0].IdLocalidade == "2"
    assert destinos[0].Descricao == "FLORIANOPOLIS"


async def test_partidas(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Partidas/Partidas",
        json={
            "ParametrosEntrada": {},
            "ListaPartidas": [],
            "ListaMultiempresas": [],
            "ListaLogos": [],
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0.0,
            "Sucesso": True,
            "Advertencia": False,
            "Xml": {
                "NewDataSet": {
                    "Table": [
                        {
                            "IdViagem": 123,
                            "DataPartida": "2025-01-15T06:00:00",
                            "DataChegada": "2025-01-15T12:00:00",
                            "IdTipoVeiculo": 1,
                            "Lugares": 42,
                            "LugaresDisponiveis": 30,
                        }
                    ]
                }
            },
            "IdErro": "",
            "Mensagem": "",
            "MensagemDetalhada": "",
        },
    )

    partidas = await client.partidas(
        id_sessao_op="ABC123",
        id_estabelecimento=123,
        localidade_origem=1,
        localidade_destino=2,
        data_partida=datetime(2025, 1, 15, 6, 0),
    )

    assert partidas == PartidasResponse(
        ParametrosEntrada={},
        ListaPartidas=[],
        ListaMultiempresas=[],
        ListaLogos=[],
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0.0,
        Sucesso=True,
        Advertencia=False,
        Xml={
            "NewDataSet": {
                "Table": [
                    {
                        "IdViagem": 123,
                        "DataPartida": "2025-01-15T06:00:00",
                        "DataChegada": "2025-01-15T12:00:00",
                        "IdTipoVeiculo": 1,
                        "Lugares": 42,
                        "LugaresDisponiveis": 30,
                    }
                ]
            }
        },
        IdErro="",
        Mensagem="",
        MensagemDetalhada="",
    )


async def test_partidas_conexoes(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Partidas/Partidas",
        json={
            "ParametrosEntrada": {},
            "ListaPartidas": [
                {
                    "conexoes": [
                        {
                            "IdViagem": 147457,
                            "IdViagemComp": 0,
                            "IdTipoVeiculo": 384,
                            "tarifa": 196.9,
                            "Andares": 2,
                            "ControlaCliente": 0,
                            "TxEmbarque": 0,
                            "Seguro": 0,
                            "Pedagio": 0,
                            "Desconto": 49.22,
                            "Pricing": 0,
                            "VlTarifa": 196.9,
                            "viagemTFO": {
                                "IdEstabelecimentoLinha": 1000,
                                "ControlaPoltornaTrecho": 0,
                                "NomeLinha": "12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                                "CodigoOrigem": 3081,
                                "DescricaoOrigem": "CONCEICAO DAS ALAGOAS-MG",
                                "CodigoDestino": 3091,
                                "DescricaoDestino": "FLORIANOPOLIS-SC",
                                "DescricaoDestinoConexao": None,
                                "DataHoraInicio": "2025-05-27T02:00",
                                "DataHoraEmbarque": "2025-05-27T02:00",
                                "TempoViagem": "1290 min",
                                "StrDistanciaViagem": "1392 km",
                                "DistanciaViagem": 1392,
                                "DtaHoraChegada": "2025-05-27T23:30",
                                "ValorTarifa": "196.90",
                                "OfertaAPP5": 0,
                                "OfertaAP": 0,
                                "QtdPoltronas": 40,
                                "TipoHorario": "CONVENCIONAL",
                                "PercentualImposto": "0",
                                "ValorImposto": "0",
                                "PoltronasDisponiveis": 33,
                                "IdLinha": "GOSC0245001",
                                "IdVeiculo": None,
                                "Motorista": None,
                                "Empresa": None,
                                "Filial": None,
                                "HoraPartida": "0200",
                                "HoraTolerancia": "0200",
                                "TipoLinha": 1,
                                "UFDestino": "SC",
                                "ObsLinha": "",
                                "ObsLinhaAux": "",
                                "GratuidadeDisponivel": 0,
                                "ListaGratuidades": None,
                                "Plataforma": None,
                                "UFOrigem": "MG",
                                "TipoServico": None,
                                "DuracaoViagem": "21:30",
                            },
                            "ControlaPoltrona": 0,
                            "cPOL": 1,
                            "idCodigoLinha": 10822,
                            "Extra": 0,
                            "Servico": 1245,
                            "CodigoLinha": "GOSC0245001",
                            "Gerenciamento": {
                                "IdGerenciamento": 72,
                                "Descricao": "ANTT DIAMANTE CONV",
                                "IdTipo": 0,
                                "IdadeMaxColo": 6,
                                "UrlConsAgr": None,
                                "UsuarioAgr": None,
                                "SenhaAgr": None,
                                "NaoMarcarIDJovemMesmaUF": 0,
                                "ArredondaTarifa": 0,
                                "ORGAO_INTEREST": 1,
                                "QTDPOLTRESDEF_GEREN": 2,
                                "QTDPOLTRESDEF50_GEREN": 0,
                                "QTDPOLTRESMUL_GEREN": 0,
                                "QTDPOLTSRESIDOSO50_GEREN": 1,
                                "QTDPOLTRESIDOSO100_GEREN": 2,
                                "QTDPOLTRESDESCONT50_GEREN": 1,
                                "QTDPOLTRESDESCON100_GEREN": 0,
                                "QTDPOLTRESESTUDANTE50_GEREN": 0,
                                "QTDPOLTRESESTUDANTE100_GEREN": 0,
                                "QTDPOLTRESPOLICIALMILITAR100_GEREN": 0,
                                "NAOPERMIDOSO50VENDA_GEREN": False,
                                "NAOPERMIDOSO100VENDA_GEREN": False,
                                "NAOPERMDEFICIENTEVENDA_GEREN": False,
                                "NAOPERMCRIANCAVENDA_GEREN": False,
                                "NAOPERMPOLICIAVENDA_GEREN": True,
                                "NAOPERMESTUDANTE50VENDA_GEREN": True,
                                "NAOPERMESTUDANTE100VENDA_GEREN": True,
                                "NAOPERMPROFESSORVENDA_GEREN": True,
                                "NaoPermJovem50_Geren": False,
                                "NaoPermJovem100_Geren": False,
                                "NumpoltronaEstudante50Gerenciamento": "",
                                "NumpoltronaEstudante100Gerenciamento": "",
                                "NumpoltronaIdoso50Gerenciamento": "",
                                "NumpoltronaIdoso100Gerenciamento": "",
                                "NumpoltronaJovem50Gerenciamento": "",
                                "NumpoltronaJovem100Gerenciamento": "",
                                "NumpoltronaDeficienteGerenciamento": "",
                                "NumpoltronaEspacoMulherGerenciamento": "",
                                "NumpoltronaDeficienteGerenciamento50": "",
                                "NumpoltronaPolicialMilitarGerenciamento": "",
                                "PermVenderApenasPolReserv_Geren": False,
                                "ObrigaNascimento": 0,
                                "UrlServidorWS": "",
                                "CodigoBilhetagem": "",
                                "NAOOBSQTDIDOSO50VENDA_GEREN": False,
                                "PERMITIRVENDAIDOSODIAS_GEREN": False,
                                "VENDAIDOSODIAS_GEREN": 0,
                                "ControleGratuidades": None,
                                "ObrigaCPFVenda": 0,
                                "NaoPermVendaOfJust": False,
                                "NaoPermVendaPassLivre": False,
                            },
                            "CodigoBilhetagem": "",
                            "DescConexao": "CDA x FLO",
                            "DataViagem": "2025-05-27T00:00:00",
                            "HoraViagem": "0200",
                            "TipoViagem": 0,
                            "QtdLugares": 40,
                            "CodigoOrigem": 3081,
                            "CodigoDestino": 3091,
                            "Sentido": 1,
                            "IdLocalidade": 3081,
                            "DadosViagem": "02:00 - R$ 196.90 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                            "IdRota": 413,
                            "LayoutBarco": 0,
                            "CobraTaxa": True,
                            "CobraPedagio": False,
                            "CobraSeguro": False,
                            "CobraOutros": True,
                            "TxConveniencia": 0,
                            "Outros": 0,
                        }
                    ],
                    "TotalConexao": 409,
                    "DescontoConexao": 0,
                    "DescConexao": "GYN x CDA",
                    "PagamentosNaoPermitidos": [],
                    "IdRota": 413,
                    "TipoConexao": 1,
                    "DescTipoConexao": "Escala",
                    "IdDesconto": 460,
                    "Pricing": 0,
                    "ChavePricing": None,
                    "VlTarifaAnterior": 199,
                    "ValorMaiorDesconto": 100.61,
                    "EstabelecimentoEmissor": None,
                    "IdViagem": 147457,
                    "IdViagemComp": 0,
                    "TipoVeiculo": 384,
                    "IdLocalidade": 270,
                    "Tarifa": 212.1,
                    "CobrarTxEmbarque": 1,
                    "DadosViagem": "17:30 - R$ 212.10 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                    "ViagemTFO": {
                        "IdEstabelecimentoLinha": 1000,
                        "ControlaPoltornaTrecho": 1,
                        "NomeLinha": "12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                        "CodigoOrigem": 270,
                        "DescricaoOrigem": "GOIANIA - GO",
                        "CodigoDestino": 3081,
                        "DescricaoDestino": "FLORIANOPOLIS-SC",
                        "DescricaoDestinoConexao": "CONCEICAO DAS ALAGOAS-MG",
                        "DataHoraInicio": "2025-05-26T17:30",
                        "DataHoraEmbarque": "2025-05-26T17:30",
                        "TempoViagem": "510 min",
                        "StrDistanciaViagem": "405 km",
                        "DistanciaViagem": 405,
                        "DtaHoraChegada": "2025-05-27T02:00",
                        "ValorTarifa": "212.10",
                        "OfertaAPP5": 0,
                        "OfertaAP": 0,
                        "QtdPoltronas": 40,
                        "TipoHorario": "CONVENCIONAL",
                        "PercentualImposto": "0",
                        "ValorImposto": "0",
                        "PoltronasDisponiveis": 33,
                        "IdLinha": "GOSC0245001",
                        "IdVeiculo": "",
                        "Motorista": "",
                        "Empresa": None,
                        "Filial": None,
                        "HoraPartida": "1730",
                        "HoraTolerancia": "1730",
                        "TipoLinha": 1,
                        "UFDestino": "SC",
                        "ObsLinha": "",
                        "ObsLinhaAux": "",
                        "GratuidadeDisponivel": 3,
                        "ListaGratuidades": [
                            {
                                "IdTipoPassageiro": None,
                                "Descricao": "Total Gratuidades",
                                "Qtd": 3,
                            },
                            {
                                "IdTipoPassageiro": 4,
                                "Descricao": "Idoso 100%",
                                "Qtd": 1,
                            },
                            {
                                "IdTipoPassageiro": 7,
                                "Descricao": "Deficiente 100%",
                                "Qtd": 2,
                            },
                            {
                                "IdTipoPassageiro": 11,
                                "Descricao": "Jovem 100%",
                                "Qtd": 0,
                            },
                            {
                                "IdTipoPassageiro": 12,
                                "Descricao": "Estudante 100%",
                                "Qtd": 0,
                            },
                        ],
                        "Plataforma": None,
                        "UFOrigem": "GO",
                        "TipoServico": 0,
                        "DuracaoViagem": "08:30",
                    },
                    "TxEmbarque": 6.55,
                    "Seguro": 0,
                    "Pedagio": 0,
                    "Desconto": 51.39,
                    "VlTarifa": 205.55,
                    "Andares": 2,
                    "ControlaCliente": 0,
                    "ControlaPoltrona": 1,
                    "CPOL": 1,
                    "IdCodigoLinha": 10822,
                    "CodigoLinha": "GOSC0245001",
                    "Extra": 0,
                    "Servico": 1245,
                    "NaoVerifIdoso50": 0,
                    "NaoVerifIdoso100": 0,
                    "NaoVerifDeficiente": 0,
                    "Comentario": None,
                    "Gerenciamento": {
                        "IdGerenciamento": 72,
                        "Descricao": "ANTT DIAMANTE CONV",
                        "IdTipo": 0,
                        "IdadeMaxColo": 6,
                        "UrlConsAgr": None,
                        "UsuarioAgr": None,
                        "SenhaAgr": None,
                        "NaoMarcarIDJovemMesmaUF": 0,
                        "ArredondaTarifa": 0,
                        "ORGAO_INTEREST": 1,
                        "QTDPOLTRESDEF_GEREN": 2,
                        "QTDPOLTRESDEF50_GEREN": 0,
                        "QTDPOLTRESMUL_GEREN": 0,
                        "QTDPOLTSRESIDOSO50_GEREN": 1,
                        "QTDPOLTRESIDOSO100_GEREN": 2,
                        "QTDPOLTRESDESCONT50_GEREN": 1,
                        "QTDPOLTRESDESCON100_GEREN": 0,
                        "QTDPOLTRESESTUDANTE50_GEREN": 0,
                        "QTDPOLTRESESTUDANTE100_GEREN": 0,
                        "QTDPOLTRESPOLICIALMILITAR100_GEREN": 0,
                        "NAOPERMIDOSO50VENDA_GEREN": False,
                        "NAOPERMIDOSO100VENDA_GEREN": False,
                        "NAOPERMDEFICIENTEVENDA_GEREN": False,
                        "NAOPERMCRIANCAVENDA_GEREN": False,
                        "NAOPERMPOLICIAVENDA_GEREN": True,
                        "NAOPERMESTUDANTE50VENDA_GEREN": True,
                        "NAOPERMESTUDANTE100VENDA_GEREN": True,
                        "NAOPERMPROFESSORVENDA_GEREN": True,
                        "NaoPermJovem50_Geren": False,
                        "NaoPermJovem100_Geren": False,
                        "NumpoltronaEstudante50Gerenciamento": "",
                        "NumpoltronaEstudante100Gerenciamento": "",
                        "NumpoltronaIdoso50Gerenciamento": "",
                        "NumpoltronaIdoso100Gerenciamento": "",
                        "NumpoltronaJovem50Gerenciamento": "",
                        "NumpoltronaJovem100Gerenciamento": "",
                        "NumpoltronaDeficienteGerenciamento": "",
                        "NumpoltronaEspacoMulherGerenciamento": "",
                        "NumpoltronaDeficienteGerenciamento50": "",
                        "NumpoltronaPolicialMilitarGerenciamento": "",
                        "PermVenderApenasPolReserv_Geren": False,
                        "ObrigaNascimento": 0,
                        "UrlServidorWS": "",
                        "CodigoBilhetagem": "",
                        "NAOOBSQTDIDOSO50VENDA_GEREN": False,
                        "PERMITIRVENDAIDOSODIAS_GEREN": False,
                        "VENDAIDOSODIAS_GEREN": 0,
                        "ControleGratuidades": None,
                        "ObrigaCPFVenda": 0,
                        "NaoPermVendaOfJust": False,
                        "NaoPermVendaPassLivre": False,
                    },
                    "CodigoBilhetagem": "",
                    "LayoutBarco": 0,
                    "BloqDescontoManual": 0,
                    "BloqAcrescimoManual": 0,
                    "PermissaoDescManualEmpresa": -1,
                    "TxConveniencia": 0,
                    "SemPoltronaWeb": 0,
                    "LinhaAlias": None,
                    "Outros": 0,
                    "TxEmbCortesia": 0,
                    "NaoPermitirReserva": False,
                    "DescPricing": None,
                }
            ],
            "ListaMultiempresas": [],
            "ListaLogos": [],
            "Xml": None,
            "IdErro": None,
            "Mensagem": None,
            "MensagemDetalhada": None,
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0,
            "Sucesso": False,
            "Advertencia": False,
        },
    )

    partidas = await client.partidas(
        id_sessao_op="ABC123",
        id_estabelecimento=123,
        localidade_origem=1,
        localidade_destino=2,
        data_partida=datetime(2025, 1, 15, 6, 0),
    )

    assert partidas == PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0,
        Sucesso=False,
        Advertencia=False,
        ParametrosEntrada={},
        ListaPartidas=[
            Partida(
                ViagemTFO=ViagemTFO(
                    IdEstabelecimentoLinha=1000,
                    ControlaPoltornaTrecho=1,
                    NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                    CodigoOrigem=270,
                    DescricaoOrigem="GOIANIA - GO",
                    CodigoDestino=3081,
                    DescricaoDestino="FLORIANOPOLIS-SC",
                    DataHoraInicio="2025-05-26T17:30",
                    DataHoraEmbarque="2025-05-26T17:30",
                    TempoViagem="510 min",
                    StrDistanciaViagem="405 km",
                    DistanciaViagem=405,
                    DtaHoraChegada="2025-05-27T02:00",
                    ValorTarifa="212.10",
                    TipoHorario="CONVENCIONAL",
                    UFOrigem="GO",
                    UFDestino="SC",
                    DescricaoDestinoConexao="CONCEICAO DAS ALAGOAS-MG",
                    OfertaAPP5=0,
                    OfertaAP=0,
                    QtdPoltronas=40,
                    PoltronasDisponiveis=33,
                    IdLinha="GOSC0245001",
                    IdVeiculo="",
                    Motorista="",
                    Empresa=None,
                    Filial=None,
                    HoraPartida="1730",
                    HoraTolerancia="1730",
                    TipoLinha=1,
                    ObsLinha="",
                    ObsLinhaAux="",
                    GratuidadeDisponivel=3,
                    ListaGratuidades=[
                        {
                            "IdTipoPassageiro": None,
                            "Descricao": "Total Gratuidades",
                            "Qtd": 3,
                        },
                        {"IdTipoPassageiro": 4, "Descricao": "Idoso 100%", "Qtd": 1},
                        {
                            "IdTipoPassageiro": 7,
                            "Descricao": "Deficiente 100%",
                            "Qtd": 2,
                        },
                        {"IdTipoPassageiro": 11, "Descricao": "Jovem 100%", "Qtd": 0},
                        {
                            "IdTipoPassageiro": 12,
                            "Descricao": "Estudante 100%",
                            "Qtd": 0,
                        },
                    ],
                    Plataforma=None,
                    DuracaoViagem="08:30",
                    TipoServico=0,
                ),
                IdViagem=147457,
                IdViagemComp=0,
                TipoVeiculo=384,
                IdLocalidade=270,
                Gerenciamento=Gerenciamento(
                    IdGerenciamento=72,
                    Descricao="ANTT DIAMANTE CONV",
                    ArredondaTarifa=0,
                    IdadeMaxColo=6,
                    IdTipo=0,
                    IdCliente=None,
                    ImprimeMonitriip=None,
                    ORGAO_INTEREST=1,
                    EmpresaConveniada=None,
                    Comissao=None,
                    CodigoBilhetagem="",
                    SenhaBilhetagem=None,
                    UrlServidorWS="",
                    CodigoEmpresa=None,
                    UrlAgr=None,
                    UsuarioAgr=None,
                    SenhaAgr=None,
                    UrlConfAgr=None,
                    UrlConsAgr=None,
                    UrlCancAgr=None,
                    RESERVADEFIC_GEREN=0,
                ),
                conexoes=[
                    Conexao(
                        IdViagem=147457,
                        IdViagemComp=0,
                        IdTipoVeiculo=384,
                        tarifa=196.9,
                        Andares=2,
                        ControlaCliente=0,
                        TxEmbarque=0,
                        Seguro=0,
                        Pedagio=0,
                        Desconto=49.22,
                        Pricing=0,
                        VlTarifa=196.9,
                        viagemTFO=ViagemTFOConexao(
                            IdEstabelecimentoLinha=1000,
                            ControlaPoltornaTrecho=0,
                            NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                            CodigoOrigem=3081,
                            DescricaoOrigem="CONCEICAO DAS ALAGOAS-MG",
                            CodigoDestino=3091,
                            DescricaoDestino="FLORIANOPOLIS-SC",
                            DescricaoDestinoConexao=None,
                            DataHoraInicio="2025-05-27T02:00",
                            DataHoraEmbarque="2025-05-27T02:00",
                            TempoViagem="1290 min",
                            StrDistanciaViagem="1392 km",
                            DistanciaViagem=1392,
                            DtaHoraChegada="2025-05-27T23:30",
                            ValorTarifa="196.90",
                            OfertaAPP5=0,
                            OfertaAP=0,
                            QtdPoltronas=40,
                            TipoHorario="CONVENCIONAL",
                            PercentualImposto="0",
                            ValorImposto="0",
                            PoltronasDisponiveis=33,
                            IdLinha="GOSC0245001",
                            IdVeiculo=None,
                            Motorista=None,
                            Empresa=None,
                            Filial=None,
                            HoraPartida="0200",
                            HoraTolerancia="0200",
                            TipoLinha=1,
                            UFDestino="SC",
                            ObsLinha="",
                            ObsLinhaAux="",
                            GratuidadeDisponivel=0,
                            ListaGratuidades=None,
                            Plataforma=None,
                            UFOrigem="MG",
                            TipoServico=None,
                            DuracaoViagem="21:30",
                        ),
                        ControlaPoltrona=0,
                        cPOL=1,
                        idCodigoLinha=10822,
                        Extra=0,
                        Servico=1245,
                        CodigoLinha="GOSC0245001",
                        Gerenciamento=Gerenciamento(
                            IdGerenciamento=72,
                            Descricao="ANTT DIAMANTE CONV",
                            ArredondaTarifa=0,
                            IdadeMaxColo=6,
                            IdTipo=0,
                            IdCliente=None,
                            ImprimeMonitriip=None,
                            ORGAO_INTEREST=1,
                            EmpresaConveniada=None,
                            Comissao=None,
                            CodigoBilhetagem="",
                            SenhaBilhetagem=None,
                            UrlServidorWS="",
                            CodigoEmpresa=None,
                            UrlAgr=None,
                            UsuarioAgr=None,
                            SenhaAgr=None,
                            UrlConfAgr=None,
                            UrlConsAgr=None,
                            UrlCancAgr=None,
                            RESERVADEFIC_GEREN=0,
                        ),
                        CodigoBilhetagem="",
                        DescConexao="CDA x FLO",
                        DataViagem="2025-05-27T00:00:00",
                        HoraViagem="0200",
                        TipoViagem=0,
                        QtdLugares=40,
                        CodigoOrigem=3081,
                        CodigoDestino=3091,
                        Sentido=1,
                        IdLocalidade=3081,
                        DadosViagem="02:00 - R$ 196.90 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                        IdRota=413,
                        LayoutBarco=0,
                        CobraTaxa=True,
                        CobraPedagio=False,
                        CobraSeguro=False,
                        CobraOutros=True,
                        TxConveniencia=0,
                        Outros=0,
                    )
                ],
                TotalConexao=409,
                DescontoConexao=0,
                DescConexao="GYN x CDA",
                PagamentosNaoPermitidos=[],
                IdRota=413,
                TipoConexao=1,
                DescTipoConexao="Escala",
                IdDesconto=460,
                Pricing=0,
                VlTarifaAnterior=199,
                ValorMaiorDesconto=100.61,
                Tarifa=212.1,
                CobrarTxEmbarque=1,
                DadosViagem="17:30 - R$ 212.10 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                EstabelecimentoEmissor=None,
                TxEmbarque=6.55,
                Seguro=0,
                Pedagio=0,
                Desconto=51.39,
                VlTarifa=205.55,
                Andares=2,
                ControlaCliente=0,
                ControlaPoltrona=1,
                CPOL=1,
                IdCodigoLinha=10822,
                CodigoLinha="GOSC0245001",
                Extra=0,
                Servico=1245,
                NaoVerifIdoso50=0,
                NaoVerifIdoso100=0,
                NaoVerifDeficiente=0,
                Comentario=None,
                CodigoBilhetagem="",
                LayoutBarco=0,
                BloqDescontoManual=0,
                BloqAcrescimoManual=0,
                PermissaoDescManualEmpresa=-1,
                TxConveniencia=0,
                SemPoltronaWeb=0,
                LinhaAlias=None,
                Outros=0,
                PedagioVolta=None,
                ChavePricing=None,
                TxEmbCortesia=0,
                NaoPermitirReserva=False,
                DescPricing=None,
            )
        ],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem=None,
        MensagemDetalhada=None,
    )


async def test_retorna_poltronas(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Poltrona/retornaPoltronas",
        json={
            "ParametrosEntrada": {},
            "QtdDisponivel": 30,
            "QtdGratuidade": 0,
            "NumeroPoltrona": 1,
            "Situacao": 1,
            "Caption": "Disponível",
            "IntCaption": 1,
            "Marcada": False,
            "IdReserva": None,
            "LaypoltronaXml": {
                "PoltronaXmlRetorno": [
                    {
                        "ParametrosEntrada": {},
                        "QtdDisponivel": 30,
                        "QtdGratuidade": 0,
                        "NumeroPoltrona": 1,
                        "Situacao": 1,
                        "Caption": "Disponível",
                        "IntCaption": 1,
                        "Marcada": False,
                        "IdReserva": None,
                        "ReservaSite": False,
                        "Sucesso": True,
                        "Advertencia": False,
                    }
                ]
            },
        },
    )

    poltronas = await client.retorna_poltronas(
        id_sessao_op="ABC123",
        id_viagem=123,
        id_tipo_veiculo=1,
        id_loc_origem=1,
        id_loc_destino=2,
    )

    assert poltronas == RetornaPoltronasResponse(
        ParametrosEntrada={},
        QtdDisponivel=30,
        QtdGratuidade=0,
        NumeroPoltrona=1,
        Situacao=1,
        Caption="Disponível",
        IntCaption=1,
        Marcada=False,
        IdReserva=None,
        LaypoltronaXml=LaypoltronaXml(
            PoltronaXmlRetorno=[
                Poltrona(
                    ParametrosEntrada={},
                    QtdDisponivel=30,
                    QtdGratuidade=0,
                    NumeroPoltrona=1,
                    Situacao=1,
                    Caption="Disponível",
                    IntCaption=1,
                    Marcada=False,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                )
            ]
        ),
    )


async def test_verifica_poltrona(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Poltrona/VerificaPoltrona",
        json={
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0.0,
            "Sucesso": True,
            "Advertencia": False,
            "Marcada": True,
            "IdErro": "",
            "Mensagem": None,
        },
    )

    response = await client.verifica_poltrona(
        id_sessao_op="ABC123",
        id_viagem=123,
        id_tipo_veiculo=1,
        id_loc_origem=1,
        id_loc_destino=2,
        andar=1,
        id_poltrona=1,
        bloqueia=1,
    )

    assert response == VerificaPoltronaResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0.0,
        Sucesso=True,
        Advertencia=False,
        Marcada=True,
        IdErro="",
        Mensagem=None,
    )


async def test_desmarca_poltrona(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Poltrona/DesmarcaPoltrona",
        json={
            "ParametrosEntrada": {},
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0.0,
            "Sucesso": True,
            "Advertencia": False,
            "Marcada": False,
            "IdErro": "",
            "Mensagem": None,
        },
    )

    response = await client.desmarca_poltrona(
        id_sessao_op="ABC123",
        id_viagem=123,
        id_tipo_veiculo=1,
        id_loc_origem=1,
        id_loc_destino=2,
        andar=1,
        id_poltrona=1,
    )

    assert response.Marcada is False
    assert response.IdErro == ""


async def test_error_handling(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Login/EfetuaLogin",
        json={
            "IdSessaoOp": None,
            "IdPerfilOperador": None,
            "Xml": None,
            "IdErro": "AUTH001",
            "Mensagem": "Invalid credentials",
            "MensagemDetalhada": "The provided username or password is incorrect",
        },
    )

    with pytest.raises(PraxioClientError) as exc_info:
        await client.efetua_login(
            nome="test",
            senha="wrong",
            cliente="CLIENTE1",
            empresa="EMPRESA1",
        )

    assert exc_info.value.error_id == "AUTH001"
    assert exc_info.value.message == "Invalid credentials"


async def test_lista_partidas_tfo(httpx_mock: HTTPXMock, client: PraxioClient):
    httpx_mock.add_response(
        method="POST",
        url="https://praxio.test/Autumn/Partidas/listaPartidasTFO",
        json={
            "ParametrosEntrada": {},
            "ListaTrechosTFO": None,
            "ListaPartidasTFO": [
                {
                    "DataBloqSemPassageiro": "0001-01-01T00:00:00",
                    "HoraBloqSemPassageiro": None,
                    "IDViagem": 10920,
                    "DataPartida": "2025-06-18T21:00:00",
                    "HoraPartida": "2100",
                    "Sentido": 0,
                    "Plataforma": "",
                    "HoraChegada": None,
                    "ControlaPoltrona": 0,
                    "ControlaPassageiros": 0,
                    "ControlaCliente": 0,
                    "Localidade": {
                        "ListEstabelecimentos": None,
                        "BilheteEmbW2i": 1,
                        "IDLocalidade": 35,
                        "Descricao": "BARREIRAS",
                        "Sigla": "BES",
                        "IdRegiao": None,
                        "Uf": "BA",
                        "IdEstabelecimento": 0,
                        "IdCidade": 2903201,
                        "TxEmbIdoso50": 0,
                        "TxEmbIdoso100": 0,
                        "TxEmbPasseLivre": 0,
                        "PedagioIdoso100": 0,
                        "PedagioIdoso50": 0,
                        "Codigo": 0,
                        "AgenciasCargas": None,
                        "LastUpdate": None,
                        "TxPedagioPasseLivre": None,
                        "CodigoSgltar": 0,
                        "TxEmb50Idoso50": 0,
                        "TxEmb50Def50": 0,
                        "TxEmb50Estud50": 0,
                        "TxEmb50Jov50": 0,
                        "TxEmbIdosoDef": 0,
                        "CodigoSigma": None,
                        "TxEmbCortesia": 0,
                        "TxEmbJovem50": 0,
                        "TxEmbJovem100": 0,
                        "TxEmbAcompanhantePL": 0,
                    },
                    "DataChegada": "0001-01-01T00:00:00",
                    "Obs": None,
                    "HoraTolerancia": None,
                    "DataExibirViagem": "0001-01-01T00:00:00",
                    "HoraExibirViagem": None,
                    "QtdPoltronaBloqueio": 0,
                    "Comentario": "",
                },
                {
                    "DataBloqSemPassageiro": "0001-01-01T00:00:00",
                    "HoraBloqSemPassageiro": None,
                    "IDViagem": 10920,
                    "DataPartida": "2025-06-18T22:30:00",
                    "HoraPartida": "2230",
                    "Sentido": 0,
                    "Plataforma": "",
                    "HoraChegada": None,
                    "ControlaPoltrona": 0,
                    "ControlaPassageiros": 0,
                    "ControlaCliente": 0,
                    "Localidade": {
                        "ListEstabelecimentos": None,
                        "BilheteEmbW2i": 1,
                        "IDLocalidade": 34,
                        "Descricao": "LUIS EDUARDO MAGALHAES",
                        "Sigla": "MIOO",
                        "IdRegiao": None,
                        "Uf": "BA",
                        "IdEstabelecimento": 0,
                        "IdCidade": 2919553,
                        "TxEmbIdoso50": 0,
                        "TxEmbIdoso100": 0,
                        "TxEmbPasseLivre": 0,
                        "PedagioIdoso100": 0,
                        "PedagioIdoso50": 0,
                        "Codigo": 0,
                        "AgenciasCargas": None,
                        "LastUpdate": None,
                        "TxPedagioPasseLivre": None,
                        "CodigoSgltar": 0,
                        "TxEmb50Idoso50": 0,
                        "TxEmb50Def50": 0,
                        "TxEmb50Estud50": 0,
                        "TxEmb50Jov50": 0,
                        "TxEmbIdosoDef": 0,
                        "CodigoSigma": None,
                        "TxEmbCortesia": 0,
                        "TxEmbJovem50": 0,
                        "TxEmbJovem100": 0,
                        "TxEmbAcompanhantePL": 0,
                    },
                    "DataChegada": "0001-01-01T00:00:00",
                    "Obs": None,
                    "HoraTolerancia": None,
                    "DataExibirViagem": "0001-01-01T00:00:00",
                    "HoraExibirViagem": None,
                    "QtdPoltronaBloqueio": 0,
                    "Comentario": "",
                },
                {
                    "DataBloqSemPassageiro": "0001-01-01T00:00:00",
                    "HoraBloqSemPassageiro": None,
                    "IDViagem": 10920,
                    "DataPartida": "2025-06-19T02:45:00",
                    "HoraPartida": "0245",
                    "Sentido": 0,
                    "Plataforma": "",
                    "HoraChegada": None,
                    "ControlaPoltrona": 0,
                    "ControlaPassageiros": 0,
                    "ControlaCliente": 0,
                    "Localidade": {
                        "ListEstabelecimentos": None,
                        "BilheteEmbW2i": 0,
                        "IDLocalidade": 33,
                        "Descricao": "ALVORADA DO NORTE",
                        "Sigla": "AVN",
                        "IdRegiao": None,
                        "Uf": "GO",
                        "IdEstabelecimento": 0,
                        "IdCidade": 5200803,
                        "TxEmbIdoso50": 0,
                        "TxEmbIdoso100": 0,
                        "TxEmbPasseLivre": 0,
                        "PedagioIdoso100": 0,
                        "PedagioIdoso50": 0,
                        "Codigo": 0,
                        "AgenciasCargas": None,
                        "LastUpdate": None,
                        "TxPedagioPasseLivre": None,
                        "CodigoSgltar": 0,
                        "TxEmb50Idoso50": 0,
                        "TxEmb50Def50": 0,
                        "TxEmb50Estud50": 0,
                        "TxEmb50Jov50": 0,
                        "TxEmbIdosoDef": 0,
                        "CodigoSigma": None,
                        "TxEmbCortesia": 0,
                        "TxEmbJovem50": 0,
                        "TxEmbJovem100": 0,
                        "TxEmbAcompanhantePL": 0,
                    },
                    "DataChegada": "0001-01-01T00:00:00",
                    "Obs": None,
                    "HoraTolerancia": None,
                    "DataExibirViagem": "0001-01-01T00:00:00",
                    "HoraExibirViagem": None,
                    "QtdPoltronaBloqueio": 0,
                    "Comentario": "",
                },
            ],
            "Xml": None,
            "IdErro": None,
            "Mensagem": None,
            "MensagemDetalhada": None,
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0,
            "Sucesso": False,
            "Advertencia": False,
        },
    )

    response = await client.lista_partidas_tfo(id_sessao_op="ABC123", id_viagem=123)

    assert response == ListaPartidasTFOResponse(
        ParametrosEntrada={},
        ListaTrechosTFO=None,
        ListaPartidasTFO=[
            PartidaTFO(
                DataBloqSemPassageiro="0001-01-01T00:00:00",
                HoraBloqSemPassageiro=None,
                IDViagem=10920,
                DataPartida="2025-06-18T21:00:00",
                HoraPartida="2100",
                Sentido=0,
                Plataforma="",
                HoraChegada=None,
                ControlaPoltrona=0,
                ControlaPassageiros=0,
                ControlaCliente=0,
                Localidade=LocalidadePartida(
                    ListEstabelecimentos=None,
                    BilheteEmbW2i=1,
                    IDLocalidade=35,
                    Descricao="BARREIRAS",
                    Sigla="BES",
                    IdRegiao=None,
                    Uf="BA",
                    IdEstabelecimento=0,
                    IdCidade=2903201,
                    TxEmbIdoso50=0,
                    TxEmbIdoso100=0,
                    TxEmbPasseLivre=0,
                    PedagioIdoso100=0,
                    PedagioIdoso50=0,
                    Codigo=0,
                    AgenciasCargas=None,
                    LastUpdate=None,
                    TxPedagioPasseLivre=None,
                    CodigoSgltar=0,
                    TxEmb50Idoso50=0,
                    TxEmb50Def50=0,
                    TxEmb50Estud50=0,
                    TxEmb50Jov50=0,
                    TxEmbIdosoDef=0,
                    CodigoSigma=None,
                    TxEmbCortesia=0,
                    TxEmbJovem50=0,
                    TxEmbJovem100=0,
                    TxEmbAcompanhantePL=0,
                ),
                DataChegada="0001-01-01T00:00:00",
                Obs=None,
                HoraTolerancia=None,
                DataExibirViagem="0001-01-01T00:00:00",
                HoraExibirViagem=None,
                QtdPoltronaBloqueio=0,
                Comentario="",
            ),
            PartidaTFO(
                DataBloqSemPassageiro="0001-01-01T00:00:00",
                HoraBloqSemPassageiro=None,
                IDViagem=10920,
                DataPartida="2025-06-18T22:30:00",
                HoraPartida="2230",
                Sentido=0,
                Plataforma="",
                HoraChegada=None,
                ControlaPoltrona=0,
                ControlaPassageiros=0,
                ControlaCliente=0,
                Localidade=LocalidadePartida(
                    ListEstabelecimentos=None,
                    BilheteEmbW2i=1,
                    IDLocalidade=34,
                    Descricao="LUIS EDUARDO MAGALHAES",
                    Sigla="MIOO",
                    IdRegiao=None,
                    Uf="BA",
                    IdEstabelecimento=0,
                    IdCidade=2919553,
                    TxEmbIdoso50=0,
                    TxEmbIdoso100=0,
                    TxEmbPasseLivre=0,
                    PedagioIdoso100=0,
                    PedagioIdoso50=0,
                    Codigo=0,
                    AgenciasCargas=None,
                    LastUpdate=None,
                    TxPedagioPasseLivre=None,
                    CodigoSgltar=0,
                    TxEmb50Idoso50=0,
                    TxEmb50Def50=0,
                    TxEmb50Estud50=0,
                    TxEmb50Jov50=0,
                    TxEmbIdosoDef=0,
                    CodigoSigma=None,
                    TxEmbCortesia=0,
                    TxEmbJovem50=0,
                    TxEmbJovem100=0,
                    TxEmbAcompanhantePL=0,
                ),
                DataChegada="0001-01-01T00:00:00",
                Obs=None,
                HoraTolerancia=None,
                DataExibirViagem="0001-01-01T00:00:00",
                HoraExibirViagem=None,
                QtdPoltronaBloqueio=0,
                Comentario="",
            ),
            PartidaTFO(
                DataBloqSemPassageiro="0001-01-01T00:00:00",
                HoraBloqSemPassageiro=None,
                IDViagem=10920,
                DataPartida="2025-06-19T02:45:00",
                HoraPartida="0245",
                Sentido=0,
                Plataforma="",
                HoraChegada=None,
                ControlaPoltrona=0,
                ControlaPassageiros=0,
                ControlaCliente=0,
                Localidade=LocalidadePartida(
                    ListEstabelecimentos=None,
                    BilheteEmbW2i=0,
                    IDLocalidade=33,
                    Descricao="ALVORADA DO NORTE",
                    Sigla="AVN",
                    IdRegiao=None,
                    Uf="GO",
                    IdEstabelecimento=0,
                    IdCidade=5200803,
                    TxEmbIdoso50=0,
                    TxEmbIdoso100=0,
                    TxEmbPasseLivre=0,
                    PedagioIdoso100=0,
                    PedagioIdoso50=0,
                    Codigo=0,
                    AgenciasCargas=None,
                    LastUpdate=None,
                    TxPedagioPasseLivre=None,
                    CodigoSgltar=0,
                    TxEmb50Idoso50=0,
                    TxEmb50Def50=0,
                    TxEmb50Estud50=0,
                    TxEmb50Jov50=0,
                    TxEmbIdosoDef=0,
                    CodigoSigma=None,
                    TxEmbCortesia=0,
                    TxEmbJovem50=0,
                    TxEmbJovem100=0,
                    TxEmbAcompanhantePL=0,
                ),
                DataChegada="0001-01-01T00:00:00",
                Obs=None,
                HoraTolerancia=None,
                DataExibirViagem="0001-01-01T00:00:00",
                HoraExibirViagem=None,
                QtdPoltronaBloqueio=0,
                Comentario="",
            ),
        ],
    )
