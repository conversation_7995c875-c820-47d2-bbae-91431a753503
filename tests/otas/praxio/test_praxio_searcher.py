from datetime import date, datetime, timedelta, timezone
from unittest import mock
from unittest.mock import AsyncMock, MagicMock

import pytest

from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    PlaceStatus,
    Seat,
    SeatMap,
    Stopover,
    Travel,
)
from marketplace.otas.exception import (
    OTAInvalidParameters,
    OTASearcherTravelNotFound,
    OTATimeoutException,
)
from marketplace.otas.praxio.client import (
    Conexao,
    Gerenciamento,
    LaypoltronaXml,
    ListaPartidasTFOResponse,
    Localidade,
    LocalidadePartida,
    Partida,
    PartidasResponse,
    PartidaTFO,
    Poltrona,
    PraxioClient,
    RetornaPoltronasResponse,
    VerificaPoltronaResponse,
    ViagemTFO,
    ViagemTFOConexao,
)
from marketplace.otas.praxio.searcher import PraxioSearcher


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=3,
        name="Praxio",
        provider="praxio",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=3,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=3,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
        config={
            "nome": "Praxio",
            "senha": "123",
            "cliente": "Itapemirim",
            "empresa": "Itapemirim",
        },
    )


class PicklableMock(MagicMock):
    def __reduce__(self):
        return (PicklableMock, ())


@pytest.fixture
def mock_client():
    mock = AsyncMock(spec=PraxioClient)
    mock.efetua_login.return_value = PicklableMock(TempoSessao=11)
    return mock


@pytest.fixture
def searcher(ota_config, mock_client):
    searcher = PraxioSearcher(
        ota_config=ota_config,
        base_url="https://praxio.test",
        username="test",
        password="test",
        tenant_id="test",
    )
    searcher._session = mock_client

    return searcher


class FakePartida(Partida):
    def __init__(
        self,
        tx_embarque=10.00,
        seguro=10.00,
        pedagio=10.00,
        vl_tarifa=10.00,
        desconto=0.00,
        pricing=0.00,
        conexoes=None,
        total_conexao=0.00,
        valor_maior_desconto=0.00,
        desconto_conexao=0.00,
    ):
        self.conexoes = conexoes
        self.TotalConexao = total_conexao
        self.ValorMaiorDesconto = valor_maior_desconto
        self.DescontoConexao = desconto_conexao

        self.TxEmbarque = tx_embarque
        self.Seguro = seguro
        self.Pedagio = pedagio
        self.VlTarifa = vl_tarifa
        self.Desconto = desconto
        self.Pricing = pricing


def test_get_price():
    assert FakePartida().preco_final == 40.00


def test_get_price_desconto():
    assert FakePartida(desconto=30.00).preco_final == 10.00


def test_get_price_pricing():
    assert FakePartida(pricing=30.00).preco_final == 10.00


def test_get_price_conexao():
    assert (
        FakePartida(
            conexoes=[FakePartida()], total_conexao=100, valor_maior_desconto=-50
        ).preco_final
        == 150
    )
    assert (
        FakePartida(
            conexoes=[FakePartida()], total_conexao=100, desconto_conexao=50
        ).preco_final
        == 100
    )


async def test_search(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=3,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"IdLocalidade": 1001},
    )
    destination = OTAPlace(
        ota_config_id=3,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"IdLocalidade": 2001},
    )
    departure_date = date(2025, 1, 10)
    mock_client.partidas.return_value = PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0.0,
        Sucesso=True,
        Advertencia=False,
        ParametrosEntrada={"some_key": "some_value"},
        ListaPartidas=[
            Partida(
                ViagemTFO=ViagemTFO(
                    IdEstabelecimentoLinha=1,
                    ControlaPoltornaTrecho=1,
                    NomeLinha="Linha 123",
                    CodigoOrigem=101,
                    DescricaoOrigem="Origem Exemplo",
                    CodigoDestino=202,
                    DescricaoDestino="Destino Exemplo",
                    DataHoraInicio="2025-01-14T08:00:00",
                    DataHoraEmbarque="2025-01-14T08:30:00",
                    TempoViagem="2h 30m",
                    StrDistanciaViagem="150km",
                    DistanciaViagem=150,
                    DtaHoraChegada="2025-01-14T10:30:00",
                    ValorTarifa="R$50,00",
                    TipoHorario="Executivo",
                    UFOrigem="SP",
                    UFDestino="RJ",
                ),
                IdViagem=12345,
                IdViagemComp=67890,
                TipoVeiculo=1,
                IdLocalidade=1,
                Gerenciamento=Gerenciamento(
                    IdGerenciamento=1,
                    Descricao="Gerenciamento Exemplo",
                    ArredondaTarifa=0,
                    IdadeMaxColo=5,
                    IdTipo=1,
                ),
                conexoes=None,
                TotalConexao=0.0,
                DescontoConexao=0.0,
                DescConexao=None,
                PagamentosNaoPermitidos=[],
                IdRota=123,
                TipoConexao=0,
                DescTipoConexao=None,
                IdDesconto=0,
                Pricing=100.0,
                PedagioVolta=0.0,
                VlTarifaAnterior=50.0,
                ValorMaiorDesconto=5.0,
                Tarifa=45.0,
                CobrarTxEmbarque=0,
                DadosViagem="Detalhes da viagem",
                EstabelecimentoEmissor=None,
                TxEmbarque=5.0,
                Seguro=0.0,
                Pedagio=0.0,
                Desconto=0.0,
                VlTarifa=50.0,
                Andares=1,
                ControlaCliente=0,
                ControlaPoltrona=0,
                CPOL=0,
                IdCodigoLinha=123,
                CodigoLinha="Linha 123",
                Extra=0,
                Servico=1,
                NaoVerifIdoso50=0,
                NaoVerifIdoso100=0,
                NaoVerifDeficiente=0,
                Comentario=None,
                CodigoBilhetagem="123456",
                LayoutBarco=0,
                BloqDescontoManual=0,
                BloqAcrescimoManual=0,
                PermissaoDescManualEmpresa=0,
                TxConveniencia=0.0,
                SemPoltronaWeb=0,
                LinhaAlias=None,
                Outros=0.0,
                ChavePricing="/J/ywbLyB49+4sksGj04N5tb8LoV1SteCtyb/n9dk4Bi90x6qxL236YToGVogWQr",
            )
        ],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem="Operação realizada com sucesso.",
        MensagemDetalhada=None,
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="praxio",
        ota_config_id=3,
        code="12345",
        group_class_code="12345",
        service_code="12345",
        company=OTACompany(
            name="Test Company",
            external_id=1,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=3,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 14, 8, 00),
        arrival_at=datetime(2025, 1, 14, 10, 30),
        seat_type=OTASeatType(
            ota_config_id=3,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=-45.00,
        available_seats=0,
        total_seats=0,
        last_synced=mock.ANY,
        extra={
            "IdViagem": 12345,
            "TipoVeiculo": 1,
            "CodigoOrigem": 101,
            "CodigoDestino": 202,
            "Andares": 1,
            "TipoHorario": "Executivo",
            "TipoServico": None,
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-01-14T08:00:00",
            "ChavePricing": "/J/ywbLyB49+4sksGj04N5tb8LoV1SteCtyb/n9dk4Bi90x6qxL236YToGVogWQr",
        },
    )
    assert results[0] == expected_travel


async def test_search_stopovers(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=3,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"IdLocalidade": 1001},
    )
    destination = OTAPlace(
        ota_config_id=3,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"IdLocalidade": 2001},
    )
    departure_date = date(2025, 1, 10)
    mock_client.partidas.return_value = PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0,
        Sucesso=False,
        Advertencia=False,
        ParametrosEntrada={},
        ListaPartidas=[
            Partida(
                ViagemTFO=ViagemTFO(
                    IdEstabelecimentoLinha=1,
                    ControlaPoltornaTrecho=1,
                    NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                    CodigoOrigem=270,
                    DescricaoOrigem="GOIANIA - GO",
                    CodigoDestino=3081,
                    DescricaoDestino="FLORIANOPOLIS-SC",
                    DataHoraInicio="2025-05-26T17:30",
                    DataHoraEmbarque="2025-05-26T17:30",
                    TempoViagem="510 min",
                    StrDistanciaViagem="405 km",
                    DistanciaViagem=405,
                    DtaHoraChegada="2025-05-27T02:00",
                    ValorTarifa="212.10",
                    TipoHorario="Executivo",
                    UFOrigem="GO",
                    UFDestino="SC",
                    DescricaoDestinoConexao="CONCEICAO DAS ALAGOAS-MG",
                    OfertaAPP5=0,
                    OfertaAP=0,
                    QtdPoltronas=40,
                    PoltronasDisponiveis=33,
                    IdLinha="GOSC0245001",
                    IdVeiculo="",
                    Motorista="",
                    Empresa=None,
                    Filial=None,
                    HoraPartida="1730",
                    HoraTolerancia="1730",
                    TipoLinha=1,
                    ObsLinha="",
                    ObsLinhaAux="",
                    GratuidadeDisponivel=3,
                    ListaGratuidades=[],
                    Plataforma=None,
                    DuracaoViagem="08:30",
                    TipoServico=0,
                ),
                IdViagem=147457,
                IdViagemComp=0,
                TipoVeiculo=384,
                IdLocalidade=270,
                Gerenciamento=Gerenciamento(
                    IdGerenciamento=72,
                    Descricao="ANTT DIAMANTE CONV",
                    ArredondaTarifa=0,
                    IdadeMaxColo=6,
                    IdTipo=0,
                    IdCliente=None,
                    ImprimeMonitriip=None,
                    ORGAO_INTEREST=1,
                    EmpresaConveniada=None,
                    Comissao=None,
                    CodigoBilhetagem="",
                    SenhaBilhetagem=None,
                    UrlServidorWS="",
                    CodigoEmpresa=None,
                    UrlAgr=None,
                    UsuarioAgr=None,
                    SenhaAgr=None,
                    UrlConfAgr=None,
                    UrlConsAgr=None,
                    UrlCancAgr=None,
                    RESERVADEFIC_GEREN=0,
                ),
                conexoes=[
                    Conexao(
                        IdViagem=147457,
                        IdViagemComp=0,
                        IdTipoVeiculo=384,
                        tarifa=196.9,
                        Andares=2,
                        ControlaCliente=0,
                        TxEmbarque=0,
                        Seguro=0,
                        Pedagio=0,
                        Desconto=49.22,
                        Pricing=0,
                        VlTarifa=196.9,
                        viagemTFO=ViagemTFOConexao(
                            IdEstabelecimentoLinha=1,
                            ControlaPoltornaTrecho=0,
                            NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                            CodigoOrigem=3081,
                            DescricaoOrigem="CONCEICAO DAS ALAGOAS-MG",
                            CodigoDestino=3091,
                            DescricaoDestino="FLORIANOPOLIS-SC",
                            DescricaoDestinoConexao=None,
                            DataHoraInicio="2025-05-27T02:00",
                            DataHoraEmbarque="2025-05-27T02:00",
                            TempoViagem="1290 min",
                            StrDistanciaViagem="1392 km",
                            DistanciaViagem=1392,
                            DtaHoraChegada="2025-05-27T23:30",
                            ValorTarifa="196.90",
                            OfertaAPP5=0,
                            OfertaAP=0,
                            QtdPoltronas=40,
                            TipoHorario="Executivo",
                            PercentualImposto="0",
                            ValorImposto="0",
                            PoltronasDisponiveis=33,
                            IdLinha="GOSC0245001",
                            IdVeiculo=None,
                            Motorista=None,
                            Empresa=None,
                            Filial=None,
                            HoraPartida="0200",
                            HoraTolerancia="0200",
                            TipoLinha=1,
                            UFDestino="SC",
                            ObsLinha="",
                            ObsLinhaAux="",
                            GratuidadeDisponivel=0,
                            ListaGratuidades=None,
                            Plataforma=None,
                            UFOrigem="MG",
                            TipoServico=None,
                            DuracaoViagem="21:30",
                        ),
                        ControlaPoltrona=0,
                        cPOL=1,
                        idCodigoLinha=10822,
                        Extra=0,
                        Servico=1245,
                        CodigoLinha="GOSC0245001",
                        Gerenciamento=Gerenciamento(
                            IdGerenciamento=72,
                            Descricao="ANTT DIAMANTE CONV",
                            ArredondaTarifa=0,
                            IdadeMaxColo=6,
                            IdTipo=0,
                            IdCliente=None,
                            ImprimeMonitriip=None,
                            ORGAO_INTEREST=1,
                            EmpresaConveniada=None,
                            Comissao=None,
                            CodigoBilhetagem="",
                            SenhaBilhetagem=None,
                            UrlServidorWS="",
                            CodigoEmpresa=None,
                            UrlAgr=None,
                            UsuarioAgr=None,
                            SenhaAgr=None,
                            UrlConfAgr=None,
                            UrlConsAgr=None,
                            UrlCancAgr=None,
                            RESERVADEFIC_GEREN=0,
                        ),
                        CodigoBilhetagem="",
                        DescConexao="CDA x FLO",
                        DataViagem="2025-05-27T00:00:00",
                        HoraViagem="0200",
                        TipoViagem=0,
                        QtdLugares=40,
                        CodigoOrigem=3081,
                        CodigoDestino=3091,
                        Sentido=1,
                        IdLocalidade=3081,
                        DadosViagem="02:00 - R$ 196.90 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                        IdRota=413,
                        LayoutBarco=0,
                        CobraTaxa=True,
                        CobraPedagio=False,
                        CobraSeguro=False,
                        CobraOutros=True,
                        TxConveniencia=0,
                        Outros=0,
                    )
                ],
                TotalConexao=409,
                DescontoConexao=0,
                DescConexao="GYN x CDA",
                PagamentosNaoPermitidos=[],
                IdRota=413,
                TipoConexao=1,
                DescTipoConexao="Escala",
                IdDesconto=460,
                Pricing=0,
                PedagioVolta=0,
                VlTarifaAnterior=199,
                ValorMaiorDesconto=100.61,
                Tarifa=212.1,
                CobrarTxEmbarque=1,
                DadosViagem="17:30 - R$ 212.10 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                EstabelecimentoEmissor=None,
                TxEmbarque=6.55,
                Seguro=0,
                Pedagio=0,
                Desconto=51.39,
                VlTarifa=205.55,
                Andares=2,
                ControlaCliente=0,
                ControlaPoltrona=1,
                CPOL=1,
                IdCodigoLinha=10822,
                CodigoLinha="GOSC0245001",
                Extra=0,
                Servico=1245,
                NaoVerifIdoso50=0,
                NaoVerifIdoso100=0,
                NaoVerifDeficiente=0,
                Comentario=None,
                CodigoBilhetagem="",
                LayoutBarco=0,
                BloqDescontoManual=0,
                BloqAcrescimoManual=0,
                PermissaoDescManualEmpresa=-1,
                TxConveniencia=0,
                SemPoltronaWeb=0,
                LinhaAlias=None,
                Outros=0,
            )
        ],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem=None,
        MensagemDetalhada=None,
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="praxio",
        ota_config_id=3,
        code="147457",
        group_class_code="147457",
        service_code="147457",
        company=OTACompany(
            ota_config_id=3,
            external_id=1,
            name="Test Company",
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        itinerary=[],
        origin=Place(
            slug="origin_city",
            name="Origin City",
            id=1,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        departure_at=datetime(2025, 5, 26, 17, 30),
        destination=Place(
            slug="destination_city",
            name="Destination City",
            id=2,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        arrival_at=datetime(2025, 5, 27, 23, 30),
        seat_type=OTASeatType(
            ota_config_id=3,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        available_seats=33,
        total_seats=40,
        last_synced=mock.ANY,
        extra={
            "IdViagem": 147457,
            "TipoVeiculo": 384,
            "CodigoOrigem": 270,
            "CodigoDestino": 3091,
            "Andares": 2,
            "TipoHorario": "Executivo",
            "TipoServico": 0,
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-05-26T17:30",
            "ChavePricing": None,
        },
        single_ticket_connection=False,
        stopovers=[
            Stopover(
                origin_ota_place_id=270,
                destination_ota_place_id=3081,
                departure_at=datetime(2025, 5, 26, 17, 30),
                extra={
                    "IdViagem": 147457,
                    "TipoVeiculo": 384,
                    "CodigoOrigem": 270,
                    "CodigoDestino": 3081,
                    "Andares": 2,
                    "TipoHorario": "Executivo",
                    "TipoServico": 0,
                    "classe_final": "Executivo",
                    "DataHoraInicio": "2025-05-26T17:30",
                    "ChavePricing": None,
                },
            ),
            Stopover(
                origin_ota_place_id=3081,
                destination_ota_place_id=3091,
                departure_at=datetime(2025, 5, 27, 2, 0),
                extra={
                    "IdViagem": 147457,
                    "TipoVeiculo": 384,
                    "CodigoOrigem": 3081,
                    "CodigoDestino": 3091,
                    "Andares": 2,
                    "TipoHorario": "Executivo",
                    "TipoServico": None,
                    "classe_final": "Executivo",
                    "DataHoraInicio": "2025-05-27T02:00",
                    "ChavePricing": None,
                },
            ),
        ],
        price=308.39,
    )

    assert results[0] == expected_travel


async def test_search_connections(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=3,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"IdLocalidade": 1001},
    )
    destination = OTAPlace(
        ota_config_id=3,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"IdLocalidade": 2001},
    )
    departure_date = date(2025, 1, 10)
    mock_client.partidas.return_value = PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0,
        Sucesso=False,
        Advertencia=False,
        ParametrosEntrada={},
        ListaPartidas=[
            Partida(
                ViagemTFO=ViagemTFO(
                    IdEstabelecimentoLinha=1,
                    ControlaPoltornaTrecho=1,
                    NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                    CodigoOrigem=270,
                    DescricaoOrigem="GOIANIA - GO",
                    CodigoDestino=3081,
                    DescricaoDestino="FLORIANOPOLIS-SC",
                    DataHoraInicio="2025-05-26T17:30",
                    DataHoraEmbarque="2025-05-26T17:30",
                    TempoViagem="510 min",
                    StrDistanciaViagem="405 km",
                    DistanciaViagem=405,
                    DtaHoraChegada="2025-05-27T02:00",
                    ValorTarifa="212.10",
                    TipoHorario="Executivo",
                    UFOrigem="GO",
                    UFDestino="SC",
                    DescricaoDestinoConexao="CONCEICAO DAS ALAGOAS-MG",
                    OfertaAPP5=0,
                    OfertaAP=0,
                    QtdPoltronas=40,
                    PoltronasDisponiveis=33,
                    IdLinha="GOSC0245001",
                    IdVeiculo="",
                    Motorista="",
                    Empresa=None,
                    Filial=None,
                    HoraPartida="1730",
                    HoraTolerancia="1730",
                    TipoLinha=1,
                    ObsLinha="",
                    ObsLinhaAux="",
                    GratuidadeDisponivel=3,
                    ListaGratuidades=[],
                    Plataforma=None,
                    DuracaoViagem="08:30",
                    TipoServico=0,
                ),
                IdViagem=147457,
                IdViagemComp=0,
                TipoVeiculo=384,
                IdLocalidade=270,
                Gerenciamento=Gerenciamento(
                    IdGerenciamento=72,
                    Descricao="ANTT DIAMANTE CONV",
                    ArredondaTarifa=0,
                    IdadeMaxColo=6,
                    IdTipo=0,
                    IdCliente=None,
                    ImprimeMonitriip=None,
                    ORGAO_INTEREST=1,
                    EmpresaConveniada=None,
                    Comissao=None,
                    CodigoBilhetagem="",
                    SenhaBilhetagem=None,
                    UrlServidorWS="",
                    CodigoEmpresa=None,
                    UrlAgr=None,
                    UsuarioAgr=None,
                    SenhaAgr=None,
                    UrlConfAgr=None,
                    UrlConsAgr=None,
                    UrlCancAgr=None,
                    RESERVADEFIC_GEREN=0,
                ),
                conexoes=[
                    Conexao(
                        IdViagem=147458,
                        IdViagemComp=0,
                        IdTipoVeiculo=384,
                        tarifa=196.9,
                        Andares=2,
                        ControlaCliente=0,
                        TxEmbarque=0,
                        Seguro=0,
                        Pedagio=0,
                        Desconto=49.22,
                        Pricing=0,
                        VlTarifa=196.9,
                        viagemTFO=ViagemTFOConexao(
                            IdEstabelecimentoLinha=1,
                            ControlaPoltornaTrecho=0,
                            NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                            CodigoOrigem=3081,
                            DescricaoOrigem="CONCEICAO DAS ALAGOAS-MG",
                            CodigoDestino=3091,
                            DescricaoDestino="FLORIANOPOLIS-SC",
                            DescricaoDestinoConexao=None,
                            DataHoraInicio="2025-05-27T02:00",
                            DataHoraEmbarque="2025-05-27T02:00",
                            TempoViagem="1290 min",
                            StrDistanciaViagem="1392 km",
                            DistanciaViagem=1392,
                            DtaHoraChegada="2025-05-27T23:30",
                            ValorTarifa="196.90",
                            OfertaAPP5=0,
                            OfertaAP=0,
                            QtdPoltronas=40,
                            TipoHorario="Executivo",
                            PercentualImposto="0",
                            ValorImposto="0",
                            PoltronasDisponiveis=33,
                            IdLinha="GOSC0245001",
                            IdVeiculo=None,
                            Motorista=None,
                            Empresa=None,
                            Filial=None,
                            HoraPartida="0200",
                            HoraTolerancia="0200",
                            TipoLinha=1,
                            UFDestino="SC",
                            ObsLinha="",
                            ObsLinhaAux="",
                            GratuidadeDisponivel=0,
                            ListaGratuidades=None,
                            Plataforma=None,
                            UFOrigem="MG",
                            TipoServico=None,
                            DuracaoViagem="21:30",
                        ),
                        ControlaPoltrona=0,
                        cPOL=1,
                        idCodigoLinha=10822,
                        Extra=0,
                        Servico=1245,
                        CodigoLinha="GOSC0245001",
                        Gerenciamento=Gerenciamento(
                            IdGerenciamento=72,
                            Descricao="ANTT DIAMANTE CONV",
                            ArredondaTarifa=0,
                            IdadeMaxColo=6,
                            IdTipo=0,
                            IdCliente=None,
                            ImprimeMonitriip=None,
                            ORGAO_INTEREST=1,
                            EmpresaConveniada=None,
                            Comissao=None,
                            CodigoBilhetagem="",
                            SenhaBilhetagem=None,
                            UrlServidorWS="",
                            CodigoEmpresa=None,
                            UrlAgr=None,
                            UsuarioAgr=None,
                            SenhaAgr=None,
                            UrlConfAgr=None,
                            UrlConsAgr=None,
                            UrlCancAgr=None,
                            RESERVADEFIC_GEREN=0,
                        ),
                        CodigoBilhetagem="",
                        DescConexao="CDA x FLO",
                        DataViagem="2025-05-27T00:00:00",
                        HoraViagem="0200",
                        TipoViagem=0,
                        QtdLugares=40,
                        CodigoOrigem=3081,
                        CodigoDestino=3091,
                        Sentido=1,
                        IdLocalidade=3081,
                        DadosViagem="02:00 - R$ 196.90 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                        IdRota=413,
                        LayoutBarco=0,
                        CobraTaxa=True,
                        CobraPedagio=False,
                        CobraSeguro=False,
                        CobraOutros=True,
                        TxConveniencia=0,
                        Outros=0,
                    )
                ],
                TotalConexao=409,
                DescontoConexao=0,
                DescConexao="GYN x CDA",
                PagamentosNaoPermitidos=[],
                IdRota=413,
                TipoConexao=0,
                DescTipoConexao="Conexao",
                IdDesconto=460,
                Pricing=0,
                PedagioVolta=0,
                VlTarifaAnterior=199,
                ValorMaiorDesconto=100.61,
                Tarifa=212.1,
                CobrarTxEmbarque=1,
                DadosViagem="17:30 - R$ 212.10 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                EstabelecimentoEmissor=None,
                TxEmbarque=6.55,
                Seguro=0,
                Pedagio=0,
                Desconto=51.39,
                VlTarifa=205.55,
                Andares=2,
                ControlaCliente=0,
                ControlaPoltrona=1,
                CPOL=1,
                IdCodigoLinha=10822,
                CodigoLinha="GOSC0245001",
                Extra=0,
                Servico=1245,
                NaoVerifIdoso50=0,
                NaoVerifIdoso100=0,
                NaoVerifDeficiente=0,
                Comentario=None,
                CodigoBilhetagem="",
                LayoutBarco=0,
                BloqDescontoManual=0,
                BloqAcrescimoManual=0,
                PermissaoDescManualEmpresa=-1,
                TxConveniencia=0,
                SemPoltronaWeb=0,
                LinhaAlias=None,
                Outros=0,
            )
        ],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem=None,
        MensagemDetalhada=None,
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 0


async def test_available_seats(searcher, mock_client):
    mock_client.retorna_poltronas.return_value = RetornaPoltronasResponse(
        ParametrosEntrada={"bus": "1234", "route": "5678"},
        QtdDisponivel=2,
        QtdGratuidade=0,
        NumeroPoltrona=2,
        Situacao=1,
        Caption="2",
        IntCaption=0,
        Marcada=False,
        IdReserva=None,
        LaypoltronaXml=LaypoltronaXml(
            PoltronaXmlRetorno=[
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=1,
                    QtdGratuidade=0,
                    NumeroPoltrona=1,
                    Situacao=1,
                    Caption="2",
                    IntCaption=1,
                    Marcada=False,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=0,
                    QtdGratuidade=0,
                    NumeroPoltrona=2,
                    Situacao=0,
                    Caption="3",
                    IntCaption=2,
                    Marcada=True,
                    IdReserva="reservation_123",
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=0,
                    QtdGratuidade=0,
                    NumeroPoltrona=4,
                    Situacao=-7,
                    Caption="4",
                    IntCaption=2,
                    Marcada=True,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
            ]
        ),
    )
    mock_client.partidas.return_value = PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0,
        Sucesso=False,
        Advertencia=False,
        ParametrosEntrada={},
        ListaPartidas=[
            Partida(
                ViagemTFO=ViagemTFO(
                    IdEstabelecimentoLinha=1,
                    ControlaPoltornaTrecho=1,
                    NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                    CodigoOrigem=270,
                    DescricaoOrigem="GOIANIA - GO",
                    CodigoDestino=3081,
                    DescricaoDestino="FLORIANOPOLIS-SC",
                    DataHoraInicio="2025-05-26T17:30",
                    DataHoraEmbarque="2025-05-26T17:30",
                    TempoViagem="510 min",
                    StrDistanciaViagem="405 km",
                    DistanciaViagem=405,
                    DtaHoraChegada="2025-05-27T02:00",
                    ValorTarifa="212.10",
                    TipoHorario="Executivo",
                    UFOrigem="GO",
                    UFDestino="SC",
                    DescricaoDestinoConexao="CONCEICAO DAS ALAGOAS-MG",
                    OfertaAPP5=0,
                    OfertaAP=0,
                    QtdPoltronas=40,
                    PoltronasDisponiveis=33,
                    IdLinha="GOSC0245001",
                    IdVeiculo="",
                    Motorista="",
                    Empresa=None,
                    Filial=None,
                    HoraPartida="1730",
                    HoraTolerancia="1730",
                    TipoLinha=1,
                    ObsLinha="",
                    ObsLinhaAux="",
                    GratuidadeDisponivel=3,
                    ListaGratuidades=[],
                    Plataforma=None,
                    DuracaoViagem="08:30",
                    TipoServico=0,
                ),
                IdViagem=147457,
                IdViagemComp=0,
                TipoVeiculo=384,
                IdLocalidade=270,
                Gerenciamento=Gerenciamento(
                    IdGerenciamento=72,
                    Descricao="ANTT DIAMANTE CONV",
                    ArredondaTarifa=0,
                    IdadeMaxColo=6,
                    IdTipo=0,
                    IdCliente=None,
                    ImprimeMonitriip=None,
                    ORGAO_INTEREST=1,
                    EmpresaConveniada=None,
                    Comissao=None,
                    CodigoBilhetagem="",
                    SenhaBilhetagem=None,
                    UrlServidorWS="",
                    CodigoEmpresa=None,
                    UrlAgr=None,
                    UsuarioAgr=None,
                    SenhaAgr=None,
                    UrlConfAgr=None,
                    UrlConsAgr=None,
                    UrlCancAgr=None,
                    RESERVADEFIC_GEREN=0,
                ),
                conexoes=[],
                TotalConexao=409,
                DescontoConexao=0,
                DescConexao="GYN x CDA",
                PagamentosNaoPermitidos=[],
                IdRota=413,
                TipoConexao=1,
                DescTipoConexao="Escala",
                IdDesconto=460,
                Pricing=0,
                PedagioVolta=0,
                VlTarifaAnterior=199,
                ValorMaiorDesconto=100.61,
                Tarifa=212.1,
                CobrarTxEmbarque=1,
                DadosViagem="17:30 - R$ 212.10 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                EstabelecimentoEmissor=None,
                TxEmbarque=6.55,
                Seguro=0,
                Pedagio=0,
                Desconto=51.39,
                VlTarifa=205.55,
                Andares=2,
                ControlaCliente=0,
                ControlaPoltrona=1,
                CPOL=1,
                IdCodigoLinha=10822,
                CodigoLinha="GOSC0245001",
                Extra=0,
                Servico=1245,
                NaoVerifIdoso50=0,
                NaoVerifIdoso100=0,
                NaoVerifDeficiente=0,
                Comentario=None,
                CodigoBilhetagem="",
                LayoutBarco=0,
                BloqDescontoManual=0,
                BloqAcrescimoManual=0,
                PermissaoDescManualEmpresa=-1,
                TxConveniencia=0,
                SemPoltronaWeb=0,
                LinhaAlias=None,
                Outros=0,
            )
        ],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem=None,
        MensagemDetalhada=None,
    )

    travel = InputTravel(
        ota_config_id=2,
        seat_type="executivo",
        departure_at=datetime(2025, 5, 26, 17, 30).isoformat(),
        extra={
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": 147457,
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-05-26T17:30",
        },
    )

    _now = datetime(2025, 1, 1, 12, 0, tzinfo=timezone.utc)
    with mock.patch("marketplace.otas.praxio.searcher.datetime") as mock_date:
        mock_date.now.return_value = _now
        mock_date.fromisoformat.side_effect = (
            lambda *args, **kw: datetime.fromisoformat(*args, **kw)
        )
        seats = await searcher.available_seats(travel)

    assert seats == SeatMap(
        base_price=160.71,
        seats=[
            Seat(
                number="2",
                floor=1,
                row=1,
                column=1,
                available=False,
                category="Executivo",
                seat_type="executivo",
                price=160.71,
                benefits=[Benefit.NORMAL],
                extra={"NumeroPoltrona": 1},
            ),
            Seat(
                number="3",
                floor=1,
                row=1,
                column=2,
                available=True,
                category="Executivo",
                seat_type="executivo",
                price=160.71,
                benefits=[Benefit.NORMAL],
                extra={"NumeroPoltrona": 2},
            ),
            Seat(
                number="4",
                floor=1,
                row=1,
                column=4,
                available=True,
                category="Executivo",
                seat_type="executivo",
                price=160.71,
                benefits=[Benefit.YOUNG_100],
                extra={"NumeroPoltrona": 4},
            ),
        ],
    )


async def test_available_seats_do_not_search_price(searcher, mock_client):
    mock_client.retorna_poltronas.return_value = RetornaPoltronasResponse(
        ParametrosEntrada={"bus": "1234", "route": "5678"},
        QtdDisponivel=2,
        QtdGratuidade=0,
        NumeroPoltrona=2,
        Situacao=1,
        Caption="2",
        IntCaption=0,
        Marcada=False,
        IdReserva=None,
        LaypoltronaXml=LaypoltronaXml(
            PoltronaXmlRetorno=[
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=1,
                    QtdGratuidade=0,
                    NumeroPoltrona=1,
                    Situacao=1,
                    Caption="2",
                    IntCaption=1,
                    Marcada=False,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=0,
                    QtdGratuidade=0,
                    NumeroPoltrona=2,
                    Situacao=0,
                    Caption="3",
                    IntCaption=2,
                    Marcada=True,
                    IdReserva="reservation_123",
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=0,
                    QtdGratuidade=0,
                    NumeroPoltrona=4,
                    Situacao=-7,
                    Caption="4",
                    IntCaption=2,
                    Marcada=True,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
            ]
        ),
    )

    travel = InputTravel(
        ota_config_id=2,
        seat_type="executivo",
        departure_at=datetime(2025, 5, 26, 17, 30).isoformat(),
        extra={
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": 147457,
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-05-26T17:30",
        },
    )

    _now = datetime(2025, 1, 1, 12, 0, tzinfo=timezone.utc)
    with mock.patch("marketplace.otas.praxio.searcher.datetime") as mock_date:
        mock_date.now.return_value = _now
        mock_date.fromisoformat.side_effect = (
            lambda *args, **kw: datetime.fromisoformat(*args, **kw)
        )
        seats = await searcher.available_seats(travel, search_for_price=False)

    assert seats == SeatMap(
        base_price=None,
        seats=[
            Seat(
                number="2",
                floor=1,
                row=1,
                column=1,
                available=False,
                category="Executivo",
                seat_type="executivo",
                price=None,
                benefits=[Benefit.NORMAL],
                extra={"NumeroPoltrona": 1},
            ),
            Seat(
                number="3",
                floor=1,
                row=1,
                column=2,
                available=True,
                category="Executivo",
                seat_type="executivo",
                price=None,
                benefits=[Benefit.NORMAL],
                extra={"NumeroPoltrona": 2},
            ),
            Seat(
                number="4",
                floor=1,
                row=1,
                column=4,
                available=True,
                category="Executivo",
                seat_type="executivo",
                price=None,
                benefits=[Benefit.YOUNG_100],
                extra={"NumeroPoltrona": 4},
            ),
        ],
    )


async def test_available_seats_incompatible_class(searcher, mock_client):
    mock_client.retorna_poltronas.return_value = RetornaPoltronasResponse(
        ParametrosEntrada={"bus": "1234", "route": "5678"},
        QtdDisponivel=2,
        QtdGratuidade=0,
        NumeroPoltrona=2,
        Situacao=1,
        Caption="2",
        IntCaption=0,
        Marcada=False,
        IdReserva=None,
        LaypoltronaXml=LaypoltronaXml(
            PoltronaXmlRetorno=[
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=1,
                    QtdGratuidade=0,
                    NumeroPoltrona=1,  # Matches seat "A1"
                    Situacao=1,  # 2
                    Caption="2",
                    IntCaption=1,
                    Marcada=False,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=0,
                    QtdGratuidade=0,
                    NumeroPoltrona=2,  # Matches seat "A2"
                    Situacao=0,  # Not 2
                    Caption="3",
                    IntCaption=2,
                    Marcada=True,
                    IdReserva="reservation_123",
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
            ]
        ),
    )
    mock_client.partidas.return_value = PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0,
        Sucesso=False,
        Advertencia=False,
        ParametrosEntrada={},
        ListaPartidas=[
            Partida(
                ViagemTFO=ViagemTFO(
                    IdEstabelecimentoLinha=1,
                    ControlaPoltornaTrecho=1,
                    NomeLinha="12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                    CodigoOrigem=270,
                    DescricaoOrigem="GOIANIA - GO",
                    CodigoDestino=3081,
                    DescricaoDestino="FLORIANOPOLIS-SC",
                    DataHoraInicio="2025-05-26T17:30",
                    DataHoraEmbarque="2025-05-26T17:30",
                    TempoViagem="510 min",
                    StrDistanciaViagem="405 km",
                    DistanciaViagem=405,
                    DtaHoraChegada="2025-05-27T02:00",
                    ValorTarifa="212.10",
                    TipoHorario="Leito",
                    UFOrigem="GO",
                    UFDestino="SC",
                    DescricaoDestinoConexao="CONCEICAO DAS ALAGOAS-MG",
                    OfertaAPP5=0,
                    OfertaAP=0,
                    QtdPoltronas=40,
                    PoltronasDisponiveis=33,
                    IdLinha="GOSC0245001",
                    IdVeiculo="",
                    Motorista="",
                    Empresa=None,
                    Filial=None,
                    HoraPartida="1730",
                    HoraTolerancia="1730",
                    TipoLinha=1,
                    ObsLinha="",
                    ObsLinhaAux="",
                    GratuidadeDisponivel=3,
                    ListaGratuidades=[],
                    Plataforma=None,
                    DuracaoViagem="08:30",
                    TipoServico=0,
                ),
                IdViagem=147457,
                IdViagemComp=0,
                TipoVeiculo=384,
                IdLocalidade=270,
                Gerenciamento=Gerenciamento(
                    IdGerenciamento=72,
                    Descricao="ANTT DIAMANTE CONV",
                    ArredondaTarifa=0,
                    IdadeMaxColo=6,
                    IdTipo=0,
                    IdCliente=None,
                    ImprimeMonitriip=None,
                    ORGAO_INTEREST=1,
                    EmpresaConveniada=None,
                    Comissao=None,
                    CodigoBilhetagem="",
                    SenhaBilhetagem=None,
                    UrlServidorWS="",
                    CodigoEmpresa=None,
                    UrlAgr=None,
                    UsuarioAgr=None,
                    SenhaAgr=None,
                    UrlConfAgr=None,
                    UrlConsAgr=None,
                    UrlCancAgr=None,
                    RESERVADEFIC_GEREN=0,
                ),
                conexoes=[],
                TotalConexao=409,
                DescontoConexao=0,
                DescConexao="GYN x CDA",
                PagamentosNaoPermitidos=[],
                IdRota=413,
                TipoConexao=1,
                DescTipoConexao="Escala",
                IdDesconto=460,
                Pricing=0,
                PedagioVolta=0,
                VlTarifaAnterior=199,
                ValorMaiorDesconto=100.61,
                Tarifa=212.1,
                CobrarTxEmbarque=1,
                DadosViagem="17:30 - R$ 212.10 - Linha: GOSC0245001 12076300 - ANAPOLIS (GO) - FLORIANOPOLIS (SC) CONV",
                EstabelecimentoEmissor=None,
                TxEmbarque=6.55,
                Seguro=0,
                Pedagio=0,
                Desconto=51.39,
                VlTarifa=205.55,
                Andares=2,
                ControlaCliente=0,
                ControlaPoltrona=1,
                CPOL=1,
                IdCodigoLinha=10822,
                CodigoLinha="GOSC0245001",
                Extra=0,
                Servico=1245,
                NaoVerifIdoso50=0,
                NaoVerifIdoso100=0,
                NaoVerifDeficiente=0,
                Comentario=None,
                CodigoBilhetagem="",
                LayoutBarco=0,
                BloqDescontoManual=0,
                BloqAcrescimoManual=0,
                PermissaoDescManualEmpresa=-1,
                TxConveniencia=0,
                SemPoltronaWeb=0,
                LinhaAlias=None,
                Outros=0,
            )
        ],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem=None,
        MensagemDetalhada=None,
    )

    travel = InputTravel(
        ota_config_id=2,
        seat_type="executivo",
        departure_at=datetime(2025, 1, 9, 6, 0).isoformat(),
        extra={
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": 147457,
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-01-09T06:00:00",
        },
    )
    _now = datetime(2025, 1, 1, 12, 0, tzinfo=timezone.utc)
    with (
        pytest.raises(
            OTASearcherTravelNotFound,
            match="The travel found for the 147457 has incompatible class or departure time",
        ),
        mock.patch("marketplace.otas.praxio.searcher.datetime") as mock_date,
    ):
        mock_date.now.return_value = _now
        mock_date.fromisoformat.side_effect = (
            lambda *args, **kw: datetime.fromisoformat(*args, **kw)
        )
        await searcher.available_seats(travel)


async def test_available_seats_not_found(searcher, mock_client):
    mock_client.retorna_poltronas.return_value = RetornaPoltronasResponse(
        ParametrosEntrada={"bus": "1234", "route": "5678"},
        QtdDisponivel=2,
        QtdGratuidade=0,
        NumeroPoltrona=2,
        Situacao=1,
        Caption="2",
        IntCaption=0,
        Marcada=False,
        IdReserva=None,
        LaypoltronaXml=LaypoltronaXml(
            PoltronaXmlRetorno=[
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=1,
                    QtdGratuidade=0,
                    NumeroPoltrona=1,  # Matches seat "A1"
                    Situacao=1,  # 2
                    Caption="2",
                    IntCaption=1,
                    Marcada=False,
                    IdReserva=None,
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
                Poltrona(
                    ParametrosEntrada={"categoriaReservadaId": 1},
                    QtdDisponivel=0,
                    QtdGratuidade=0,
                    NumeroPoltrona=2,  # Matches seat "A2"
                    Situacao=0,  # Not 2
                    Caption="3",
                    IntCaption=2,
                    Marcada=True,
                    IdReserva="reservation_123",
                    ReservaSite=False,
                    Sucesso=True,
                    Advertencia=False,
                ),
            ]
        ),
    )
    mock_client.partidas.return_value = PartidasResponse(
        Strings=[],
        Integers=[],
        Floats=[],
        VarStr=None,
        VarInt=0,
        VarFloat=0,
        Sucesso=False,
        Advertencia=False,
        ParametrosEntrada={},
        ListaPartidas=[],
        ListaMultiempresas=[],
        ListaLogos=[],
        Xml=None,
        IdErro=None,
        Mensagem=None,
        MensagemDetalhada=None,
    )

    travel = InputTravel(
        ota_config_id=2,
        seat_type="executivo",
        departure_at=datetime(2025, 1, 9, 6, 0).isoformat(),
        extra={
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": 147457,
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-01-09T06:00:00",
        },
    )

    _now = datetime(2025, 1, 1, 12, 0, tzinfo=timezone.utc)
    with (
        pytest.raises(
            OTASearcherTravelNotFound,
            match="Not found any travel with the code 147457",
        ),
        mock.patch("marketplace.otas.praxio.searcher.datetime") as mock_date,
    ):
        mock_date.now.return_value = _now
        mock_date.fromisoformat.side_effect = (
            lambda *args, **kw: datetime.fromisoformat(*args, **kw)
        )

        await searcher.available_seats(travel)


async def test_block_seat(searcher, mock_client):
    mock_client.verifica_poltrona.return_value = VerificaPoltronaResponse(
        Marcada=False,
        IdErro="",
        Strings=[],
        Integers=[],
        Floats=[],
        VarInt=1,
        VarFloat=1.0,
        Sucesso=True,
        Advertencia=False,
        VarStr="",
        Mensagem="",
    )

    travel = Travel(
        ota="praxio",
        ota_config_id=2,
        code="travel_key_123",
        service_code="travel_key_123",
        company=OTACompany(
            name="Test Company",
            external_id=2,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=datetime(2024, 1, 1, tzinfo=timezone.utc),
        extra={
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": "123",
            "TipoVeiculo": "Executivo",
        },
    )

    seat = Seat(
        number="1",
        floor=1,
        row=1,
        column=1,
        available=True,
        category="Executivo",
        seat_type=None,
        price=150.00,
        extra={"NumeroPoltrona": "123"},
    )

    _now = datetime(2025, 1, 1, 12, 0, tzinfo=timezone.utc)
    with mock.patch("marketplace.otas.praxio.searcher.datetime") as mock_date:
        mock_date.now.return_value = _now
        blocked_seat = await searcher.block_seat(travel, seat)

    assert blocked_seat == BlockedSeat(
        number="1",
        available=True,
        category="Executivo",
        seat_type=None,
        price=150.00,
        floor=1,
        row=1,
        column=1,
        extra={"NumeroPoltrona": "123"},
        best_before=_now
        + timedelta(seconds=searcher.ota_config.search_cache.ttl_block_seat),
        block_key=1,
    )


async def test_unblock_seat(searcher, mock_client):
    mock_client.desmarca_poltrona.return_value = VerificaPoltronaResponse(
        Marcada=False,
        IdErro="",
        Strings=[],
        Integers=[],
        Floats=[],
        VarInt=1,
        VarFloat=1.0,
        Sucesso=True,
        Advertencia=False,
        VarStr="",
        Mensagem="",
    )

    travel = Travel(
        ota="praxio",
        ota_config_id=2,
        code="travel_key_123",
        service_code="travel_key_123",
        company=OTACompany(
            name="Test Company",
            external_id=2,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=datetime(2024, 1, 1, tzinfo=timezone.utc),
        extra={
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": "123",
            "TipoVeiculo": 1,
            "TipoHorario": "Executivo",
        },
    )

    seat = BlockedSeat(
        number="1",
        floor=1,
        row=1,
        column=1,
        available=True,
        category="Executivo",
        seat_type=None,
        price=150.00,
        extra={"NumeroPoltrona": "123"},
        block_key=123,
        best_before=datetime.now(timezone.utc),
    )

    result = await searcher.unblock_seat(travel, seat)

    assert result is False


async def test_list_places(searcher, mock_client):
    mock_client.partidas_origens.return_value = [
        Localidade(IdLocalidade="123", Descricao="456", IdCidade="989")
    ]

    places = await searcher.list_places()

    assert len(places) == 1
    assert places[0] == OTAPlace(
        name="456", ota_config_id=3, extra={"IdLocalidade": "123"}
    )


async def test_search_travels_timeout(searcher, mock_client):
    mock_client.partidas.side_effect = OTATimeoutException("error")
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)

    with pytest.raises(OTATimeoutException):
        await searcher._search_travels(date(2025, 1, 1), 1, 2)


async def test_search_travels_invalid_parameters(searcher, mock_client):
    mock_client.partidas.side_effect = OTAInvalidParameters(
        "O local de origem e de destino não pode ser o mesmo.",
    )
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)

    with pytest.raises(OTAInvalidParameters):
        await searcher._search_travels(date(2025, 1, 1), 1, 2)


async def test_travel_itinerary(searcher, mock_client):
    mock_client.lista_partidas_tfo.return_value = ListaPartidasTFOResponse(
        ParametrosEntrada={},
        ListaTrechosTFO=None,
        ListaPartidasTFO=[
            PartidaTFO(
                DataBloqSemPassageiro="0001-01-01T00:00:00",
                HoraBloqSemPassageiro=None,
                IDViagem=10920,
                DataPartida="2025-06-18T21:00:00",
                HoraPartida="2100",
                Sentido=0,
                Plataforma="",
                HoraChegada=None,
                ControlaPoltrona=0,
                ControlaPassageiros=0,
                ControlaCliente=0,
                Localidade=LocalidadePartida(
                    ListEstabelecimentos=None,
                    BilheteEmbW2i=1,
                    IDLocalidade=35,
                    Descricao="BARREIRAS",
                    Sigla="BES",
                    IdRegiao=None,
                    Uf="BA",
                    IdEstabelecimento=0,
                    IdCidade=2903201,
                    TxEmbIdoso50=0,
                    TxEmbIdoso100=0,
                    TxEmbPasseLivre=0,
                    PedagioIdoso100=0,
                    PedagioIdoso50=0,
                    Codigo=0,
                    AgenciasCargas=None,
                    LastUpdate=None,
                    TxPedagioPasseLivre=None,
                    CodigoSgltar=0,
                    TxEmb50Idoso50=0,
                    TxEmb50Def50=0,
                    TxEmb50Estud50=0,
                    TxEmb50Jov50=0,
                    TxEmbIdosoDef=0,
                    CodigoSigma=None,
                    TxEmbCortesia=0,
                    TxEmbJovem50=0,
                    TxEmbJovem100=0,
                    TxEmbAcompanhantePL=0,
                ),
                DataChegada="0001-01-01T00:00:00",
                Obs=None,
                HoraTolerancia=None,
                DataExibirViagem="0001-01-01T00:00:00",
                HoraExibirViagem=None,
                QtdPoltronaBloqueio=0,
                Comentario="",
            ),
            PartidaTFO(
                DataBloqSemPassageiro="0001-01-01T00:00:00",
                HoraBloqSemPassageiro=None,
                IDViagem=10920,
                DataPartida="2025-06-18T22:30:00",
                HoraPartida="2230",
                Sentido=0,
                Plataforma="",
                HoraChegada=None,
                ControlaPoltrona=0,
                ControlaPassageiros=0,
                ControlaCliente=0,
                Localidade=LocalidadePartida(
                    ListEstabelecimentos=None,
                    BilheteEmbW2i=1,
                    IDLocalidade=34,
                    Descricao="LUIS EDUARDO MAGALHAES",
                    Sigla="MIOO",
                    IdRegiao=None,
                    Uf="BA",
                    IdEstabelecimento=0,
                    IdCidade=2919553,
                    TxEmbIdoso50=0,
                    TxEmbIdoso100=0,
                    TxEmbPasseLivre=0,
                    PedagioIdoso100=0,
                    PedagioIdoso50=0,
                    Codigo=0,
                    AgenciasCargas=None,
                    LastUpdate=None,
                    TxPedagioPasseLivre=None,
                    CodigoSgltar=0,
                    TxEmb50Idoso50=0,
                    TxEmb50Def50=0,
                    TxEmb50Estud50=0,
                    TxEmb50Jov50=0,
                    TxEmbIdosoDef=0,
                    CodigoSigma=None,
                    TxEmbCortesia=0,
                    TxEmbJovem50=0,
                    TxEmbJovem100=0,
                    TxEmbAcompanhantePL=0,
                ),
                DataChegada="0001-01-01T00:00:00",
                Obs=None,
                HoraTolerancia=None,
                DataExibirViagem="0001-01-01T00:00:00",
                HoraExibirViagem=None,
                QtdPoltronaBloqueio=0,
                Comentario="",
            ),
            PartidaTFO(
                DataBloqSemPassageiro="0001-01-01T00:00:00",
                HoraBloqSemPassageiro=None,
                IDViagem=10920,
                DataPartida="2025-06-19T02:45:00",
                HoraPartida="0245",
                Sentido=0,
                Plataforma="",
                HoraChegada=None,
                ControlaPoltrona=0,
                ControlaPassageiros=0,
                ControlaCliente=0,
                Localidade=LocalidadePartida(
                    ListEstabelecimentos=None,
                    BilheteEmbW2i=0,
                    IDLocalidade=33,
                    Descricao="ALVORADA DO NORTE",
                    Sigla="AVN",
                    IdRegiao=None,
                    Uf="GO",
                    IdEstabelecimento=0,
                    IdCidade=5200803,
                    TxEmbIdoso50=0,
                    TxEmbIdoso100=0,
                    TxEmbPasseLivre=0,
                    PedagioIdoso100=0,
                    PedagioIdoso50=0,
                    Codigo=0,
                    AgenciasCargas=None,
                    LastUpdate=None,
                    TxPedagioPasseLivre=None,
                    CodigoSgltar=0,
                    TxEmb50Idoso50=0,
                    TxEmb50Def50=0,
                    TxEmb50Estud50=0,
                    TxEmb50Jov50=0,
                    TxEmbIdosoDef=0,
                    CodigoSigma=None,
                    TxEmbCortesia=0,
                    TxEmbJovem50=0,
                    TxEmbJovem100=0,
                    TxEmbAcompanhantePL=0,
                ),
                DataChegada="0001-01-01T00:00:00",
                Obs=None,
                HoraTolerancia=None,
                DataExibirViagem="0001-01-01T00:00:00",
                HoraExibirViagem=None,
                QtdPoltronaBloqueio=0,
                Comentario="",
            ),
        ],
    )

    travel = InputTravel(
        ota_config_id=2,
        extra={
            "CodigoOrigem": 34,
            "CodigoDestino": 33,
            "Andares": 1,
            "IdViagem": "123",
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
            "classe_final": "Executivo",
            "DataHoraInicio": "2025-06-18T22:30:00",
        },
    )
    checkpoints = await searcher.travel_itinerary(travel)

    assert checkpoints == [
        Checkpoint(
            name="LUIS EDUARDO MAGALHAES",
            departure_at=datetime(2025, 6, 18, 22, 30),
            distance_km=None,
        ),
        Checkpoint(
            name="ALVORADA DO NORTE",
            departure_at=datetime(2025, 6, 19, 2, 45),
            distance_km=None,
        ),
    ]
