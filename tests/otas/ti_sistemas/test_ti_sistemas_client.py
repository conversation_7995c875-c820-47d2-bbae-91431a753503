from datetime import date

import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.exception import SeatUnavailable
from marketplace.otas.ti_sistemas.client import (
    BlockSeatsResponse,
    BusLayout,
    Checkpoint,
    Deck,
    PriceInfo,
    Seat,
    TiSistemasClient,
    Travel,
    TravelInfo,
)


@pytest.fixture
def client():
    return TiSistemasClient(base_url="https://tisistemas.test", auth_token="auth_token")


async def test_travels_search(httpx_mock: HTTPXMock, client: TiSistemasClient):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://tisistemas.test/rest/bs/travels"
            "?origin=1001"
            "&destination=2001"
            "&date=09-01-2025"
        ),
        json=[
            {
                "id": 59,
                "origin": 2169,
                "destination": 977,
                "departure": "2025-06-22T01:30",
                "arrival": "2025-06-22T07:00",
                "service": "CONVENCIONAL",
                "busCompany": "558",
                "busCompanyName": "RealMaia Goiania (Buser)",
                "freeSeats": 48,
                "price": 99.71,
                "toll": 0.0,
                "busType": "C",
                "message": "",
            }
        ],
    )

    response = await client.travels_search(
        departure_date=date(2025, 1, 9),
        origin=1001,
        destination=2001,
    )

    assert response == [
        Travel(
            id=59,
            origin=2169,
            destination=977,
            departure="2025-06-22T01:30",
            arrival="2025-06-22T07:00",
            service="CONVENCIONAL",
            busCompany="558",
            busCompanyName="RealMaia Goiania (Buser)",
            freeSeats=48,
            price=99.71,
            toll=0.0,
            busType="C",
            message="",
        )
    ]


async def test_get_itinerary(httpx_mock: HTTPXMock, client: TiSistemasClient):
    httpx_mock.add_response(
        method="GET",
        url="https://tisistemas.test/rest/bs/itineraries/123/456",
        json={
            "list": [
                {
                    "id": 1,
                    "localId": 10,
                    "localNome": "Goiânia",
                    "localUf": "GO",
                    "dataHoraPartida": "2025-06-22T01:30",
                    "totalKm": 150.0,
                    "duracao": "02:00",
                    "tz": -3,
                }
            ]
        },
    )

    response = await client.get_itinerary(company_id="123", travel_id="456")

    assert response == [
        Checkpoint(
            id=1,
            localId=10,
            localNome="Goiânia",
            localUf="GO",
            dataHoraPartida="2025-06-22T01:30",
            totalKm=150.0,
            duracao="02:00",
            tz=-3,
        )
    ]


async def test_get_bus_layout(httpx_mock: HTTPXMock, client: TiSistemasClient):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://tisistemas.test/rest/bs/bus"
            "?date=09-01-2025&origin=1001&destination=2001&busCompany=123&id=456"
        ),
        json={
            "travel": {
                "id": 59,
                "origin": 2169,
                "destination": 977,
                "departure": "2025-06-22T01:30",
                "arrival": "2025-06-22T07:00",
                "service": "CONVENCIONAL",
                "busCompany": "558",
                "busCompanyName": "RealMaia Goiania (Buser)",
                "freeSeats": 48,
                "price": 99.71,
                "toll": 0.0,
                "busType": "C",
                "message": "",
            },
            "priceInfo": {
                "basePrice": 90.0,
                "insurancePrice": 2.0,
                "taxPrice": 1.0,
                "otherPrice": 0.0,
                "tollPrice": 0.0,
                "boardingPrice": 1.0,
                "comission": 5.71,
                "price": 99.71,
            },
            "busLayout": {
                "decks": [
                    {
                        "number": 1,
                        "seats": [
                            {
                                "status": "AVAILABLE",
                                "x": 0,
                                "y": 1,
                                "number": "1A",
                                "description": None,
                            }
                        ],
                    }
                ]
            },
        },
    )

    response = await client.get_bus_layout(
        departure_date=date(2025, 1, 9),
        origin=1001,
        destination=2001,
        company_id="123",
        id=456,
    )

    expected = TravelInfo(
        travel=Travel(
            id=59,
            origin=2169,
            destination=977,
            departure="2025-06-22T01:30",
            arrival="2025-06-22T07:00",
            service="CONVENCIONAL",
            busCompany="558",
            busCompanyName="RealMaia Goiania (Buser)",
            freeSeats=48,
            price=99.71,
            toll=0.0,
            busType="C",
            message="",
        ),
        priceInfo=PriceInfo(
            basePrice=90.0,
            insurancePrice=2.0,
            taxPrice=1.0,
            otherPrice=0.0,
            tollPrice=0.0,
            boardingPrice=1.0,
            comission=5.71,
            price=99.71,
        ),
        busLayout=BusLayout(
            decks=[
                Deck(
                    number=1,
                    seats=[
                        Seat(
                            status="AVAILABLE", x=0, y=1, number="1A", description=None
                        )
                    ],
                )
            ]
        ),
    )

    assert response == expected


async def test_block_seats(httpx_mock: HTTPXMock, client: TiSistemasClient):
    httpx_mock.add_response(
        method="POST",
        url=("https://tisistemas.test/rest/bs/bookings"),
        json={
            "orderId": "123",
            "seats": [
                {
                    "seatId": 1,
                    "voucher": 123,
                    "seat": "1A",
                    "status": "Reservada",
                }
            ],
        },
    )

    response = await client.block_seats(
        departure_date=date(2025, 1, 9),
        origin=1001,
        destination=2001,
        company_id="123",
        id=456,
        order_id="123",
        seat_numbers=["1A"],
    )

    expected = BlockSeatsResponse(
        order_id="123",
        seats=[
            BlockSeatsResponse.BlockedSeat(
                seat_id=1, voucher=123, seat="1A", status="Reservada"
            )
        ],
    )

    assert response == expected


async def test_block_seats_seat_unavailable(
    httpx_mock: HTTPXMock, client: TiSistemasClient
):
    httpx_mock.add_response(
        method="POST",
        url=("https://tisistemas.test/rest/bs/bookings"),
        status_code=500,
        json={"id": "", "code": "", "type": "", "message": "O banco 9 já está ocupado"},
    )

    with pytest.raises(SeatUnavailable):
        await client.block_seats(
            departure_date=date(2025, 1, 9),
            origin=1001,
            destination=2001,
            company_id="123",
            id=456,
            order_id="123",
            seat_numbers=["1A"],
        )


async def test_unblock_seat(httpx_mock: HTTPXMock, client: TiSistemasClient):
    httpx_mock.add_response(
        method="DELETE",
        url=("https://tisistemas.test/rest/bs/bookings?orderId=123&seatId=1"),
        json={"ok": "ok"},
    )

    response = await client.unblock_seat(
        order_id="123",
        seat_id=1,
    )

    assert response is None
