from datetime import date, datetime, timezone
from unittest import mock
from unittest.mock import AsyncMock

import httpx
import pytest
from pytest_httpx import HTTPXMock

from marketplace.circuit_breaker import CircuitState
from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASearchCircuitBreakerConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    PlaceStatus,
    Seat,
    SeatMap,
    Travel,
)
from marketplace.otas.exception import (
    OTATimeoutException,
    OTATooManyRequests,
    SeatUnavailable,
)
from marketplace.otas.ti_sistemas.client import (
    BlockSeatsResponse,
    BusLayout,
    Deck,
    PriceInfo,
    TiSistemasClient,
    TravelInfo,
)
from marketplace.otas.ti_sistemas.client import (
    Checkpoint as TiSistemasCheckpoint,
)
from marketplace.otas.ti_sistemas.client import (
    Seat as TiSistemasSeat,
)
from marketplace.otas.ti_sistemas.client import (
    Travel as TiSistemasTravel,
)
from marketplace.otas.ti_sistemas.searcher import TiSistemasSearcher


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=2,
        name="TiSistemas",
        provider="ti_sistemas",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=2,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=2,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
    )


@pytest.fixture
def mock_client():
    return AsyncMock(spec=TiSistemasClient)


@pytest.fixture
def searcher(ota_config, mock_client):
    searcher = TiSistemasSearcher(
        ota_config=ota_config,
        base_url="https://tisistemas.test",
        auth_token="auth_token",
    )
    searcher._session = mock_client

    return searcher


async def test_search(searcher, mock_client):
    mock_client.travels_search.return_value = [
        TiSistemasTravel(
            id=59,
            origin=2169,
            destination=977,
            departure="2025-06-22T01:30",
            arrival="2025-06-22T07:00",
            service="Executivo",
            busCompany="1",
            busCompanyName="RealMaia Goiania (Buser)",
            freeSeats=-1,
            price=99.71,
            toll=0.0,
            busType="C",
            message="",
        )
    ]

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=date(2025, 1, 9)
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="ti_sistemas",
        ota_config_id=2,
        code="59_2169_977_Executivo_1",
        itinerary_class_code="59",
        service_code="59",
        company=OTACompany(
            ota_config_id=2,
            external_id=1,
            name="Test Company",
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        itinerary=[],
        origin=Place(
            slug="origin_city",
            name="Origin City",
            id=1,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        departure_at=datetime(2025, 6, 22, 1, 30),
        destination=Place(
            slug="destination_city",
            name="Destination City",
            id=2,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        arrival_at=datetime(2025, 6, 22, 7, 0),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        available_seats=0,
        total_seats=0,
        last_synced=mock.ANY,
        extra={
            "id": 59,
            "origin": 2169,
            "destination": 977,
            "departure": "2025-06-22T01:30",
            "busCompany": "1",
            "service": "Executivo",
        },
        single_ticket_connection=False,
        stopovers=None,
        price=99.71,
    )
    assert results[0] == expected_travel


async def test_search_travels_open_circuit_after_errors(httpx_mock: HTTPXMock):
    searcher = TiSistemasSearcher(
        ota_config=OTAConfig(
            id=1,
            name="Example",
            provider="example",
            status=OTAConfigStatus.active,
            created_at=mock.ANY,
            updated_at=mock.ANY,
            companies=mock.ANY,
            seat_types=mock.ANY,
            search_cache=mock.ANY,
            circuit_breaker=OTASearchCircuitBreakerConfig(
                enabled=True,
                failure_threshold=2,
            ),
        ),
        base_url="http://example.com",
        auth_token="test",
    )
    searcher.retry_policy = lambda v: v

    origin = OTAPlace(ota_config_id=1, name="place-1", extra={"id": 1})
    destination = OTAPlace(ota_config_id=1, name="place-2", extra={"id": 2})

    httpx_mock.add_exception(
        httpx.ReadTimeout("Unable to read within timeout"),
        is_reusable=True,
    )
    for _ in range(3):
        try:
            await searcher.search(origin, destination, date(2025, 1, 15))
        except (OTATimeoutException, OTATooManyRequests):
            ...

    assert searcher._circuit_breaker.state == CircuitState.OPEN


@pytest.mark.asyncio
async def test_available_seats(searcher, mock_client):
    travel_info = TravelInfo(
        travel=mock.ANY,
        priceInfo=PriceInfo(
            basePrice=90.0,
            insurancePrice=2.0,
            taxPrice=1.0,
            otherPrice=0.0,
            tollPrice=0.0,
            boardingPrice=1.0,
            comission=5.71,
            price=99.71,
        ),
        busLayout=BusLayout(
            decks=[
                Deck(
                    number=1,
                    seats=[
                        TiSistemasSeat(
                            status="FREE", x=0, y=1, number="1A", description="Janela"
                        )
                    ],
                )
            ]
        ),
    )
    mock_client.get_bus_layout.return_value = travel_info

    travel = InputTravel(
        ota_config_id=searcher.ota_config.id,
        seat_type="executivo",
        departure_at=datetime(2025, 6, 22, 1, 30).isoformat(),
        extra={
            "id": 59,
            "origin": 2169,
            "destination": 977,
            "departure": "2025-06-22T01:30",
            "busCompany": "1",
            "service": "Executivo",
        },
    )

    seats = await searcher.available_seats(travel)

    assert seats == SeatMap(
        base_price=99.71,
        seats=[
            Seat(
                number="1A",
                floor=1,
                row=0,
                column=1,
                available=True,
                category="Executivo",
                seat_type="executivo",
                benefits=[Benefit.NORMAL],
                price=99.71,
                extra={"description": "Janela"},
            )
        ],
    )


async def test_travel_itinerary(searcher, mock_client):
    mock_client.get_itinerary.return_value = [
        TiSistemasCheckpoint(
            id=1,
            localId=2169,
            localNome="Origem",
            localUf="GO",
            dataHoraPartida="2025-06-22 01:30:00",
            totalKm=0.0,
            duracao="00:00",
            tz=-3,
        ),
        TiSistemasCheckpoint(
            id=2,
            localId=977,
            localNome="Destino",
            localUf="SP",
            dataHoraPartida="2025-06-22 07:00:00",
            totalKm=350.0,
            duracao="05:30",
            tz=-3,
        ),
    ]

    travel = InputTravel(
        ota_config_id=searcher.ota_config.id,
        extra={
            "id": 59,
            "origin": 2169,
            "destination": 977,
            "departure": "2025-06-22T01:30",
            "busCompany": "1",
            "service": "Executivo",
        },
    )

    itinerary = await searcher.travel_itinerary(travel)

    assert itinerary == [
        Checkpoint(
            name="Origem - GO",
            departure_at=datetime(2025, 6, 22, 1, 30),
            distance_km=0,
        ),
        Checkpoint(
            name="Destino - SP",
            departure_at=datetime(2025, 6, 22, 7, 0),
            distance_km=350.0,
        ),
    ]


async def test_block_seat(searcher, mock_client):
    mock_client.block_seats.return_value = BlockSeatsResponse(
        order_id="123",
        seats=[
            BlockSeatsResponse.BlockedSeat(
                seat_id=1, voucher=123, seat="10", status="Reservada"
            )
        ],
    )

    travel = InputTravel(
        ota_config_id=searcher.ota_config.id,
        departure_at=datetime(2025, 6, 22, 1, 30).isoformat(),
        extra={
            "id": 59,
            "origin": 2169,
            "destination": 977,
            "departure": "2025-06-22T01:30",
            "busCompany": "1",
            "service": "Executivo",
        },
    )
    seat = Seat(
        number="10",
        floor=1,
        row=0,
        column=1,
        available=True,
        category="Executivo",
        seat_type="executivo",
        benefits=[Benefit.NORMAL],
        price=99.71,
        extra={"description": "Janela"},
    )

    searcher._generate_order_id = lambda: "123"
    blocked_seat = await searcher.block_seat(travel, seat)

    assert blocked_seat == BlockedSeat(
        number="10",
        floor=1,
        row=0,
        column=1,
        available=True,
        category="Executivo",
        seat_type="executivo",
        benefits=[Benefit.NORMAL],
        price=99.71,
        extra={"description": "Janela"},
        best_before=mock.ANY,
        block_key={"order_id": "123", "seat_id": 1},
        extra_rodoviaria={"seat": 10, "order_id": "123", "seat_id": 1},
    )


async def test_block_seat_seat_unavailable(searcher, mock_client):
    mock_client.block_seats.side_effect = SeatUnavailable

    travel = InputTravel(
        ota_config_id=searcher.ota_config.id,
        departure_at=datetime(2025, 6, 22, 1, 30).isoformat(),
        extra={
            "id": 59,
            "origin": 2169,
            "destination": 977,
            "departure": "2025-06-22T01:30",
            "busCompany": "1",
            "service": "Executivo",
        },
    )
    seat = Seat(
        number="1A",
        floor=1,
        row=0,
        column=1,
        available=True,
        category="Executivo",
        seat_type="executivo",
        benefits=[Benefit.NORMAL],
        price=99.71,
        extra={"description": "Janela"},
    )
    with pytest.raises(SeatUnavailable):
        await searcher.block_seat(travel, seat)


async def test_unblock_seat(searcher, mock_client):
    mock_client.unblock_seat.return_value = None

    travel = InputTravel(
        ota_config_id=searcher.ota_config.id,
        departure_at=datetime(2025, 6, 22, 1, 30).isoformat(),
        extra={
            "id": 59,
            "origin": 2169,
            "destination": 977,
            "departure": "2025-06-22T01:30",
            "busCompany": "1",
            "service": "Executivo",
        },
    )

    blocked_seat = BlockedSeat(
        number="1A",
        floor=1,
        row=0,
        column=1,
        available=True,
        category="Executivo",
        seat_type="executivo",
        benefits=[Benefit.NORMAL],
        price=99.71,
        extra={"description": "Janela"},
        best_before=mock.ANY,
        block_key={"order_id": "123", "seat_id": 1},
    )

    await searcher.unblock_seat(travel, blocked_seat)
