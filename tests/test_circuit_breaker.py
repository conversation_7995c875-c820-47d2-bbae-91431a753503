import asyncio
from datetime import timed<PERSON><PERSON>

import pytest

from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    CircuitState,
)


async def dummy_success():
    return "success"


async def dummy_failure():
    raise ValueError("failure")


async def dummy_slow():
    await asyncio.sleep(0.2)
    return "slow"


class DummyIgnoredException(Exception):
    pass


async def dummy_ignored_exception():
    raise DummyIgnoredException("failure")


@pytest.fixture
def breaker():
    return CircuitBreaker(
        CircuitBreakerConfig(
            failure_threshold=2,
            failure_window=timedelta(seconds=1),
            recovery_timeout=timedelta(seconds=0.5),
        ),
        name="test_breaker",
        ignored_exceptions=(DummyIgnoredException,),
    )


async def test_initial_state(breaker):
    assert breaker.state == CircuitState.CLOSED
    assert breaker.failure_count == 0


async def test_successful_call(breaker):
    result = await breaker.call(dummy_success)
    assert result == "success"
    assert breaker.state == CircuitState.CLOSED
    assert breaker.failure_count == 0


async def test_failure_threshold(breaker):
    # First failure
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.CLOSED

    # Second failure - should trip the circuit
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.OPEN

    # Next call should fail with CircuitOpenError
    with pytest.raises(CircuitOpenError):
        await breaker.call(dummy_success)


async def test_ignore_exceptions(breaker):
    # First failure
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.CLOSED

    # Second failure - should mantain the circuit closed, because the exception is ignored
    with pytest.raises(DummyIgnoredException):
        await breaker.call(dummy_ignored_exception)
    assert breaker.state == CircuitState.CLOSED

    # Next call should open the circuit
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.OPEN


async def test_success_after_recovery_timeout(breaker):
    # Trip the circuit
    for _ in range(2):
        with pytest.raises(ValueError):
            await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.OPEN

    # Wait for recovery timeout
    sleep_time = (
        breaker.config.recovery_timeout + timedelta(seconds=0.1)
    ).total_seconds()
    await asyncio.sleep(sleep_time)  # Just over recovery_timeout

    # Circuit should go to HALF_OPEN on next call
    result = await breaker.call(dummy_success)
    assert result == "success"
    assert breaker.state == CircuitState.CLOSED


async def test_error_after_recovery_timeout(breaker):
    # Trip the circuit
    for _ in range(2):
        with pytest.raises(ValueError):
            await breaker.call(dummy_failure)

    # Wait for recovery timeout
    sleep_time = (
        breaker.config.recovery_timeout + timedelta(seconds=0.1)
    ).total_seconds()
    await asyncio.sleep(sleep_time)

    # A failure in HALF_OPEN keeps the circuit in the HALF_OPEN state
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.HALF_OPEN

    # Once the allowed test_requests are exhausted, the circuit transitions to OPEN
    # and blocks further calls without executing the operation
    with pytest.raises(CircuitOpenError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.OPEN


async def test_state_change_callback():
    state_changes = []

    async def on_state_change(circuit_breaker):
        state_changes.append(circuit_breaker.state)

    breaker = CircuitBreaker(
        CircuitBreakerConfig(failure_threshold=1), name="callback_test"
    )
    breaker.on_state_change(on_state_change)

    # Cause state change to OPEN
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)

    assert len(state_changes) == 1
    assert state_changes[0] == CircuitState.OPEN


async def test_reset(breaker):
    # Trip the circuit
    for _ in range(2):
        with pytest.raises(ValueError):
            await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.OPEN

    # Reset the circuit
    await breaker.reset()
    assert breaker.state == CircuitState.CLOSED
    assert breaker.failure_count == 0

    # Should be able to make successful calls
    result = await breaker.call(dummy_success)
    assert result == "success"


async def test_force_open(breaker):
    # Force circuit to open
    await breaker.force_open()
    assert breaker.state == CircuitState.OPEN

    # Should fail with CircuitOpenError
    with pytest.raises(CircuitOpenError):
        await breaker.call(dummy_success)


async def test_error_after_failure_window(breaker):
    # Trip the circuit
    for _ in range(2):
        with pytest.raises(ValueError):
            await breaker.call(dummy_failure)

    # Wait outside the failure window
    sleep_time = (
        breaker.config.failure_window + timedelta(seconds=0.1)
    ).total_seconds()
    await asyncio.sleep(sleep_time)

    # Failure after failure window should not reopen the circuit, since previous failures had expired
    with pytest.raises(ValueError):
        await breaker.call(dummy_failure)
    assert breaker.state == CircuitState.HALF_OPEN


async def test_recovery_test_requests_limit(breaker):
    # Trip the circuit
    for _ in range(2):
        with pytest.raises(ValueError):
            await breaker.call(dummy_failure)

    # Wait for recovery timeout
    sleep_time = (
        breaker.config.failure_window + timedelta(seconds=0.1)
    ).total_seconds()
    await asyncio.sleep(sleep_time)

    # First request in HALF_OPEN should be allowed
    result = await breaker.call(dummy_success)
    assert result == "success"
    assert breaker.state == CircuitState.CLOSED


async def test_not_callstate_change_callbacks_if_state_was_not_changed(breaker):
    state_changes = []

    async def on_state_change(circuit_breaker):
        state_changes.append(circuit_breaker.state)

    breaker.on_state_change(on_state_change)

    # Make successful call when already in CLOSED state
    result = await breaker.call(dummy_success)
    assert result == "success"
    assert breaker.state == CircuitState.CLOSED
    assert len(state_changes) == 0  # Callback should not have been called
