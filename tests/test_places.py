from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch

import pytest

from marketplace.models import (
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTAPlaceStatus,
    Place,
    PlaceStatus,
)
from marketplace.places import list_ota_places_by_slugs, refresh_ota_places


@pytest.fixture
def mock_db_conn():
    return AsyncMock()


@pytest.fixture
def ota_config():
    return OTAConfig(
        id=1,
        name="Test OTA",
        provider="test_provider",
        status=OTAConfigStatus.active,
        created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )


@patch("marketplace.places.database.queries.insert_ota_config_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.fetch_ota_places")
@patch("marketplace.places.list_ota_places")
@patch(
    "marketplace.places.database.queries.list_places_with_subplaces",
    new_callable=AsyncMock,
)
async def test_refresh_ota_places_filters_user_places(
    mock_list_subplaces,
    mock_list_places,
    mock_fetch_places,
    mock_find_matching,
    mock_insert,
    mock_db_conn,
    ota_config,
):
    mock_list_subplaces.return_value = []

    current_places = [
        OTAPlace(
            ota_config_id=1, name="SP", extra={"id": "sp"}, status=OTAPlaceStatus.USER
        ),
        OTAPlace(
            ota_config_id=1, name="RJ", extra={"id": "rj"}, status=OTAPlaceStatus.SYSTEM
        ),
    ]
    mock_list_places.return_value = current_places

    fetched_places = [
        OTAPlace(
            ota_config_id=1, name="SP", extra={"id": "sp"}, status=OTAPlaceStatus.SYSTEM
        ),
        OTAPlace(
            ota_config_id=1, name="RJ", extra={"id": "rj"}, status=OTAPlaceStatus.SYSTEM
        ),
    ]
    mock_fetch_places.return_value = fetched_places

    mock_find_matching.return_value = []
    mock_insert.return_value = None

    await refresh_ota_places(mock_db_conn, ota_config)

    mock_find_matching.assert_called_once_with([fetched_places[1]], [])


@patch("marketplace.places.database.queries.insert_ota_config_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.fetch_ota_places")
@patch("marketplace.places.list_ota_places")
@patch(
    "marketplace.places.database.queries.list_places_with_subplaces",
    new_callable=AsyncMock,
)
async def test_refresh_ota_places_includes_new_places(
    mock_list_subplaces,
    mock_list_places,
    mock_fetch_places,
    mock_find_matching,
    mock_insert,
    mock_db_conn,
    ota_config,
):
    mock_list_subplaces.return_value = []
    mock_list_places.return_value = []

    new_place = OTAPlace(
        ota_config_id=1, name="BSB", extra={"id": "bsb"}, status=OTAPlaceStatus.SYSTEM
    )
    mock_fetch_places.return_value = [new_place]
    mock_find_matching.return_value = []
    mock_insert.return_value = None

    await refresh_ota_places(mock_db_conn, ota_config)

    mock_find_matching.assert_called_once_with([new_place], [])


@patch("marketplace.places.database.queries.insert_ota_config_places")
@patch("marketplace.places.fetch_ota_places")
@patch("marketplace.places.list_ota_places")
@patch(
    "marketplace.places.database.queries.list_places_with_subplaces",
    new_callable=AsyncMock,
)
async def test_refresh_ota_places_similarity_match(
    mock_list_subplaces,
    mock_list_places,
    mock_fetch_places,
    mock_insert,
    mock_db_conn,
    ota_config,
):
    mock_list_subplaces.return_value = [
        {"slug": "brasilia", "subplaces": ["brasilia.terminal"]}
    ]
    mock_list_places.return_value = []

    new_place = OTAPlace(
        ota_config_id=1,
        name="Brasília",
        extra={"id": "bsb"},
        status=OTAPlaceStatus.SYSTEM,
    )
    mock_fetch_places.return_value = [new_place]
    mock_insert.return_value = None

    await refresh_ota_places(mock_db_conn, ota_config)

    new_place.place = Place(name="Brasília", slug="brasilia.terminal")
    mock_insert.assert_called_once_with(
        mock_db_conn,
        [
            {
                "ota_config_id": 1,
                "name": "Brasília",
                "extra": {"id": "bsb"},
                "status": OTAPlaceStatus.SYSTEM,
                "place": "brasilia.terminal",
                "created_at": None,
            }
        ],
    )


@patch(
    "marketplace.places.database.queries.list_ota_places_by_slugs",
    new_callable=AsyncMock,
)
async def test_list_ota_places_by_slugs(mock_list_ota_places_by_slugs, mock_db_conn):
    mock_list_ota_places_by_slugs.return_value = [
        {
            "ota_config_id": 1,
            "name": "São Paulo 2",
            "extra": {"id": "sao-paulo-dois"},
            "status": OTAPlaceStatus.SYSTEM,
            "place": {
                "name": "São Paulo",
                "slug": "sao-paulo.terminal-tiete",
                "status": "active",
                "id": 1,
            },
            "created_at": None,
        },
        {
            "ota_config_id": 1,
            "name": "Brasília",
            "extra": {"id": "bsb"},
            "status": OTAPlaceStatus.SYSTEM,
            "place": {
                "name": "Brasília",
                "slug": "brasilia.terminal",
                "status": "active",
                "id": 2,
            },
            "created_at": None,
        },
        {
            "ota_config_id": 1,
            "name": "São Paulo",
            "extra": {"id": "sao-paulo"},
            "status": OTAPlaceStatus.SYSTEM,
            "place": {
                "name": "São Paulo",
                "slug": "sao-paulo.terminal-tiete",
                "status": "active",
                "id": 1,
            },
            "created_at": None,
        },
    ]
    result = await list_ota_places_by_slugs(mock_db_conn, ["sao-paulo", "brasilia"])
    assert result == {
        1: {
            "sao-paulo.terminal-tiete": [
                OTAPlace(
                    ota_config_id=1,
                    name="São Paulo 2",
                    extra={"id": "sao-paulo-dois"},
                    status=OTAPlaceStatus.SYSTEM,
                    place=Place(
                        name="São Paulo",
                        slug="sao-paulo.terminal-tiete",
                        status=PlaceStatus.ACTIVE,
                        id=1,
                    ),
                    created_at=None,
                ),
                OTAPlace(
                    ota_config_id=1,
                    name="São Paulo",
                    extra={"id": "sao-paulo"},
                    status=OTAPlaceStatus.SYSTEM,
                    place=Place(
                        name="São Paulo",
                        slug="sao-paulo.terminal-tiete",
                        status=PlaceStatus.ACTIVE,
                        id=1,
                    ),
                    created_at=None,
                ),
            ],
            "brasilia.terminal": [
                OTAPlace(
                    ota_config_id=1,
                    name="Brasília",
                    extra={"id": "bsb"},
                    status=OTAPlaceStatus.SYSTEM,
                    place=Place(
                        name="Brasília",
                        slug="brasilia.terminal",
                        status=PlaceStatus.ACTIVE,
                        id=2,
                    ),
                    created_at=None,
                ),
            ],
        },
    }
