FROM python:3.13

# application server (uvicorn
RUN pip install gunicorn==23.0.0 \
    uvicorn[standard]==0.30.6 \
    uvicorn-worker==0.2.0

ADD . /app/
WORKDIR /app

RUN pip install .

# OpenTelemetry
RUN pip install \
    opentelemetry-instrumentation \
    opentelemetry-distro \
    opentelemetry-exporter-otlp \
    && opentelemetry-bootstrap --action=install

CMD ["opentelemetry-instrument", "gunicorn", "marketplace.asgi:app", "--bind", "0.0.0.0", "-k", "uvicorn_worker.UvicornWorker", "--keep-alive", "75"]
