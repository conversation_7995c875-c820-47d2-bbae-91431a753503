# links = [{"local_ota_id": 100, "buser_local_slug": "terminal-rodoviario-de-londrina-londrina-pr", "buser_local_nickname": "Terminal Rodoviário de Londrina", "buser_cidade_slug": "londrina-pr", "buser_cidade_name": "Londrina", "ota_config_id": 5}]

import asyncio
import json
from dataclasses import asdict

from marketplace import database
from marketplace.models import OTAConfig, OTAPlace, Place
from src.marketplace import list_ota_configs
from src.marketplace.__main__ import _import_places
from src.marketplace.places import list_ota_places


async def _run(links: list):
    places = {
        Place(name=value[key], slug=value[slug_key])
        for value in links
        for key, slug_key in [
            ("buser_local_nickname", "buser_local_slug"),
            ("buser_cidade_name", "buser_cidade_slug"),
        ]
    }
    await _save_new_places(places)

    map_configs = await _get_configs_map()
    current_ota_places = await _map_ota_places(list(map_configs.values()))

    result = []
    for link in links:
        ota_place = current_ota_places.get(
            (link["ota_config_id"], link["ota_place_id"])
        )
        if not ota_place:
            ota_place = OTAPlace(
                ota_config_id=int(link["ota_config_id"]),
                name=link["name"],
                extra=json.loads(link["extra"]),
            )
        cidade_slug = link["buser_cidade_slug"]
        slug = link["buser_local_slug"]

        if not slug or not cidade_slug:
            continue

        result.append({**asdict(ota_place), "place": slug or cidade_slug})

    print(f"Found: {result}")
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            await database.queries.insert_ota_config_places(db_conn, result)


async def _save_new_places(places: set[Place]):
    await _import_places(list(places))


async def _get_configs_map() -> dict[int, OTAConfig]:
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            all_configs = await list_ota_configs(db_conn)
    return {config.id: config for config in all_configs}


async def _map_ota_places(
    ota_configs: list[OTAConfig],
) -> dict[tuple[int, int], OTAPlace]:
    current_ota_places: dict[tuple[int, int], OTAPlace] = {}
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            for ota_config in ota_configs:
                current_ota_places |= {
                    (ota_place.ota_config_id, ota_place.ota_place_id): ota_place
                    for ota_place in await list_ota_places(
                        db_conn, config_id=ota_config.id
                    )
                }
    return current_ota_places


async def run_async(links: list):
    await _run(links)


def run_sync(links: list):
    try:
        asyncio.run(run_async(links))
    except Exception as e:
        print(f"An error occurred: {e}")
