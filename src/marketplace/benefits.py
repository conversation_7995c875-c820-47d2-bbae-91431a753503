import logging
from dataclasses import dataclass
from typing import override

from yapcache import memoize

from marketplace import database
from marketplace.caching import cache, lock
from marketplace.models import Benefit, OTABenefitStatus
from marketplace.worker import WORKER, QueuedTask, QueuedWorker

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class CreateBenefit(QueuedTask):
    ota_config_id: int
    description: str
    external_code: str
    type: Benefit | None
    status: OTABenefitStatus = OTABenefitStatus.AVAILABLE

    @override
    async def run(self, worker: QueuedWorker):
        await self.insert_on_db(worker)

    @memoize(
        cache,
        ttl=3600,
        lock=lock,
        cache_key=lambda self,
        worker: f"create_benefit:{self.ota_config_id}:{self.external_code}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def insert_on_db(self, worker: Que<PERSON><PERSON>orker):
        async with worker.db_pool.acquire() as conn:
            await database.queries.insert_ota_benefits(
                conn,
                [
                    {
                        "ota_config_id": self.ota_config_id,
                        "description": self.description,
                        "external_code": self.external_code,
                        "type": self.type,
                        "status": self.status,
                    }
                ],
            )


def create_benefit(
    ota_config_id: int,
    external_code: str,
    description: str,
    type: Benefit | None,
):
    WORKER.put_nowait(CreateBenefit(ota_config_id, description, external_code, type))
