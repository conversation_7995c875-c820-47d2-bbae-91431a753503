import logging
from dataclasses import dataclass
from typing import Optional, override

import asyncpg

from marketplace import database
from marketplace.models import OTACompany, OTAConfig, from_dict
from marketplace.worker import WORKER, QueuedTask, QueuedWorker

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class CreateCompanyTask(QueuedTask):
    ota_config: OTAConfig
    external_id: int
    name: Optional[str]
    cnpj: Optional[str]

    @override
    async def run(self, worker: QueuedWorker):
        async with worker.db_pool.acquire() as conn:
            await database.queries.insert_ota_company(
                conn,
                ota_config_id=self.ota_config.id,
                external_id=self.external_id,
                name=self.name,
                cnpj=self.cnpj,
            )


def create_company(
    ota_config: OTAConfig, external_id: int, name: str | None, cnpj: str | None
):
    WORKER.put_nowait(CreateCompanyTask(ota_config, external_id, name, cnpj))


async def update_companies_cnpj(to_update: list[dict]):
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            await database.queries.update_ota_company_cnpj(db_conn, to_update)


async def list_all_ota_companies(
    db_conn: asyncpg.Connection, status: str | None = "active"
):
    records = await database.queries.list_all_ota_companies(
        db_conn,
        status=status,
    )
    return [from_dict(OTACompany, record) for record in records]
