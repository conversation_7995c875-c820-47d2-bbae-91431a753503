import logging
from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass, field
from datetime import date, datetime
from decimal import Decimal
from enum import StrEnum, auto
from typing import Any

import asyncpg
from dacite import DaciteError

from marketplace import database
from marketplace.models import (
    BlockedSeat,
    OTACompany,
    OTAConfig,
    Pax,
    PaxBenefit,
    Place,
    TicketTravel,
    from_dict,
)

logger = logging.getLogger(__name__)


class TicketStatus(StrEnum):
    CREATED = auto()
    EXPIRED = auto()
    PROCESSING = auto()
    ISSUED = auto()
    FAILED = auto()
    CANCELING = auto()
    CANCELED = auto()
    CANCEL_FAILED = auto()
    REFRESHING = auto()
    REFRESHED = auto()
    REFRESH_FAILED = auto()
    UPDATING = auto()
    UPDATED = auto()
    UPDATE_FAILED = auto()


@dataclass
class _BaseTicket:
    client_code: str
    tags: dict[str, str]
    valid_until: datetime
    price: Decimal

    # pax
    pax_name: str
    pax_doc_type: Pax.DocType
    pax_doc: str
    pax_cpf: str | None
    pax_phone: str | None
    pax_birthdate: date | None
    pax_benefit: PaxBenefit | None

    # travel
    code: str
    departure_at: datetime
    arrival_at: datetime
    travel_extra: Any

    # seat
    seat_number: str
    seat_type: str
    seat_floor: int
    seat_row: int
    seat_column: int
    seat_extra: Any
    seat_block_key: str | None


@dataclass
class Ticket(_BaseTicket):
    id: int
    created_at: datetime
    ota_config: OTAConfig
    company: OTACompany
    origin: Place
    destination: Place
    expired: bool
    status_history: list["TicketStatusHistory"] = field(default_factory=lambda: [])

    @property
    def issue_history(self) -> "TicketStatusHistory":
        # When a ticket has multiple cancellation attempts, the 'ISSUED' status will not be the first entry

        for status_history in self.status_history:
            if status_history.status == TicketStatus.ISSUED:
                return status_history

        raise ValueError("Ticket has no issue history")

    @property
    def issued_ticket(self) -> "IssuedTicket":
        issue_result = self.issue_history.result

        if not issue_result:
            raise ValueError("ticket issue result is None")

        try:
            return from_dict(IssuedTicket, issue_result)
        except (DaciteError, AssertionError, KeyError) as ex:
            raise ValueError("ticket issue result is invalid") from ex

    @property
    def last_status_ignoring_refreshing(self) -> "TicketStatusHistory":
        """
        Atualização da passagem, não altera o status dela na OTA. E alguns fluxos precisam do status sem considerar as atualizações.
        """
        for status_history in self.status_history:
            if status_history.status not in (
                TicketStatus.REFRESHING,
                TicketStatus.REFRESHED,
                TicketStatus.REFRESH_FAILED,
            ):
                return status_history
        raise ValueError("Ticket has no status history")

    @property
    def last_status(self) -> "TicketStatusHistory":
        if not self.status_history:
            raise ValueError("Ticket has no status history")
        return self.status_history[0]


@dataclass
class NewTicket(_BaseTicket):
    ota_config_id: int
    company_id: int
    origin: str
    destination: str


@dataclass
class IssuedTicket:
    # BPE / voucher
    localizador: str | None = None
    numero_pedido: str | None = None
    numero_bilhete: str | None = None
    numero_bpe: str | None = None
    chave_bpe: str | None = None
    serie_bpe: str | None = None
    protocolo_autorizacao: str | None = None
    data_autorizacao: datetime | None = None
    nome_agencia: str | None = None
    emissao_em_contigencia: bool | None = None

    # QRcode / barcode
    bpe_qrcode: str | None = None
    monitriip_code: str | None = None
    numero_embarque_code: str | None = None
    embarque_code: str | None = None
    tipo_embarque: str | None = None

    # Valores
    preco: float | None = None
    preco_pedagio: float | None = None
    preco_taxa_embarque: float | None = None
    preco_seguro: float | None = None
    preco_desconto: float | None = None
    preco_total: float | None = None
    outros_tributos: str | None = None

    # Úteis
    poltrona: str | None = None
    plataforma: str | None = None
    linha: str | None = None
    prefixo: str | None = None
    servico: str | None = None

    # Empresa
    cnpj: str | None = None
    endereco_empresa: str | None = None

    extra: Any | None = None


@dataclass
class RefreshedTicket:
    # TODO - Concatenar com retorno do banco de dados e juntar com Ticket
    linha: str
    prefixo: str

    embarque_code: str
    numero_embarque_code: str

    preco_taxa_embarque: Decimal
    preco_seguro: Decimal
    preco_pedagio: Decimal
    outros_tributos: str

    chave_bpe: str
    numero_bpe: str
    serie_bpe: str
    bpe_qrcode: str


@dataclass
class TicketStatusHistory:
    status: TicketStatus
    created_at: datetime
    result: dict | None


class TicketIssuer(ABC):
    def __init__(self, ota_config: OTAConfig):
        self.ota_config = ota_config

    def create_ticket(
        self,
        client_code: str,
        travel: TicketTravel,
        blocked_seat: BlockedSeat,
        pax: Pax,
        price: float,
        tags: dict[str, str],
    ) -> NewTicket:
        if blocked_seat.seat_type is None:
            raise ValueError("seat.seat_type is None")

        company_id = travel.company.external_id
        if company_id is None:
            if travel.company.cnpj is None:
                raise ValueError("not found any identifier for travel.company")

            ota_company = self._get_ota_company_by_cnpj(travel.company.cnpj)
            if ota_company is None:
                raise ValueError("not found any OTACompany for travel.company.cnpj")

            company_id = ota_company.external_id

        return NewTicket(
            client_code=client_code,
            tags=tags,
            valid_until=blocked_seat.best_before,
            # pax
            pax_name=pax.name,
            pax_doc_type=pax.doc_type,
            pax_doc=pax.doc,
            pax_cpf=pax.cpf,
            pax_phone=pax.phone,
            pax_birthdate=pax.birthdate,
            pax_benefit=pax.benefit,
            # travel
            ota_config_id=travel.ota_config_id,
            code=travel.code,
            company_id=company_id,
            origin=travel.origin.slug,
            destination=travel.destination.slug,
            departure_at=travel.departure_at,
            arrival_at=travel.arrival_at,
            travel_extra=travel.extra,
            # seat
            seat_number=blocked_seat.number,
            seat_floor=blocked_seat.floor,
            seat_row=blocked_seat.row,
            seat_column=blocked_seat.column,
            seat_extra=blocked_seat.extra,
            seat_block_key=str(blocked_seat.block_key),
            seat_type=blocked_seat.seat_type,
            price=Decimal.from_float(price),
        )

    def update_ticket(
        self,
        ticket: Ticket,
        blocked_seat: BlockedSeat | None,
        pax: Pax | None,
        price: float | None,
        tags: dict[str, str] | None,
    ) -> Ticket:
        if blocked_seat is not None:
            ticket.valid_until = blocked_seat.best_before
            ticket.seat_number = blocked_seat.number
            ticket.seat_floor = blocked_seat.floor
            ticket.seat_row = blocked_seat.row
            ticket.seat_column = blocked_seat.column
            ticket.seat_extra = blocked_seat.extra
            ticket.seat_block_key = str(blocked_seat.block_key)
            if blocked_seat.seat_type is not None:
                ticket.seat_type = blocked_seat.seat_type
        if pax is not None:
            ticket.pax_name = pax.name
            ticket.pax_doc_type = pax.doc_type
            ticket.pax_doc = pax.doc
            ticket.pax_cpf = pax.cpf
            ticket.pax_phone = pax.phone
            ticket.pax_birthdate = pax.birthdate
            ticket.pax_benefit = pax.benefit
        if price is not None:
            ticket.price = Decimal.from_float(price)
        if tags is not None:
            ticket.tags = tags
        return ticket

    def _get_ota_company_by_cnpj(self, cnpj: str) -> OTACompany | None:
        company = [
            c for c in self.ota_config.companies_mapping.values() if c.cnpj == cnpj
        ][0]
        if company is None:
            return None

        return company

    @abstractmethod
    async def issue_ticket(self, ticket: Ticket) -> IssuedTicket: ...

    @abstractmethod
    async def cancel_ticket(self, ticket: Ticket) -> dict: ...

    @abstractmethod
    async def refresh_ticket(self, ticket: Ticket) -> RefreshedTicket: ...

    @abstractmethod
    async def refresh_ticket_from_extra(self, extra: dict) -> RefreshedTicket: ...


async def insert_ticket(db_conn: asyncpg.Connection, ticket: NewTicket) -> int:
    from marketplace.places import slug_to_ltree

    try:
        row = asdict(ticket)
        row["origin"] = slug_to_ltree(row["origin"])
        row["destination"] = slug_to_ltree(row["destination"])

        ticket_id = await database.queries.insert_ticket(db_conn, **row)
        await insert_ticket_status(db_conn, ticket_id, TicketStatus.CREATED)
        return ticket_id
    except asyncpg.UniqueViolationError:
        raise TicketAlreadyExists(f'Ticket "{ticket.client_code}" already exists.')


async def update_ticket(
    db_conn: asyncpg.Connection, ticket_id: int, ticket: Ticket
) -> int:
    row = asdict(ticket)
    ticket_id = await database.queries.update_ticket(db_conn, **row)
    return ticket_id


async def insert_ticket_status(
    db_conn,
    ticket_id: int,
    status: TicketStatus,
    result: Any | None = None,
):
    await database.queries.insert_ticket_status(
        db_conn,
        ticket_id=ticket_id,
        status=status,
        result=result,
    )


async def list_tickets(db_conn: asyncpg.Connection, client_code: str | None = None):
    rows = await database.queries.list_tickets(db_conn, client_code=client_code)
    return [from_dict(Ticket, row) for row in rows]


async def get_ticket_by_id(db_conn: asyncpg.Connection, ticket_id: int):
    row = await database.queries.get_ticket_by_id(db_conn, ticket_id=ticket_id)
    return from_dict(Ticket, row)


class TicketAlreadyExists(RuntimeError): ...


class TicketInvalidStatus(RuntimeError): ...
