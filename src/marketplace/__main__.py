import asyncio
import csv
import json
import typing
from pathlib import Path

import click
import httpx

from marketplace import companies, database, ota_config, places
from marketplace.models import Place


@click.group()
def cli(): ...


SUPPORTED_OTAS = ["eulabs", "other"]


class JSONParamType(click.ParamType):
    def convert(self, value: str, param, ctx):
        if not isinstance(value, str):
            return value

        try:
            return json.loads(value)
        except json.JSONDecodeError as exc:
            self.fail(f"Invalid JSON: {exc.msg}", param, ctx)


@cli.group()
def db(): ...


@db.command()
@click.option("-n", type=int, required=False, default=None)
@click.option("--fake", type=bool, required=False, default=False, is_flag=True)
def up(n, fake):
    available = database.list_available_migrations()
    applied = set(asyncio.run(database.list_applied_migrations()))
    pending = [name for name in available if name not in applied]

    if n is None:
        n = len(available)

    for name, _ in zip(pending, range(n)):
        print("Running", name, "...")
        asyncio.run(database.apply_migration(name, fake))
        print(f"[x] {name}")


@db.command()
def status():
    applied = set(asyncio.run(database.list_applied_migrations()))
    available = database.list_available_migrations()
    for name in available:
        print(f"[{'x' if name in applied else ' '}] {name}")


@cli.command()
@click.option("--file", type=click.File(), required=True)
def import_places(file: typing.IO[typing.Any]):
    csv_reader = csv.DictReader(file)
    places = [
        Place(name=row["name"], slug=row["slug"].replace("-", "_"))
        for row in csv_reader
    ]
    asyncio.run(_import_places(places))


async def _import_places(items: list[Place]):
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            await places.insert_places(db_conn, items)


@cli.command()
def refresh_places():
    asyncio.run(_refresh_places())


@cli.command()
def upsert_otas():
    asyncio.run(_upsert_otas())


async def _upsert_otas():
    async with httpx.AsyncClient():
        configs_path = Path("configs/otas/")
        for json_file in configs_path.glob("*.json"):
            try:
                with open(json_file, "r") as file:
                    data = json.load(file)

                config = await ota_config.upsert_ota_config(data)

                print(
                    f"Successfully uploaded {json_file.name}. Resulted in OTAConfig id={config.id}"
                )
            except Exception as e:
                print(f"Error processing {json_file.name}: {e}")


async def _refresh_places():
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            await places.refresh_all_ota_places(db_conn)


@cli.command()
@click.option("--file", type=click.File(), required=True)
def set_company_cnpj(file: typing.IO[typing.Any]):
    csv_reader = csv.DictReader(file)
    to_update = [
        {
            "cnpj": str(row["cnpj"]),
            "ota_config_id": int(row["ota_config_id"]),
            "external_id": int(row["company_external_id"]),
        }
        for row in csv_reader
    ]
    asyncio.run(companies.update_companies_cnpj(to_update))


if __name__ == "__main__":
    cli()
