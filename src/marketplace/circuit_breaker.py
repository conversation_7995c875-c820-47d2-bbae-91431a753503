import logging
from collections import deque
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Awaitable, Callable, Optional, TypeVar

T = TypeVar("T")  # Return type of the protected function
E = TypeVar("E", bound=Exception)  # Type of exceptions to handle


class CircuitState(Enum):
    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Circuit is broken
    HALF_OPEN = "half_open"  # Testing if service is back


@dataclass
class CircuitBreakerConfig:
    """Configuration for Circuit Breaker behavior"""

    # Failure threshold before opening circuit
    failure_threshold: int = 5

    # Time window for counting failures (in seconds)
    failure_window: timedelta = timedelta(seconds=60)

    # How long to wait before attempting recovery (in seconds)
    recovery_timeout: timedelta = timedelta(seconds=30)

    # Maximum number of requests to allow in half-open state
    recovery_test_requests: int = 1


class CircuitBreakerError(Exception):
    """Base exception for circuit breaker errors"""

    pass


class CircuitOpenError(CircuitBreakerError):
    """Raised when attempting to call through an open circuit"""

    pass


class CircuitBreaker:
    def __init__(
        self,
        config: Optional[CircuitBreakerConfig] = None,
        name: str = "default",
        state_change_callbacks: list[Callable[["CircuitBreaker"], Awaitable[None]]]
        | None = None,
        ignored_exceptions: Optional[tuple[type[Exception], ...]] = None,
    ):
        self.config = config or CircuitBreakerConfig()
        self.name = name
        self._state = CircuitState.CLOSED
        self._last_failure_time: Optional[datetime] = None
        self._failures_time: deque[datetime] = deque()
        self._recovery_test_counter = 0
        self._state_change_callbacks = state_change_callbacks or [
            default_circuit_breaker_callback
        ]
        self._ignored_exceptions = ignored_exceptions or ()

    @property
    def state(self) -> CircuitState:
        """Current state of the circuit breaker"""
        return self._state

    @property
    def failure_count(self) -> int:
        """Number of failures in the current window"""
        return len(self._failures_time)

    async def call(
        self,
        protected_operation: Callable[..., Awaitable[T]],
        *args: Any,
        **kwargs: Any,
    ) -> T:
        """
        Execute the protected operation through the circuit breaker

        Args:
            protected_operation: Async function to protect
            *args: Positional arguments for the protected operation
            **kwargs: Keyword arguments for the protected operation

        Returns:
            Result from the protected operation

        Raises:
            CircuitOpenError: When circuit is open
            Exception: Any exception from the protected operation
        """
        match self._state:
            case CircuitState.OPEN:
                if self._should_attempt_recovery():
                    await self.force_half_open()
                    self._recovery_test_counter += 1
                else:
                    raise CircuitOpenError(f"Circuit {self.name} is OPEN")

            case CircuitState.HALF_OPEN:
                if self._recovery_test_counter >= self.config.recovery_test_requests:
                    await self.force_open()
                    raise CircuitOpenError(
                        f"Circuit {self.name} recovery test limit reached"
                    )
                self._recovery_test_counter += 1

        try:
            result = await protected_operation(*args, **kwargs)
        except self._ignored_exceptions:
            raise
        except Exception:
            await self._handle_failure()
            raise
        else:
            await self._handle_success()
            return result

    def on_state_change(
        self, callback: Callable[["CircuitBreaker"], Awaitable[None]]
    ) -> None:
        """Register a callback for state changes"""
        self._state_change_callbacks.append(callback)

    async def reset(self) -> None:
        """Force reset the circuit breaker to closed state"""
        self._state = CircuitState.CLOSED
        self._failures_time.clear()
        self._recovery_test_counter = 0
        await self._notify_state_change()

    async def force_open(self) -> None:
        """Force the circuit breaker to open state"""
        self._state = CircuitState.OPEN
        self._last_failure_time = datetime.now()
        await self._notify_state_change()

    async def force_half_open(self):
        """Force the circuit breaker to half-open state"""
        self._state = CircuitState.HALF_OPEN
        await self._notify_state_change()

    async def _handle_success(self) -> None:
        """Handle successful operation"""
        if self._state in (CircuitState.CLOSED, CircuitState.HALF_OPEN):
            self._failures_time.clear()
            self._recovery_test_counter = 0

            if self._state == CircuitState.HALF_OPEN:
                self._state = CircuitState.CLOSED
                await self._notify_state_change()

    async def _handle_failure(self) -> None:
        """Handle operation failure"""
        now = datetime.now()
        self._failures_time.append(now)
        self._last_failure_time = now
        self._clear_old_failures_time()

        if self._state != CircuitState.CLOSED:
            # If the circuit is already open, do nothing
            # If it's half-open, allow up to recovery_test_requests attempts
            # If all attempts fail, the circuit will reopen
            return

        if self.failure_count >= self.config.failure_threshold:
            self._state = CircuitState.OPEN
            await self._notify_state_change()

    async def _notify_state_change(self) -> None:
        """Notify all registered callbacks of state change"""
        for callback in self._state_change_callbacks:
            await callback(self)

    def _should_attempt_recovery(self) -> bool:
        """Check if enough time has passed to attempt recovery"""
        assert isinstance(self._last_failure_time, datetime)
        return (datetime.now() - self._last_failure_time) > self.config.recovery_timeout

    def _clear_old_failures_time(self) -> None:
        """Remove failures outside the current window"""
        now = datetime.now()
        while (
            self._failures_time
            and (now - self._failures_time[0]) > self.config.failure_window
        ):
            self._failures_time.popleft()


class NullCircuitBreaker:
    """A no-op implementation of Circuit Breaker that simply executes operations without any circuit breaking behavior.
    Useful for testing or when circuit breaking needs to be disabled."""

    def __init__(self, *args, **kwargs):
        self.state = CircuitState.CLOSED

    async def call(
        self,
        protected_operation: Callable[..., Awaitable[T]],
        *args: Any,
        **kwargs: Any,
    ) -> T:
        """Execute the protected operation directly without any circuit breaking logic.

        Args:
            protected_operation: Async function to execute
            *args: Positional arguments for the protected operation
            **kwargs: Keyword arguments for the protected operation

        Returns:
            Result from the protected operation
        """
        return await protected_operation(*args, **kwargs)


logger = logging.getLogger(__name__)


async def default_circuit_breaker_callback(circuit_breaker: CircuitBreaker):
    logger.warning(
        "Circuit breaker %s state=%s",
        circuit_breaker.name,
        circuit_breaker.state,
    )
