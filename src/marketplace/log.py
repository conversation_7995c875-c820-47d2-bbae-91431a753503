import json
import logging
import random
from datetime import datetime

from starlette.requests import HTTPConnection

from marketplace.correlation_context.context import correlation_id_var


class SampleRateFilter(logging.Filter):
    def __init__(self, rate=1.0):
        self.rate = rate

    def filter(self, record: logging.LogRecord):
        return random.random() < self.rate


class CorrelationIdFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        record.correlation_id = correlation_id_var.get()
        return True


requestslogger = logging.getLogger("requestslogger")

REQUEST_FIELDS_WHITE_LIST = {
    "travel",
    "blocked_seat",
    "seat",
    "benefit",
}

RESPONSE_FIELDS_WHITE_LIST = {
    # errors
    "code",
    "message",
    # seat
    "number",
    "block_key",
    "extra_rodoviaria",
    "best_before",
    "benefit",
    "extra",
}

MAX_LENGTH = 1024


def log_request(
    status_code: int,
    http_connection: HTTPConnection,
    request_body: bytes,
    response_body: bytes,
    duration: float,
    timestamp: datetime,
    exception: Exception | None = None,
):
    method = http_connection["method"]
    extra = dict(
        request_id=http_connection.headers.get("X-Request-ID", "-"),
        duration=duration,
        request_time=duration,
        path=http_connection.url.path,
        method=method,
        status_code=status_code,
        status=status_code,
        time=timestamp,
    )

    try_add_payload_data_on_extra(
        http_connection.query_params, request_body, response_body, extra
    )
    exc_msg = ""
    if exception:
        exc_msg = f"{exception.__class__.__name__}: {str(exception)}"
        extra["error"] = exc_msg
    requestslogger.log(
        get_log_level(status_code),
        "%s %s %s %s",
        method,
        status_code,
        http_connection.url.path,
        exc_msg,
        extra=extra,
        exc_info=exception,
    )


def try_add_payload_data_on_extra(
    request_query_params, request_body, response_body, extra
):
    req_json = ""
    query_params = ""

    try:
        query_params = str(request_query_params)[:MAX_LENGTH]
    except Exception:
        pass

    try:
        request_body = get_redacted_payload_as_str(
            request_body, REQUEST_FIELDS_WHITE_LIST
        )
        req_json = f"&json={request_body}"
    except Exception:
        pass

    extra["request_query"] = f"{query_params}{req_json}"

    try:
        extra["response"] = get_redacted_payload_as_str(
            response_body, RESPONSE_FIELDS_WHITE_LIST
        )
    except Exception:
        pass


def get_redacted_payload_as_str(payload: bytes, white_list_keys: set) -> str:
    def redact_dict(data_dict: dict, white_list_keys: set) -> dict:
        filtered = {}
        for k, v in data_dict.items():
            if k in white_list_keys:
                filtered[k] = v
            else:
                filtered[k] = "[redacted]"
        return filtered

    payload_dict = redact_dict(json.loads(payload), white_list_keys)
    return json.dumps(payload_dict)[:MAX_LENGTH]


def get_log_level(status_code):
    if status_code == 500 or status_code == 0:
        return logging.ERROR

    if status_code >= 400:
        return logging.WARNING

    if status_code > 500:
        # é comum retornarmos 502 e 504 em casos de erros de conexão da OTA
        # não queremos poluir o sentry com esses erros
        return logging.WARNING

    return logging.INFO
