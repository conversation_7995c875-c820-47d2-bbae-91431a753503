import contextlib
import logging
import time
from datetime import datetime, timezone
from http import H<PERSON><PERSON><PERSON>us
from typing import Async<PERSON><PERSON><PERSON>, TypedDict

import asyncpg
from opentelemetry.instrumentation.starlette import <PERSON><PERSON><PERSON>nstrumentor
from slowapi import _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from starlette.applications import Starlette
from starlette.requests import HTTPConnection, Request
from starlette.responses import JSONResponse
from starlette.routing import Route

from marketplace import (
    database,
)
from marketplace.background_tasks import launch_background_task
from marketplace.caching import cache, redis_client
from marketplace.correlation_context.middleware import CorrelationIdMiddleware
from marketplace.log import log_request
from marketplace.otas.exception import (
    OTAConnectionError,
    OTATimeoutException,
    OTATooManyRequests,
)
from marketplace.rate_limit import limiter
from marketplace.settings import DATABASE_URL
from marketplace.views import (
    OrjsonResponse,
    benefits,
    otas,
    places,
    search,
    seats,
    tickets,
    travels,
)
from marketplace.worker import WORKER

logger = logging.getLogger()


async def health(request: Request):
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        return OrjsonResponse(
            {
                "ok": True,
                "services": {
                    "db": await db_conn.fetchval("SELECT true"),
                    "cache": await cache.set("health", True, ttl=1) is None,
                },
            }
        )


async def debug_threads(request: Request):
    import threading

    threads = threading.enumerate()
    return OrjsonResponse(
        {
            "count": len(threads),
            "names": [t.name for t in threads],
        }
    )


class LogRequestMiddleware:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        request_body = b""
        response_body = b""
        response_status = 0

        async def receive_caching_body():
            nonlocal request_body

            message = await receive()
            if message["type"] == "http.request":
                request_body += message.get("body", b"")

            return message

        async def send_caching_response(message):
            nonlocal response_body
            nonlocal response_status

            if message["type"] == "http.response.start":
                response_status = message["status"]
            if message["type"] == "http.response.body":
                response_body += message.get("body", b"")

            await send(message)

        t0 = time.monotonic()
        exc = None
        try:
            await self.app(scope, receive_caching_body, send_caching_response)
        except Exception as ex:
            exc = ex
            raise
        finally:
            duration = time.monotonic() - t0
            http_connection = HTTPConnection(scope)
            log_request(
                response_status,
                http_connection,
                request_body,
                response_body,
                duration,
                datetime.now(timezone.utc),
                exc,
            )


class State(TypedDict):
    db_pool: asyncpg.Pool


@contextlib.asynccontextmanager
async def _lifespan(app: Starlette) -> AsyncIterator[State]:
    await launch_background_task(WORKER.run())

    async with database.create_connection_pool(DATABASE_URL) as db_pool:
        yield {
            "db_pool": db_pool,
        }

    # Close the Redis connection when the application is shutting down
    await redis_client.aclose()


app = Starlette(
    debug=True,
    routes=[
        Route("/health", health),
        Route("/debug/threads", debug_threads),
        Route("/search/places", search.search_places),
        Route("/search/travels", search.search_travels),
        Route("/search/travels/hot", search.search_travels_hot),
        Route("/search/travels/hot/v3", search.search_travels_hot_v3),
        Route("/travels/{travel_code}/price", travels.travel_price),
        Route("/travels/itinerary", travels.travel_itinerary, methods=["POST"]),
        Route("/travels/seating-map", seats.travel_seating_map, methods=["POST"]),
        Route("/travels/block-seat", seats.travel_block_seat, methods=["POST"]),
        Route("/travels/unblock-seat", seats.travel_unblock_seat, methods=["POST"]),
        Route("/tickets", tickets.list_tickets, methods=["GET"]),
        Route("/tickets", tickets.create_ticket, methods=["POST"]),
        Route("/tickets/{ticket_id:int}", tickets.get_ticket, methods=["GET"]),
        Route("/tickets/{ticket_id:int}", tickets.update_ticket, methods=["PUT"]),
        Route(
            "/tickets/{ticket_id:int}/confirm",
            tickets.confirm_ticket,
            methods=["POST"],
        ),
        Route(
            "/tickets/{ticket_id:int}/cancel",
            tickets.cancel_ticket,
            methods=["POST"],
        ),
        Route(
            "/tickets/{ticket_id:int}/refresh",
            tickets.refresh_ticket,
            methods=["POST"],
        ),
        Route("/otas", otas.list_ota_configs),
        Route("/otas", otas.save_ota_config, methods=["POST"]),
        Route("/otas/companies", otas.list_ota_configs_companies),
        Route("/otas/{config_id}", otas.get_ota_config),
        Route(
            "/otas/{config_id}/status",
            otas.update_ota_config_status,
            methods=["POST"],
        ),
        Route("/otas/{config_id}/places", otas.list_ota_places),
        Route("/otas/{config_id}/places/refresh", otas.refresh_places_from_ota),
        Route("/otas/{config_id}/places", otas.save_ota_place, methods=["POST"]),
        Route(
            "/otas/{config_id}/places/batch", otas.batch_add_ota_place, methods=["POST"]
        ),
        Route(
            "/otas/{config_id}/seat-types",
            otas.save_ota_seat_type,
            methods=["POST"],
        ),
        Route("/otas/{config_id}/companies", otas.save_ota_company, methods=["POST"]),
        Route("/places", places.list_places),
        Route("/places", places.save_place, methods=["POST"]),
        Route(
            "/places/{place_id}/status",
            places.update_place_status,
            methods=["POST"],
        ),
        Route(
            "/otas/{config_id}/benefits/refresh",
            benefits.refresh_benefits_from_ota,
            methods=["POST"],
        ),
        Route(
            "/otas/{config_id}/benefit",
            benefits.save_ota_benefit,
            methods=["POST"],
        ),
    ],
    lifespan=_lifespan,
    exception_handlers={
        OTATooManyRequests: lambda request, ex: JSONResponse(
            {
                "code": "TOO_MANY_REQUESTS",
                "message": str(ex) or "Too many requests",
            },
            status_code=HTTPStatus.TOO_MANY_REQUESTS,
        ),
        OTATimeoutException: lambda request, ex: JSONResponse(
            {
                "code": "TIMEOUT_ERROR",
                "message": str(ex) or "Timeout error",
            },
            status_code=HTTPStatus.GATEWAY_TIMEOUT,
        ),
        OTAConnectionError: lambda request, ex: JSONResponse(
            {
                "code": "CONNECTION_ERROR",
                "message": str(ex) or "Connection error",
            },
            status_code=HTTPStatus.BAD_GATEWAY,
        ),
    },
)
StarletteInstrumentor.instrument_app(app)
app.add_middleware(LogRequestMiddleware)
app.add_middleware(CorrelationIdMiddleware)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)  # type: ignore[arg-type]
