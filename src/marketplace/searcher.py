import asyncio
import itertools
import logging
import time
from asyncio import Task
from datetime import date
from typing import Any, Optional

from opentelemetry import trace
from sentry_sdk import capture_exception
from yapcache import MemoizeResult
from yapcache.cache_item import NotFound

from marketplace.benefits import create_benefit
from marketplace.caching import cache
from marketplace.companies import create_company
from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTABenefit,
    OTABenefitStatus,
    OTACompany,
    OTAConfig,
    OTAPlace,
    OTASeatType,
    OTASeatTypeStatus,
    ReducedOTAConfig,
    SearchResult,
    Seat,
    SeatMap,
    Travel,
)
from marketplace.otas.exception import (
    OTAConnectionError,
    OTANotJSONResponse,
    OTASearcherException,
    OTATimeoutException,
    OTATooManyRequests,
)
from marketplace.seat_types import create_seat_type

ONE_MINUTE = 60

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)


class OTASearcher:
    def __init__(self, ota_config: OTAConfig):
        self.ota_config = ota_config

    def __repr__(self):
        return f"<{self.__class__.__qualname__} ota_config={repr(self.ota_config)}>"

    async def search_multiple(
        self,
        origins: list[OTAPlace],
        destinations: list[OTAPlace],
        departure_date: date,
        timeout: float,
    ) -> list[SearchResult]:
        search_tasks = {}

        for origin, destination in itertools.product(origins, destinations):
            task = asyncio.create_task(
                self.search_with_trace(origin, destination, departure_date)
            )
            search_tasks[task] = (origin, destination)

        done, pending = await asyncio.wait(search_tasks, timeout=timeout)

        results: list[SearchResult] = []
        for task in done | pending:
            origin, destination = search_tasks[task]
            if task in pending:
                travels = None
                error = "timeout_error"
                task.add_done_callback(search_task_result_handler)
            else:
                travels, error = search_task_result_handler(task)
            results.append(
                SearchResult(
                    ota_config=ReducedOTAConfig(
                        id=self.ota_config.id,
                        name=self.ota_config.name,
                        companies=self.ota_config.companies
                        if self.ota_config.companies
                        else [],
                    ),
                    origin=origin,
                    destination=destination,
                    departure_date=departure_date,
                    travels=travels,
                    error=error,
                )
            )

        return results

    async def search_travels(
        self, origin: OTAPlace, destination: OTAPlace, departure_date: date
    ):
        pass

    async def search_with_trace(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        with tracer.start_as_current_span("ota_search") as span:
            span.set_attributes(
                {
                    "provider": self.ota_config.provider,
                    "ota_config_id": self.ota_config.id,
                    "ota_config_name": self.ota_config.name,
                    "origin": origin.place.slug if origin.place else origin.name,
                    "destination": destination.place.slug
                    if destination.place
                    else destination.name,
                    "departure_at": departure_date.isoformat(),
                }
            )
            results = await self.search(origin, destination, departure_date)
            span.set_attribute("count", len(results))
        return results

    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        raise NotImplementedError

    async def list_places(self) -> list[OTAPlace]:
        raise NotImplementedError

    async def list_benefits(self) -> set[OTABenefit]:
        raise NotImplementedError

    async def travel_price(self, travel: Travel) -> float | None:
        raise NotImplementedError

    async def available_seats(
        self, travel: InputTravel, search_for_price: bool = True
    ) -> SeatMap:
        raise NotImplementedError

    async def block_seat(
        self, travel: InputTravel, seat: Seat, benefit: Benefit = Benefit.NORMAL
    ) -> BlockedSeat:
        raise NotImplementedError

    async def unblock_seat(
        self, travel: InputTravel, blocked_seat: BlockedSeat
    ) -> bool:
        raise NotImplementedError

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        raise NotImplementedError

    def _get_ota_seat_type_by_name(self, name: str) -> Optional[OTASeatType]:
        strip_name = name.strip()
        seat_type = self.ota_config.seat_types_mapping.get(strip_name)
        if seat_type is None:
            logger.warning(
                "%s seat type not found, this will be created soon.", strip_name
            )
            create_seat_type(self.ota_config, strip_name)
            return None

        if seat_type.status == OTASeatTypeStatus.NOT_LISTED:
            return None

        if seat_type.status == OTASeatTypeStatus.AVAILABLE:
            return seat_type

        return seat_type

    def _get_benefit(
        self,
        external_id: str,
        description: str | None = None,
        type: Benefit | None = None,
    ) -> Benefit | None:
        benefit = self.ota_config.benefits_mapping.get(external_id)
        if benefit is None:
            if description:
                create_benefit(self.ota_config.id, external_id, description, type)
            return type

        if benefit.status == OTABenefitStatus.IGNORED:
            return None

        return benefit.type

    def _get_available_benefits(self) -> list[Benefit]:
        return [
            b.type
            for b in self.ota_config.benefits_mapping.values()
            if b.status == OTABenefitStatus.AVAILABLE and b.type is not None
        ] or [Benefit.NORMAL]

    def _get_ota_company(
        self, external_id: int, name: str | None = None, cnpj: str | None = None
    ) -> Optional[OTACompany]:
        company = self.ota_config.companies_mapping.get(external_id)
        if company is None:
            logger.warning(
                "%s OTACompany not found, this will be created soon.", external_id
            )
            create_company(self.ota_config, external_id, name, cnpj)
            return None

        if not company.cnpj:
            logger.warning(
                "%s OTACompany CNPJ not found, will not return company.", external_id
            )
            return None

        return company

    def _has_few_seats_travel(self, search_response: Any) -> bool:
        return False

    async def _dynamic_search_ttl(
        self, result: Any, origin_id: int, destination_id: int, departure_date: date
    ) -> int:
        cache_key = f"empty_responses_counter:{self.ota_config.id}:{origin_id}:{destination_id}:{departure_date}"
        current_counter = await cache.get(cache_key)
        current_counter = (
            current_counter.value if not isinstance(current_counter, NotFound) else 0
        )
        if not result:
            current_counter += 1
            await cache.set(
                cache_key,
                current_counter,
                ttl=self.ota_config.search_cache.empty_responses_window,
            )
        else:
            # TODO: add delete method to yapcache
            await cache.set(cache_key, current_counter, ttl=ONE_MINUTE)

        if current_counter > self.ota_config.search_cache.max_empty_responses:
            return self.ota_config.search_cache.ttl_not_available

        if result and self._has_few_seats_travel(result):
            return self.ota_config.search_cache.ttl_few_seats

        return self.ota_config.search_cache.ttl

    def _dynamic_best_before(self, cache_ttl: int):
        if cache_ttl > self.ota_config.search_cache.ttl:
            return float("inf")
        return time.time() + self.ota_config.search_cache.stale_after

    def _trace_process_result(self, result: MemoizeResult):
        span = trace.get_current_span()
        span.set_attribute("cache_status", str(result.cache_status))
        return result.result


def search_task_result_handler(task: Task) -> tuple[list[Travel] | None, str | None]:
    try:
        return task.result(), None
    except OTATimeoutException:
        return None, "timeout_error"
    except OTATooManyRequests:
        return None, "too_many_requests"
    except (OTASearcherException, OTANotJSONResponse):
        return None, "communication_error"
    except OTAConnectionError:
        return None, "connection_error"
    except Exception as ex:
        capture_exception(ex)
        return None, "internal_error"
