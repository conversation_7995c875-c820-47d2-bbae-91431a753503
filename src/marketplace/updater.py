from dataclasses import dataclass
from datetime import date
from typing import Any, AsyncGenerator, Awaitable, Callable

from marketplace.models import OTAConfig, Travel


@dataclass
class OTAUpdaterTask:
    type: str
    ota_config_id: int
    payload: Any  #: OTA specific payload


type UpdateCriteria = Callable[[OTAUpdaterTask], Awaitable[bool]]


class OTAUpdater:
    def __init__(self, ota_config: OTAConfig, criterias: list[UpdateCriteria] = []):
        self.ota_config = ota_config
        self.criterias = criterias

    def generate_tasks(
        self, start_date: date, end_date: date
    ) -> AsyncGenerator[OTAUpdaterTask, None]:
        raise NotImplementedError

    async def should_process_task(self, task: OTAUpdaterTask):
        for criteria in self.criterias:
            if not await criteria(task):
                return False
        return True

    async def process_task(self, task: OTAUpdaterTask) -> list[Travel]:
        raise NotImplementedError
