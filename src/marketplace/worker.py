import asyncio
import logging

from marketplace import database
from marketplace.settings import DATABASE_URL

logger = logging.getLogger(__name__)


class QueuedTask:
    async def run(self, worker: "QueuedWorker"):
        raise NotImplementedError


class QueuedWorker:
    def __init__(self):
        self._queue: asyncio.Queue[QueuedTask] = asyncio.Queue()

    def put_nowait(self, task: QueuedTask):
        self._queue.put_nowait(task)

    async def run(self):
        async with database.create_connection_pool(DATABASE_URL) as db_pool:
            self.db_pool = db_pool
            while task := await self._queue.get():
                logger.info("Received %s", task)

                await task.run(self)


WORKER = QueuedWorker()
