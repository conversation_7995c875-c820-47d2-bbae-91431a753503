import importlib
import logging.config

import asyncpg
import sentry_sdk

from marketplace import database
from marketplace.instrumentation.sentry_dedupe import SentryDedupeIntegration
from marketplace.log import CorrelationIdFilter, SampleRateFilter
from marketplace.models import OTAConfig, from_dict
from marketplace.searcher import OTASearcher
from marketplace.settings import (
    HTTP_REQUESTS_OUT_LOG_LEVEL,
    HTTP_REQUESTS_OUT_LOG_SAMPLE_RATE,
    LOG_LEVEL,
    SENTRY_DSN,
    SENTRY_PROFILES_SAMPLE_RATE,
    SENTRY_TRACES_SAMPLE_RATE,
)
from marketplace.tickets import TicketIssuer

logger = logging.getLogger(__name__)

logging.config.dictConfig(
    {
        "version": 1,
        "formatters": {
            "default": {
                "class": "pythonjsonlogger.json.JsonFormatter",
                "format": "%(asctime)s %(levelname)s %(correlation_id)s %(message)s %(status_code)s %(duration)s %(error)s %(response)s",
            }
        },
        "filters": {
            "http_requests_out": {
                "()": SampleRateFilter,
                "rate": HTTP_REQUESTS_OUT_LOG_SAMPLE_RATE,
            },
            "correlation": {"()": CorrelationIdFilter},
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "default",
            }
        },
        "root": {
            "level": LOG_LEVEL,
            "handlers": ["console"],
            "filters": ["correlation"],
        },
        "loggers": {
            "marketplace.otas.http": {
                "level": HTTP_REQUESTS_OUT_LOG_LEVEL,
                "handlers": ["console"],
                "filters": ["http_requests_out", "correlation"],
                "propagate": False,
            },
            "requestslogger": {
                "level": LOG_LEVEL,
                "handlers": ["console"],
                "filters": ["correlation"],
                "propagate": False,
            },
        },
    }
)


sentry_sdk.init(
    dsn=SENTRY_DSN,
    traces_sample_rate=SENTRY_TRACES_SAMPLE_RATE,
    profiles_sample_rate=SENTRY_PROFILES_SAMPLE_RATE,
    integrations=[SentryDedupeIntegration()],
)


OTA_SEARCHERS = {
    "totalbus": "marketplace.otas.totalbus.searcher.TotalbusSearcher",
    "praxio": "marketplace.otas.praxio.searcher.PraxioSearcher",
    "eulabs": "marketplace.otas.eulabs.searcher.EulabsSearcher",
    "vexado": "marketplace.otas.vexado.searcher.VexadoSearcher",
    "ti_sistemas": "marketplace.otas.ti_sistemas.searcher.TiSistemasSearcher",
    "guichepass": "marketplace.otas.guichepass.searcher.GuichepassSearcher",
}

OTA_TICKET_ISSUERS = {
    "totalbus": "marketplace.otas.totalbus.ticket_issuer.TotalbusTicketIssuer",
    "eulabs": "marketplace.otas.eulabs.ticket_issuer.EulabsTicketIssuer",
    "praxio": "marketplace.otas.praxio.ticket_issuer.PraxioTicketIssuer",
    "guichepass": "marketplace.otas.guichepass.ticket_issuer.GuichepassTicketIssuer",
}


async def list_ota_configs(
    db_conn: asyncpg.Connection, status: str | None = "active"
) -> list[OTAConfig]:
    ota_configs = [
        from_dict(OTAConfig, dict(record))
        for record in await database.queries.list_ota_configs(db_conn)
        if (record["status"] == status if status is not None else True)
    ]
    return ota_configs


async def get_ota_config_by_id(
    db_conn: asyncpg.Connection, config_id: int
) -> OTAConfig:
    record = await database.queries.get_ota_config_from_id(db_conn, id=config_id)
    return from_dict(OTAConfig, dict(record))


async def get_searcher_from_ota_config_id(
    db_conn: asyncpg.Connection, ota_config_id: int
) -> OTASearcher:
    ota_config = await get_ota_config_by_id(db_conn, config_id=ota_config_id)

    return get_searcher_from_ota_config(ota_config)


def get_searcher_from_ota_config(
    ota_config: OTAConfig,
) -> OTASearcher:
    if (
        not _searchers.get(ota_config.id)
        or _searchers[ota_config.id].ota_config != ota_config
    ):
        cls = _import_cls(OTA_SEARCHERS[ota_config.provider])
        _searchers[ota_config.id] = cls(ota_config, **ota_config.config)
        logger.info("Imported new searcher")
    return _searchers[ota_config.id]


_searchers: dict[int, OTASearcher] = {}


def ticket_issuer(ota_config: OTAConfig) -> TicketIssuer:
    if (
        not _ticket_issuers.get(ota_config.id)
        or _ticket_issuers[ota_config.id].ota_config != ota_config
    ):
        cls = _import_cls(OTA_TICKET_ISSUERS[ota_config.provider])
        _ticket_issuers[ota_config.id] = cls(ota_config)

    return _ticket_issuers[ota_config.id]


_ticket_issuers: dict[int, TicketIssuer] = {}


def _import_cls(name: str):
    module, class_name = name.rsplit(".", 1)
    mod = importlib.import_module(module)
    cls = getattr(mod, class_name)
    return cls


class InvalidConfigurationError(RuntimeError): ...
