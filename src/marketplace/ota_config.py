from marketplace import database
from marketplace.models import OTAConfig


async def upsert_ota_config(data: dict):
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            existing_record = await database.queries.get_ota_config_by_name(
                db_conn, name=data["name"]
            )
            data["id"] = existing_record["id"] if existing_record else None

            if data["id"]:
                record = await database.queries.update_ota_config(db_conn, **data)
            else:
                record = await database.queries.insert_ota_config(db_conn, **data)
            return OTAConfig(**record)
