from dataclasses import dataclass
from datetime import date
from http import HTTPStatus
from json import JSONDecodeError
from typing import List, Optional

import dacite
import httpx

from marketplace.otas.exception import (
    ItineraryNotFound,
    OTAConnectionError,
    OTATimeoutException,
    OTATooManyRequests,
    SeatUnavailable,
    TravelUnavailable,
)
from marketplace.otas.http import AsyncClient


@dataclass
class ServiceToken:
    token: str
    refresh_token: Optional[str]
    user_fingerprint: Optional[str]
    roles: list | None


@dataclass
class AuthResponse:
    service_token: ServiceToken


@dataclass
class Endereco:
    id: int
    cep: str
    logradouro: str
    complemento: Optional[str]
    bairro: str
    codigoCidade: int
    localidade: Optional[str]
    uf: str
    nomeCidade: str
    numero: Optional[str]

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            cep=data["cep"],
            logradouro=data["logradouro"],
            complemento=data["complemento"],
            bairro=data["bairro"],
            codigoCidade=data["codigoCidade"],
            localidade=data["localidade"],
            uf=data["uf"],
            nomeCidade=data["nomeCidade"],
            numero=data["numero"],
        )


@dataclass
class EmpresaDto:
    id: int
    nome: str
    email: str
    telefones: list[str]
    logomarca: str
    urlImagem: str
    cnpj: str
    nomeFantasia: str
    endereco: Endereco
    nomeDono: str
    ie: Optional[str]
    cnae: Optional[str]
    im: Optional[str]
    tar: Optional[str]
    telefoneDono: str
    nomeSocio: str
    telefoneSocio: str
    nomeGerente: str
    telefoneGerente: str
    opcoesTrechoAlternativo: bool
    valorKgEncomenda: str
    multaRemarcacaoCancelamentoDentroPrazo: str
    multaRemarcacaoCancelamentoForaPrazo: str
    habilitadoEncomenda: bool
    habilitadoSite: bool
    habilitadoBpe: bool
    nomeCertificado: Optional[str]
    senhaCertificado: Optional[str]
    bloqueada: bool | None
    mensagemAvisoBilhete: str
    grupo: str
    habilitadoBuscaReservaComBpe: bool
    dataIniciaoVigenciaBuscaReservaComBpe: Optional[str]
    dataCriacao: str
    sites: list[str]
    tempoVisualizacaoDados: Optional[str]
    habilitadaVendaSeguro: bool
    habilitadoAgencia: bool
    habilitadoValorAntt: bool
    impostoPraticar: str
    comissaoPraticar: str
    agenteTipoUm: str
    agenteTipoDois: str
    agenteTipoTres: str
    notaEmpresa: str
    habilitadoBpeSites: bool
    rodoviaria: bool
    turismo: bool
    senhaAntt: Optional[str]
    habilitadoEnvioWpp: bool
    integracoes: list
    numeroNotaCte: Optional[str]
    numeroNotaCteOs: Optional[str]
    habilitadoRegraBilhete: bool
    tipoControleEmissaoBPe: dict
    habilitadoRegraConexaoTrecho: bool
    nomeFantasiaNormalizado: str
    configuracoesBPe: list

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            nome=data["nome"],
            email=data["email"],
            telefones=data["telefones"],
            logomarca=data["logomarca"],
            urlImagem=data["urlImagem"],
            cnpj=data["cnpj"],
            nomeFantasia=data["nomeFantasia"],
            endereco=Endereco.from_dict(data["endereco"]),
            nomeDono=data["nomeDono"],
            ie=data["ie"],
            cnae=data["cnae"],
            im=data["im"],
            tar=data["tar"],
            telefoneDono=data["telefoneDono"],
            nomeSocio=data["nomeSocio"],
            telefoneSocio=data["telefoneSocio"],
            nomeGerente=data["nomeGerente"],
            telefoneGerente=data["telefoneGerente"],
            opcoesTrechoAlternativo=data["opcoesTrechoAlternativo"],
            valorKgEncomenda=data["valorKgEncomenda"],
            multaRemarcacaoCancelamentoDentroPrazo=data[
                "multaRemarcacaoCancelamentoDentroPrazo"
            ],
            multaRemarcacaoCancelamentoForaPrazo=data[
                "multaRemarcacaoCancelamentoForaPrazo"
            ],
            habilitadoEncomenda=data["habilitadoEncomenda"],
            habilitadoSite=data["habilitadoSite"],
            habilitadoBpe=data["habilitadoBpe"],
            nomeCertificado=data["nomeCertificado"],
            senhaCertificado=data["senhaCertificado"],
            bloqueada=data["bloqueada"],
            mensagemAvisoBilhete=data["mensagemAvisoBilhete"],
            grupo=data["grupo"],
            habilitadoBuscaReservaComBpe=data["habilitadoBuscaReservaComBpe"],
            dataIniciaoVigenciaBuscaReservaComBpe=data[
                "dataIniciaoVigenciaBuscaReservaComBpe"
            ],
            dataCriacao=data["dataCriacao"],
            sites=data["sites"],
            tempoVisualizacaoDados=data["tempoVisualizacaoDados"],
            habilitadaVendaSeguro=data["habilitadaVendaSeguro"],
            habilitadoAgencia=data["habilitadoAgencia"],
            habilitadoValorAntt=data["habilitadoValorAntt"],
            impostoPraticar=data["impostoPraticar"],
            comissaoPraticar=data["comissaoPraticar"],
            agenteTipoUm=data["agenteTipoUm"],
            agenteTipoDois=data["agenteTipoDois"],
            agenteTipoTres=data["agenteTipoTres"],
            notaEmpresa=data["notaEmpresa"],
            habilitadoBpeSites=data["habilitadoBpeSites"],
            rodoviaria=data["rodoviaria"],
            turismo=data["turismo"],
            senhaAntt=data["senhaAntt"],
            habilitadoEnvioWpp=data["habilitadoEnvioWpp"],
            integracoes=data["integracoes"],
            numeroNotaCte=data["numeroNotaCte"],
            numeroNotaCteOs=data["numeroNotaCteOs"],
            habilitadoRegraBilhete=data["habilitadoRegraBilhete"],
            tipoControleEmissaoBPe=data["tipoControleEmissaoBPe"],
            habilitadoRegraConexaoTrecho=data["habilitadoRegraConexaoTrecho"],
            nomeFantasiaNormalizado=data["nomeFantasiaNormalizado"],
            configuracoesBPe=data["configuracoesBPe"],
        )


@dataclass
class Cidade:
    id: int
    uf: str
    descricaoUf: str
    nome: str
    nomeComUf: str
    nomeComUfNormalizado: str
    codigoIbge: str
    codigoAntt: str
    imagem: Optional[str]
    urlImagem: Optional[str]

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            uf=data["uf"],
            descricaoUf=data["descricaoUf"],
            nome=data["nome"],
            nomeComUf=data["nomeComUf"],
            nomeComUfNormalizado=data["nomeComUfNormalizado"],
            codigoIbge=data["codigoIbge"],
            codigoAntt=data["codigoAntt"],
            imagem=data["imagem"],
            urlImagem=data.get("urlImagem"),
        )


@dataclass
class Rota:
    id: int
    empresaDto: EmpresaDto | None
    cidadeOrigem: Cidade
    cidadeDestino: Cidade
    trechosDto: list["Trecho"]
    conexoes: List["Conexao"]
    opcoesTrechoAlternativo: bool | None
    prefixo: str
    delimitacao: str | None
    descricao: str | None
    nomeFantasiaEmpresa: Optional[str]
    empresaId: Optional[int]

    def __repr__(self):
        return f"Rota(id={self.id}, descricao={self.descricao}, ...)"

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            empresaDto=data.get("empresaDto"),
            cidadeOrigem=Cidade.from_dict(data["cidadeOrigem"]),
            cidadeDestino=Cidade.from_dict(data["cidadeDestino"]),
            trechosDto=[Trecho.from_dict(trecho) for trecho in data["trechosDto"]],
            conexoes=data.get("conexoes"),
            opcoesTrechoAlternativo=data.get("opcoesTrechoAlternativo"),
            prefixo=data.get("prefixo"),
            delimitacao=data.get("delimitacao"),
            descricao=data.get("descricao"),
            nomeFantasiaEmpresa=data.get("nomeFantasiaEmpresa"),
            empresaId=data.get("empresaId"),
        )


@dataclass
class ListRoutesResponse:
    rotas: list[Rota]
    total: int


@dataclass
class Poltrona:
    id: int
    numero: int
    reservada: bool
    bloqueada: bool
    preco: str | None
    tipoPreco: Optional[str]
    dataSaida: Optional[str]
    andar: Optional[int]
    tipoCategoria: str | None = None

    @classmethod
    def from_dict(cls, data: dict) -> "Poltrona":
        return cls(
            id=data["id"],
            numero=data["numero"],
            reservada=data["reservada"],
            bloqueada=data["bloqueada"],
            preco=data["preco"],
            tipoPreco=data.get("tipoPreco"),
            dataSaida=data.get("dataSaida"),
            andar=data.get("andar"),
            tipoCategoria=data.get("tipoCategoria"),
        )


@dataclass
class Assento:
    id: int
    numero: int
    ordem: int
    tipoAssento: str
    poltrona: Poltrona | None = None

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            numero=data["numero"],
            ordem=data["ordem"],
            tipoAssento=data["tipoAssento"],
            poltrona=Poltrona.from_dict(data["poltrona"])
            if data.get("poltrona")
            else None,
        )


@dataclass
class Fileira:
    id: int
    numero: int
    exibeCorredor: bool
    assentos: List[Assento]

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            numero=data["numero"],
            exibeCorredor=data["exibeCorredor"],
            assentos=[Assento.from_dict(assento) for assento in data["assentos"]],
        )


@dataclass
class MapaVeiculo:
    id: int
    fileirasPrimeiroAndar: List[Fileira]
    fileirasSegundoAndar: List[Fileira]
    seDoisAndares: bool
    quantidadeAssentos: int
    modelo: str
    tipoVeiculo: str

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            fileirasPrimeiroAndar=[
                Fileira.from_dict(fileira) for fileira in data["fileirasPrimeiroAndar"]
            ],
            fileirasSegundoAndar=[
                Fileira.from_dict(fileira) for fileira in data["fileirasSegundoAndar"]
            ],
            seDoisAndares=data["seDoisAndares"],
            quantidadeAssentos=data["quantidadeAssentos"],
            modelo=data["modelo"],
            tipoVeiculo=data["tipoVeiculo"],
        )

    def seats_count_by_type(self):
        counter: dict[str, int] = {}
        for fileira in self.fileirasPrimeiroAndar + self.fileirasSegundoAndar:
            for assento in fileira.assentos:
                if assento.tipoAssento not in counter:
                    counter[assento.tipoAssento] = 1
                else:
                    counter[assento.tipoAssento] += 1
        return counter


@dataclass
class TipoVeiculo:
    chave: str
    valor: str

    @classmethod
    def from_dict(cls, data):
        return cls(chave=data["chave"], valor=data["valor"])


@dataclass
class Trecho:
    id: int
    idEmpresa: int
    cidadeDestino: Cidade
    cidadeOrigem: Optional[Cidade]
    cidadeOrigemAlternativa: Optional[Cidade]
    cidadeDestinoAlternativa: Optional[Cidade]
    rotaId: Optional[int]
    rotaDto: Rota
    ordem: int
    duracao: str
    pontoEmbarque: str
    plataformaEmbarque: Optional[str]
    taxaEmbarque: str
    pedagio: str
    quilometragem: str
    habilitadoVenderSemConexao: bool

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            idEmpresa=data["idEmpresa"],
            cidadeDestino=Cidade.from_dict(data["cidadeDestino"]),
            cidadeOrigem=Cidade.from_dict(data["cidadeOrigem"])
            if data.get("cidadeOrigem")
            else None,
            cidadeOrigemAlternativa=Cidade.from_dict(data["cidadeOrigemAlternativa"])
            if data.get("cidadeOrigemAlternativa")
            else None,
            cidadeDestinoAlternativa=Cidade.from_dict(data["cidadeDestinoAlternativa"])
            if data.get("cidadeDestinoAlternativa")
            else None,
            rotaId=data.get("rotaId"),
            rotaDto=Rota.from_dict(data["rotaDto"]),
            ordem=data["ordem"],
            duracao=data["duracao"],
            pontoEmbarque=data["pontoEmbarque"],
            plataformaEmbarque=data.get("plataformaEmbarque"),
            taxaEmbarque=data["taxaEmbarque"],
            pedagio=data["pedagio"],
            quilometragem=data["quilometragem"],
            habilitadoVenderSemConexao=data["habilitadoVenderSemConexao"],
        )

    @property
    def duracao_segundos(self):
        horas, minutos = self.duracao.split(":")
        return int(horas) * 3600 + int(minutos) * 60


@dataclass
class Conexao:
    id: int
    cidadeOrigem: Cidade
    cidadeDestino: Cidade
    prefixo: str
    rotaId: int
    trechoId: int

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            cidadeOrigem=Cidade.from_dict(data["cidadeOrigem"]),
            cidadeDestino=Cidade.from_dict(data["cidadeDestino"]),
            prefixo=data["prefixo"],
            rotaId=data["rotaId"],
            trechoId=data["trechoId"],
        )


@dataclass
class TipoPreco:
    tipoPreco: str
    descricaoTipoPreco: str

    @classmethod
    def from_dict(cls, data):
        return cls(
            tipoPreco=data["tipoPreco"],
            descricaoTipoPreco=data["descricaoTipoPreco"],
        )


@dataclass
class Veiculo:
    id: int
    descricao: str
    empresaDto: Optional[str]
    mapaVeiculoDto: MapaVeiculo
    idRastreamento: Optional[str]
    seDoisAndares: bool
    imagens: Optional[str]
    placa: Optional[str]
    modelo: Optional[str]
    tipoVeiculo: TipoVeiculo
    quilometragem: Optional[str]
    chassi: Optional[str]
    renavam: Optional[str]
    anoFabricacao: Optional[int]
    numeroEixo: Optional[int]
    dataVencimentoCsv: Optional[str]
    exibirDetalhe: bool
    tipoServico: List[str]

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            descricao=data["descricao"],
            empresaDto=data.get("empresaDto"),
            mapaVeiculoDto=MapaVeiculo.from_dict(data["mapaVeiculoDto"]),
            idRastreamento=data.get("idRastreamento"),
            seDoisAndares=data["seDoisAndares"],
            imagens=data.get("imagens"),
            placa=data.get("placa"),
            modelo=data.get("modelo"),
            tipoVeiculo=TipoVeiculo.from_dict(data["tipoVeiculo"]),
            quilometragem=data.get("quilometragem"),
            chassi=data.get("chassi"),
            renavam=data.get("renavam"),
            anoFabricacao=data.get("anoFabricacao"),
            numeroEixo=data.get("numeroEixo"),
            dataVencimentoCsv=data.get("dataVencimentoCsv"),
            exibirDetalhe=data["exibirDetalhe"],
            tipoServico=data["tipoServico"],
        )


@dataclass
class Itinerario:
    id: int
    dataPartida: str
    horaSaida: str
    dataHoraPartida: str
    dataHoraChegada: str
    veiculoId: Optional[int]
    veiculoDto: Veiculo
    rotaId: Optional[int]
    rotaDto: Rota
    motoristas: List[str]
    assentosDisponiveis: int
    idEmpresa: int
    nomeEmpresa: str
    nomeFantasiaEmpresa: str
    poltronas: List[str]
    descricaoTipoVeiculo: str
    quantidadeAssentos: Optional[int]
    preco: Optional[float]
    quilometragem: str
    tipoAjusteItinerario: Optional[str]
    valorAjuste: Optional[float]
    tipoPreco: TipoPreco
    andar: int
    trechosBloqueados: List[str]
    cidadesBloqueados: List[str]
    trechosBloqueadosDesconto: List[str]
    habilitadoSite: bool
    assentosVendidos: Optional[int]
    sePodeEmbarcar: bool
    tipoConfiguracaoPreco: Optional[str]
    horaPartidaRegra: Optional[str]
    quantidadePoltronasRegra: Optional[int]
    ativo: bool
    prestador: Optional[str]
    complemento: Optional[str]
    tipoCategoria: str
    valorTipoPrecoPadrao: Optional[float]
    tipoPrecoPadrao: Optional[str]
    passouTempoParaPartida: bool

    @classmethod
    def from_dict(cls, data):
        return cls(
            id=data["id"],
            dataPartida=data["dataPartida"],
            horaSaida=data["horaSaida"],
            dataHoraPartida=data["dataHoraPartida"],
            dataHoraChegada=data["dataHoraChegada"],
            veiculoId=data.get("veiculoId"),
            veiculoDto=Veiculo.from_dict(data["veiculoDto"]),
            rotaId=data.get("rotaId"),
            rotaDto=Rota.from_dict(data["rotaDto"]),
            motoristas=data["motoristas"],
            assentosDisponiveis=data["assentosDisponiveis"],
            idEmpresa=data["idEmpresa"],
            nomeEmpresa=data["nomeEmpresa"],
            nomeFantasiaEmpresa=data["nomeFantasiaEmpresa"],
            poltronas=data["poltronas"],
            descricaoTipoVeiculo=data["descricaoTipoVeiculo"],
            quantidadeAssentos=data.get("quantidadeAssentos"),
            preco=data.get("preco"),
            quilometragem=data["quilometragem"],
            tipoAjusteItinerario=data.get("tipoAjusteItinerario"),
            valorAjuste=data.get("valorAjuste"),
            tipoPreco=TipoPreco.from_dict(data["tipoPreco"]),
            andar=data["andar"],
            trechosBloqueados=data["trechosBloqueados"],
            cidadesBloqueados=data["cidadesBloqueados"],
            trechosBloqueadosDesconto=data["trechosBloqueadosDesconto"],
            habilitadoSite=data["habilitadoSite"],
            assentosVendidos=data.get("assentosVendidos"),
            sePodeEmbarcar=data["sePodeEmbarcar"],
            tipoConfiguracaoPreco=data.get("tipoConfiguracaoPreco"),
            horaPartidaRegra=data.get("horaPartidaRegra"),
            quantidadePoltronasRegra=data.get("quantidadePoltronasRegra"),
            ativo=data["ativo"],
            prestador=data.get("prestador"),
            complemento=data.get("complemento"),
            tipoCategoria=data["tipoCategoria"],
            valorTipoPrecoPadrao=data.get("valorTipoPrecoPadrao"),
            tipoPrecoPadrao=data.get("tipoPrecoPadrao"),
            passouTempoParaPartida=data["passouTempoParaPartida"],
        )


@dataclass
class ItinerariosResponse:
    itinerarios: List[Itinerario]
    total: int

    @classmethod
    def from_dict(cls, data):
        return cls(
            itinerarios=[
                Itinerario.from_dict(itinerario) for itinerario in data["itinerarios"]
            ],
            total=data["total"],
        )


@dataclass
class SearchResponse:
    passagensIda: list["Passagem"]
    passagensRetorno: list["Passagem"]
    datasIdaProximas: Optional[list[str]]
    datasRetornoProximas: Optional[list[str]]
    origem: Optional[str]
    destino: Optional[str]


@dataclass
class ImagemVeiculo:
    id: int
    nome: str
    imagem: str
    principal: bool
    urlImagem: str
    tipoVeiculo: str


@dataclass
class Passagem:
    idItinerario: int
    dataPartida: str
    horaPartida: str
    dataHoraPartida: str
    dataHoraChegada: str
    dataChegada: str
    horaChegada: str
    tipoVeiculo: str
    descricaoTipoVeiculo: str
    assentosDisponiveis: int
    idEmpresa: int
    nomeEmpresa: str
    preco: str
    precoMeia: str
    embarque: str | None
    andar: int
    taxaEmbarque: str
    pedagio: str
    tarifaSeguro: str
    precoANTT: str
    precoANTTMeia: str
    descRota: str
    descontoMaximoUsuarioRota: Optional[str]
    veiculo: str
    veiculoId: int
    duracao: str
    qtdDias: int
    trechoOrigemId: int
    trechoDestinoId: int
    idRota: int
    seAplicaAjuste: bool
    nomePrestador: Optional[str]
    seTemConexao: bool
    nomesCidadesComConexao: List[str]
    cidadesComConexao: List["CidadesConexao"]
    empresaHabilitadoValorAntt: bool
    usuarioEhHabilitadoVenderValorANTT: bool
    tipoCategoriaItinerario: str
    latitudeOrigem: Optional[float]
    longitudeOrigem: Optional[float]
    latitudeDestino: Optional[float]
    longitudeDestino: Optional[float]
    nota: float
    pontoEmbarque: str | None
    pontoDesembarque: str | None
    integracao: bool
    fareClass: Optional[str]
    idFareClass: Optional[int]
    idEstacaoOrigem: Optional[int]
    idEstacaoDestino: Optional[int]
    codeEmpresa: Optional[str]
    api: str
    acrescimoMaximoPassagem: str
    valorAcrescimoMaximoPassagem: str
    electronicTicketAvailable: bool
    imagemVeiculo: Optional[ImagemVeiculo] | None
    tipoImagemVeiculo: str
    vendaGratuidade: "VendaGratuidade"
    percentualDescontoPrice: Optional[float]
    valorSemDesconto: str
    valorSemDescontoAntt: str
    urlImagemEmpresa: Optional[str]


@dataclass
class CidadesConexao:
    idCidadeOrigem: int
    idCidadeDestino: int
    nomeCidade: str


@dataclass
class VendaGratuidade:
    totalIdJovem100: int
    totalIdoso100: int
    totalIdJovem50: int
    totalIdoso50: int
    totalDeficiente: int
    vendeGratuidade: bool


@dataclass
class TipoPrecoPadrao:
    tipoPreco: str
    descricaoTipoPreco: str

    @classmethod
    def from_dict(cls, data: dict) -> "TipoPrecoPadrao":
        return cls(
            tipoPreco=data["tipoPreco"], descricaoTipoPreco=data["descricaoTipoPreco"]
        )


@dataclass
class TipoConfiguracaoPreco:
    descricao: str
    valor: str

    @classmethod
    def from_dict(cls, data: dict) -> "TipoConfiguracaoPreco":
        return cls(descricao=data["descricao"], valor=data["valor"])


@dataclass
class TipoAjusteItinerario:
    descricao: str
    nameEnum: str

    @classmethod
    def from_dict(cls, data: dict) -> "TipoAjusteItinerario":
        return cls(descricao=data["descricao"], nameEnum=data["nameEnum"])


@dataclass
class MapaVeiculoDto:
    id: int
    fileirasPrimeiroAndar: List["Fileira"]
    fileirasSegundoAndar: List["Fileira"]
    seDoisAndares: bool
    quantidadeAssentos: int
    modelo: str
    tipoVeiculo: str

    @classmethod
    def from_dict(cls, data: dict) -> "MapaVeiculoDto":
        return cls(
            id=data["id"],
            fileirasPrimeiroAndar=[
                Fileira.from_dict(f) for f in data["fileirasPrimeiroAndar"]
            ],
            fileirasSegundoAndar=[
                Fileira.from_dict(f) for f in data["fileirasSegundoAndar"]
            ],
            seDoisAndares=data["seDoisAndares"],
            quantidadeAssentos=data["quantidadeAssentos"],
            modelo=data["modelo"],
            tipoVeiculo=data["tipoVeiculo"],
        )


@dataclass
class VeiculoDto:
    id: int
    descricao: str
    empresaDto: Optional[str]
    mapaVeiculoDto: MapaVeiculoDto
    idRastreamento: Optional[str]
    seDoisAndares: bool
    imagens: Optional[str]
    placa: str
    modelo: str
    tipoVeiculo: "TipoVeiculo"
    quilometragem: Optional[str]
    chassi: Optional[str]
    renavam: str
    anoFabricacao: str
    numeroEixo: int
    dataVencimentoCsv: str
    exibirDetalhe: bool
    tipoServico: List[str]

    @classmethod
    def from_dict(cls, data: dict) -> "VeiculoDto":
        return cls(
            id=data["id"],
            descricao=data["descricao"],
            empresaDto=data.get("empresaDto"),
            mapaVeiculoDto=MapaVeiculoDto.from_dict(data["mapaVeiculoDto"]),
            idRastreamento=data.get("idRastreamento"),
            seDoisAndares=data["seDoisAndares"],
            imagens=data.get("imagens"),
            placa=data["placa"],
            modelo=data["modelo"],
            tipoVeiculo=TipoVeiculo.from_dict(data["tipoVeiculo"]),
            quilometragem=data.get("quilometragem"),
            chassi=data.get("chassi"),
            renavam=data["renavam"],
            anoFabricacao=data["anoFabricacao"],
            numeroEixo=data["numeroEixo"],
            dataVencimentoCsv=data["dataVencimentoCsv"],
            exibirDetalhe=data["exibirDetalhe"],
            tipoServico=data["tipoServico"],
        )


@dataclass
class ItinerarioResponse:
    id: int
    dataPartida: str
    horaSaida: str
    dataHoraPartida: str
    dataHoraChegada: str
    veiculoId: Optional[int]
    veiculoDto: VeiculoDto
    rotaId: Optional[int]
    rotaDto: Rota
    motoristas: List[str]
    usuariosEmpresaBloqueado: List[str]
    assentosDisponiveis: int
    idEmpresa: int
    nomeEmpresa: str
    nomeFantasiaEmpresa: str
    poltronas: List[Poltrona]
    descricaoTipoVeiculo: Optional[str]
    quantidadeAssentos: Optional[int]
    preco: Optional[str]
    quilometragem: str
    tipoAjusteItinerario: TipoAjusteItinerario
    valorAjuste: str
    tipoPreco: Optional[str]
    andar: int
    trechosBloqueados: List[str]
    cidadesBloqueados: List[str]
    trechosBloqueadosDesconto: List[str]
    habilitadoSite: bool
    assentosVendidos: Optional[str]
    sePodeEmbarcar: bool
    tipoConfiguracaoPreco: TipoConfiguracaoPreco
    horaPartidaRegra: Optional[str]
    quantidadePoltronasRegra: Optional[int]
    ativo: bool
    prestador: Optional[str]
    complemento: Optional[str]
    tipoCategoria: str
    valorTipoPrecoPadrao: str
    tipoPrecoPadrao: TipoPrecoPadrao
    passouTempoParaPartida: bool
    pricePorUsuario: List[str]

    @classmethod
    def from_dict(cls, data: dict) -> "ItinerarioResponse":
        return cls(
            id=data["id"],
            dataPartida=data["dataPartida"],
            horaSaida=data["horaSaida"],
            dataHoraPartida=data["dataHoraPartida"],
            dataHoraChegada=data["dataHoraChegada"],
            veiculoId=data.get("veiculoId"),
            veiculoDto=VeiculoDto.from_dict(data["veiculoDto"]),
            rotaId=data.get("rotaId"),
            rotaDto=Rota.from_dict(data["rotaDto"]),
            motoristas=data.get("motoristas", []),
            usuariosEmpresaBloqueado=data.get("usuariosEmpresaBloqueado", []),
            assentosDisponiveis=data["assentosDisponiveis"],
            idEmpresa=data["idEmpresa"],
            nomeEmpresa=data["nomeEmpresa"],
            nomeFantasiaEmpresa=data["nomeFantasiaEmpresa"],
            poltronas=[Poltrona.from_dict(p) for p in data.get("poltronas", [])],
            descricaoTipoVeiculo=data.get("descricaoTipoVeiculo"),
            quantidadeAssentos=data.get("quantidadeAssentos"),
            preco=data.get("preco"),
            quilometragem=data["quilometragem"],
            tipoAjusteItinerario=TipoAjusteItinerario.from_dict(
                data["tipoAjusteItinerario"]
            ),
            valorAjuste=data["valorAjuste"],
            tipoPreco=data.get("tipoPreco"),
            andar=data["andar"],
            trechosBloqueados=data.get("trechosBloqueados", []),
            cidadesBloqueados=data.get("cidadesBloqueados", []),
            trechosBloqueadosDesconto=data.get("trechosBloqueadosDesconto", []),
            habilitadoSite=data["habilitadoSite"],
            assentosVendidos=data.get("assentosVendidos"),
            sePodeEmbarcar=data["sePodeEmbarcar"],
            tipoConfiguracaoPreco=TipoConfiguracaoPreco.from_dict(
                data["tipoConfiguracaoPreco"]
            ),
            horaPartidaRegra=data.get("horaPartidaRegra"),
            quantidadePoltronasRegra=data.get("quantidadePoltronasRegra"),
            ativo=data["ativo"],
            prestador=data.get("prestador"),
            complemento=data.get("complemento"),
            tipoCategoria=data["tipoCategoria"],
            valorTipoPrecoPadrao=data["valorTipoPrecoPadrao"],
            tipoPrecoPadrao=TipoPrecoPadrao.from_dict(data["tipoPrecoPadrao"]),
            passouTempoParaPartida=data["passouTempoParaPartida"],
            pricePorUsuario=data.get("pricePorUsuario", []),
        )


@dataclass
class BloqueioResponse:
    id: int
    poltronaId: int
    uuid: str
    preco: str
    precoANTT: str
    precoMeia: str
    precoANTTMeia: str
    taxaEmbarque: str
    pedagio: str
    tarifaSeguro: str
    garantiaPrecoId: int
    expiresAt: Optional[str]

    @classmethod
    def from_dict(cls, data: dict) -> "BloqueioResponse":
        return cls(**data)


async def handle_response(response: httpx.Response):
    if response.status_code in (
        HTTPStatus.BAD_GATEWAY,
        HTTPStatus.INTERNAL_SERVER_ERROR,
    ):
        raise OTAConnectionError()
    if response.status_code == HTTPStatus.GATEWAY_TIMEOUT:
        raise OTATimeoutException()
    if response.status_code in (
        HTTPStatus.SERVICE_UNAVAILABLE,
        HTTPStatus.TOO_MANY_REQUESTS,
    ):
        raise OTATooManyRequests()


class VexadoClient:
    def __init__(self, base_url, timeout: int = 20, log_prefix: Optional[str] = None):
        log_prefix = (
            f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__
        )
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            log_prefix=log_prefix,
            event_hooks={
                "response": [handle_response],
            },
        )

    async def auth_signin(self, username: str, password: str) -> AuthResponse:
        response = await self._client.post(
            "/auth/signin",
            json={
                "username": username,
                "password": password,
            },
        )
        response.raise_for_status()

        payload = response.json()
        return AuthResponse(
            service_token=ServiceToken(
                token=payload["tokenServico"]["tokenJwt"],
                refresh_token=payload["tokenServico"]["refreshToken"],
                user_fingerprint=payload["tokenServico"]["userFingerPrint"],
                roles=payload["tokenServico"]["roles"],
            )
        )

    async def search(
        self,
        service_token: ServiceToken,
        origem_id: int,
        destino_id: int,
        data_ida: date,
    ):
        response = await self._client.get(
            f"/passagem/origem/{origem_id}/destino/{destino_id}/dataIda/{data_ida.isoformat()}",
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
        )
        response.raise_for_status()

        payload = response.json()
        return dacite.from_dict(SearchResponse, payload)

    async def list_routes_by_company(
        self,
        service_token: ServiceToken,
        company_id: int,
        size: int = 500,
        page: int = 1,
    ) -> ListRoutesResponse:
        response = await self._client.get(
            f"/rota/empresa/{company_id}/rotas",
            params={
                "size": size,
                "from": page,
            },
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
        )
        response.raise_for_status()

        payload = response.json()
        return ListRoutesResponse(
            rotas=[
                Rota(
                    id=rota["id"],
                    empresaDto=EmpresaDto.from_dict(rota["empresaDto"]),
                    cidadeOrigem=Cidade.from_dict(rota["cidadeOrigem"]),
                    cidadeDestino=Cidade.from_dict(rota["cidadeDestino"]),
                    trechosDto=[
                        Trecho.from_dict(trecho) for trecho in rota["trechosDto"]
                    ],
                    conexoes=[Conexao.from_dict(trecho) for trecho in rota["conexoes"]],
                    opcoesTrechoAlternativo=rota["opcoesTrechoAlternativo"],
                    prefixo=rota["prefixo"],
                    delimitacao=rota["delimitacao"],
                    descricao=rota["descricao"],
                    nomeFantasiaEmpresa=rota["nomeFantasiaEmpresa"],
                    empresaId=rota["empresaId"],
                )
                for rota in payload["rotas"]
            ],
            total=payload["total"],
        )

    async def list_travels_by_route(
        self,
        service_token: ServiceToken,
        company_id: int,
        route_id: int,
        start_date: date,
        end_date: date,
        size=1_000,
    ):
        response = await self._client.get(
            f"/itinerario/empresa/{company_id}/rota/{route_id}/itinerarios",
            params={
                "dataPartidaInicio": start_date.strftime("%d/%m/%Y"),
                "dataPartidaFim": end_date.strftime("%d/%m/%Y"),
                "size": size,
            },
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
        )
        response.raise_for_status()

        payload = response.json()
        return ItinerariosResponse.from_dict(payload)

    async def list_cities_by_company(
        self, service_token: ServiceToken, company_id: int
    ) -> list[Cidade]:
        response = await self._client.get(
            f"/public/cidades/empresa/{company_id}/buscar-cidades",
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
        )
        response.raise_for_status()

        return [Cidade.from_dict(c) for c in response.json()]

    async def itinerary(
        self, service_token: ServiceToken, company_id: int, itinerario_id: int
    ) -> ItinerarioResponse:
        response = await self._client.get(
            f"/itinerario/{itinerario_id}/empresa/{company_id}",
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
        )
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as error:
            if error.response.status_code == HTTPStatus.PRECONDITION_FAILED:
                txt = error.response.text
                if "itinerarioNaoEncontrado" in txt or "Itinerário inexistente" in txt:
                    raise ItineraryNotFound from error
            raise error

        return ItinerarioResponse.from_dict(response.json())

    async def seating_map(
        self,
        service_token: ServiceToken,
        itinerary_id: int,
        origin_id: int,
        destination_id: int,
    ) -> list[Fileira]:
        response = await self._client.get(
            f"/mapas-veiculos/itinerario/{itinerary_id}/origem/{origin_id}/destino/{destination_id}",
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
        )

        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as error:
            if error.response.status_code == HTTPStatus.PRECONDITION_FAILED:
                txt = error.response.text
                if "itinerarioNaoEncontrado" in txt:
                    raise TravelUnavailable from error
            raise error

        return [Fileira.from_dict(f) for f in response.json()]

    async def bloquear_poltrona(
        self,
        service_token: ServiceToken,
        poltrona_id: int,
        itinerario_id: int,
        trecho_origem_id: int,
        trecho_destino_id: int,
    ) -> BloqueioResponse:
        response = await self._client.post(
            "/bloqueio-poltrona-temporario",
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
            json={
                "poltronaId": poltrona_id,
                "trechoOrigemId": trecho_origem_id,
                "trechoDestinoId": trecho_destino_id,
                "itinerarioId": itinerario_id,
            },
        )
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as error:
            if error.response.status_code == HTTPStatus.PRECONDITION_FAILED:
                response = error.response.text
                if "Poltrona já está reservada para o trecho informado" in response:
                    raise SeatUnavailable from error
                if (
                    "Não foi possível reservar a poltrona, porque já foi bloqueado"
                    in response
                ):
                    raise SeatUnavailable from error
                if "bloqueado temporariamente para venda" in response:
                    raise TravelUnavailable from error
            raise error

        return BloqueioResponse.from_dict(response.json())

    async def liberar_poltrona(
        self,
        service_token: ServiceToken,
        id_bloqueio: int,
    ):
        response = await self._client.post(
            "/bloqueio-poltrona-temporario/liberar",
            headers={
                "Authorization": f"Bearer {service_token.token}",
                "site": "adminVexado",
            },
            json=[id_bloqueio],
        )
        response.raise_for_status()
        try:
            return response.json()
        except JSONDecodeError:
            return None
