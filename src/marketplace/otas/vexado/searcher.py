import asyncio
import time
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from typing import cast, override

import jwt
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    SeatMap,
    Stopover,
    Travel,
)
from marketplace.otas.exception import (
    IncompleteExtra,
    OTACircuitOpenError,
    OTAConnectionError,
    OTATimeoutException,
)
from marketplace.otas.vexado.client import (
    Fileira,
    ItinerarioResponse,
    Passagem,
    SearchResponse,
    ServiceToken,
    VexadoClient,
)
from marketplace.searcher import OTASearcher


@dataclass
class VexadoTravelExtra:
    itinerario_id: int
    origem_id: int
    destino_id: int
    tipoVeiculo: str
    rota_id: int | None = None
    empresa_id: int | None = None
    trechoOrigemId: int | None = None
    trechoDestinoId: int | None = None
    dataHoraPartida: str | None = None

    @classmethod
    def for_seat_map(cls, extra: dict) -> "VexadoTravelExtra":
        try:
            return VexadoTravelExtra(
                itinerario_id=extra["itinerario_id"],
                origem_id=extra["origem_id"],
                destino_id=extra["destino_id"],
                tipoVeiculo=extra["tipoVeiculo"],
            )
        except KeyError as error:
            raise IncompleteExtra(f"Missing extra field {error}") from error


class VexadoSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        username: str,
        password: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._client = VexadoClient(base_url=base_url, log_prefix=self.ota_config.name)
        self._ota_config = ota_config
        self._token = None

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: _token_ttl(
            cast(ServiceToken, result)
        ),
        cache_key=lambda self: f"vexado:{self.ota_config.id}:token",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _get_token(self) -> ServiceToken:
        auth_response = await self._client.auth_signin(
            username=cast(str, self._ota_config.config["username"]),
            password=cast(str, self._ota_config.config["password"]),
        )
        return auth_response.service_token

    async def _call(self, func, *args, **kwargs):
        try:
            return await self._circuit_breaker.call(
                self.retry_policy(func), *args, **kwargs
            )
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        token = await self._get_token()
        search_response, last_synced, _ = await self._search_travels(
            service_token=token,
            origem_id=origin.extra["id"],
            destino_id=destination.extra["id"],
            departure_date=departure_date,
        )
        if search_response is None:
            return []

        search_response = cast(SearchResponse, search_response)

        travels = []
        for passagem in search_response.passagensIda:
            seat_type = self._get_ota_seat_type_by_name(passagem.tipoVeiculo)
            company = self._get_ota_company(
                passagem.idEmpresa, name=passagem.nomeEmpresa
            )

            if seat_type is None or company is None:
                continue

            price = float(passagem.preco) + float(passagem.taxaEmbarque or "0")
            travel = Travel(
                ota="vexado",
                ota_config_id=self.ota_config.id,
                code=f"{passagem.idEmpresa}:{passagem.idRota}:{passagem.idItinerario}",
                group_class_code=f"{passagem.idItinerario}",
                service_code=str(passagem.idItinerario),
                company=company,
                itinerary=[],
                origin=cast(Place, origin.place),
                destination=cast(Place, destination.place),
                departure_at=datetime.fromisoformat(passagem.dataHoraPartida),
                arrival_at=datetime.fromisoformat(passagem.dataHoraChegada),
                seat_type=seat_type,
                price=price,
                available_seats=passagem.assentosDisponiveis,
                total_seats=passagem.assentosDisponiveis,
                last_synced=last_synced,
                extra=asdict(
                    VexadoTravelExtra(
                        itinerario_id=passagem.idItinerario,
                        rota_id=passagem.idRota,
                        empresa_id=passagem.idEmpresa,
                        trechoOrigemId=passagem.trechoOrigemId,
                        trechoDestinoId=passagem.trechoDestinoId,
                        origem_id=origin.extra["id"],
                        destino_id=destination.extra["id"],
                        dataHoraPartida=passagem.dataHoraPartida,
                        tipoVeiculo=passagem.tipoVeiculo,
                    )
                ),
            )
            if passagem.cidadesComConexao:
                # TODO The OTA needs to return the stopover locations correctly
                travel.stopovers = [
                    Stopover(
                        origin_ota_place_id=origin.extra["id"],
                        destination_ota_place_id=passagem.cidadesComConexao[
                            0
                        ].idCidadeOrigem,
                    )
                ] + [
                    Stopover(
                        origin_ota_place_id=cidade.idCidadeOrigem,
                        destination_ota_place_id=cidade.idCidadeDestino,
                    )
                    for cidade in passagem.cidadesComConexao
                ]
                travel.single_ticket_connection = True

            travels.append(travel)

        return travels

    @override
    async def list_places(self) -> list[OTAPlace]:
        token = await self._get_token()

        if self.ota_config.config.get("company_id"):
            external_ids = [int(self.ota_config.config["company_id"])]
        else:
            companies_with_cnpj = [
                c for c in (self.ota_config.companies or []) if c.cnpj
            ]
            external_ids = [company.external_id for company in companies_with_cnpj]

        if not external_ids:
            return []

        tasks = []
        for external_id in external_ids:
            tasks.append(
                self._call(
                    self._client.list_cities_by_company,
                    service_token=token,
                    company_id=external_id,
                )
            )

        responses = await asyncio.gather(*tasks)
        cities = [city for cities_response in responses for city in cities_response]
        places = set()
        for city in cities:
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=f"{city.nome} - {city.uf}",
                    extra=city,
                )
            )

        return list(places)

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        travel_extra = VexadoTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        token = await self._get_token()
        ota_itinerary: ItinerarioResponse = await self._itinerary(
            token, travel_extra.empresa_id, travel_extra.itinerario_id
        )
        post_origin_checkpoint = False
        assert travel_extra.dataHoraPartida
        data_hora_partida = datetime.fromisoformat(travel_extra.dataHoraPartida)
        for trecho in ota_itinerary.rotaDto.trechosDto:
            post_origin_checkpoint = (
                post_origin_checkpoint
                or trecho.cidadeDestino.id == travel_extra.origem_id
            )
            if not post_origin_checkpoint:
                continue

            origin_checkpoint = trecho.cidadeDestino.id == travel_extra.origem_id
            data_hora_partida = (
                data_hora_partida
                if origin_checkpoint
                else data_hora_partida + timedelta(seconds=trecho.duracao_segundos)
            )
            itinerary.append(
                Checkpoint(
                    name=trecho.cidadeDestino.nomeComUf,
                    departure_at=data_hora_partida,
                    distance_km=0
                    if origin_checkpoint
                    else float(trecho.quilometragem.replace(",", ".")),
                )
            )
            if trecho.cidadeDestino.id == travel_extra.destino_id:
                break
        return itinerary

    async def available_seats(
        self, travel: InputTravel, search_for_price: bool = True
    ) -> SeatMap:
        travel_extra = VexadoTravelExtra.for_seat_map(travel.extra)
        token = await self._get_token()
        seats = []
        seating_map = await self._seating_map(
            service_token=token,
            itinerary_id=travel_extra.itinerario_id,
            origin=travel_extra.origem_id,
            destination=travel_extra.destino_id,
        )
        seating_map = cast(list[Fileira], seating_map)
        default_seat_type = self._get_ota_seat_type_by_name(travel_extra.tipoVeiculo)
        prices: list[float] = []
        for row in seating_map:
            for seat in row.assentos:
                if not seat.poltrona:
                    continue
                seat_type = default_seat_type
                if seat.poltrona.tipoCategoria:
                    seat_type = self._get_ota_seat_type_by_name(
                        seat.poltrona.tipoCategoria
                    )
                if not seat_type or not seat_type.seat_type:
                    continue
                if seat.poltrona.preco:
                    prices.append(float(seat.poltrona.preco))
                seats.append(
                    Seat(
                        number=str(seat.poltrona.numero),
                        floor=seat.poltrona.andar + 1
                        if seat.poltrona and seat.poltrona.andar
                        else 1,
                        row=seat.ordem + 1,
                        column=row.numero + 1 if row.numero > 2 else row.numero,
                        available=not seat.poltrona.bloqueada
                        and not seat.poltrona.reservada,
                        category=seat.poltrona.tipoCategoria
                        or travel_extra.tipoVeiculo,
                        seat_type=seat_type.seat_type,
                        price=float(seat.poltrona.preco)
                        if seat.poltrona.preco
                        else None,
                        benefits=[Benefit.NORMAL],
                        extra={"id": seat.poltrona.id},
                    )
                )

        return SeatMap(seats=seats, base_price=min(prices) if prices else None)

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        service_token,
        origem_id,
        destino_id,
        departure_date: f"search:{self.ota_config.id}:{departure_date}:{origem_id}:{destino_id}:v2",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self,
        service_token: ServiceToken,
        origem_id: int,
        destino_id: int,
        departure_date: date,
    ) -> tuple[SearchResponse | None, datetime | None, int]:
        result = await self._call(
            self._client.search,
            service_token,
            origem_id=origem_id,
            destino_id=destino_id,
            data_ida=departure_date,
        )
        last_synced = datetime.now(timezone.utc)
        cache_ttl = await self._dynamic_search_ttl(
            result.passagensIda, origem_id, destino_id, departure_date
        )
        return result, last_synced, cache_ttl

    def _has_few_seats_travel(self, search_response: list[Passagem]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for servico in search_response:
            if servico.assentosDisponiveis < threshold:
                return True

        return False

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self,
        service_token,
        company_id,
        itinerario_id: f"itinerary:{self.ota_config.id}:{company_id}:{itinerario_id}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _itinerary(
        self, service_token: ServiceToken, company_id: int, itinerario_id: int
    ) -> ItinerarioResponse:
        return await self._call(
            self._client.itinerary,
            service_token=service_token,
            company_id=company_id,
            itinerario_id=itinerario_id,
        )

    @memoize(
        cache,
        ttl=45,
        cache_key=lambda self,
        service_token,
        itinerary_id,
        origin,
        destination: f"map:{self.ota_config.id}:{itinerary_id}:{origin}:{destination}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(
        self,
        service_token: ServiceToken,
        itinerary_id: int,
        origin: int,
        destination: int,
    ) -> list[Fileira]:
        return await self._call(
            self._client.seating_map,
            service_token=service_token,
            itinerary_id=itinerary_id,
            origin_id=origin,
            destination_id=destination,
        )

    async def block_seat(
        self, travel: InputTravel, seat: Seat, benefit: Benefit = Benefit.NORMAL
    ) -> BlockedSeat:
        travel_extra = VexadoTravelExtra(**travel.extra)
        token = await self._get_token()
        bloqueio = await self._call(
            self._client.bloquear_poltrona,
            service_token=token,
            poltrona_id=seat.extra["id"],
            itinerario_id=travel_extra.itinerario_id,
            trecho_origem_id=travel_extra.trechoOrigemId,
            trecho_destino_id=travel_extra.trechoDestinoId,
        )
        best_before = datetime.now(timezone.utc) + timedelta(seconds=_token_ttl(token))
        return BlockedSeat(
            **asdict(seat),
            best_before=best_before,
            block_key=bloqueio.id,
            extra_rodoviaria={
                "uuid": bloqueio.uuid,
                "poltronaId": bloqueio.poltronaId,
            },
        )

    async def unblock_seat(
        self, travel: InputTravel, blocked_seat: BlockedSeat
    ) -> bool:
        token = await self._get_token()
        await self._call(
            self._client.liberar_poltrona,
            service_token=token,
            id_bloqueio=blocked_seat.block_key,
        )
        return True


def _token_ttl(token: ServiceToken) -> int:
    payload = jwt.decode(token.token, options={"verify_signature": False})
    return payload["exp"] - int(time.time()) - 120
