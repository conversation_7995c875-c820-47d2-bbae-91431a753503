import asyncio
from dataclasses import asdict
from datetime import datetime, timedelta
from typing import cast

from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)

from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Benefit,
    DisabledBenefit,
    ElderlyBenefit,
    OTAConfig,
    YoungBenefit,
)
from marketplace.otas.exception import (
    OTACircuitOpenError,
    OTAConnectionError,
    OTATimeoutException,
)
from marketplace.otas.guichepass.client import (
    ConfirmReserveRequest,
    GuichepassClient,
    TicketInfo,
)
from marketplace.otas.retry import NullRetry
from marketplace.tickets import IssuedTicket, RefreshedTicket, Ticket, TicketIssuer

BENEFIT_CODE_MAP = {
    Benefit.DISABLED: "DEFICIENT",
    Benefit.YOUNG_100: "YOUNG_LOW_INCOME",
    Benefit.YOUNG_50: "YOUNG_LOW_INCOME_50",
    Benefit.ELDERLY_100: "ELDER",
    Benefit.ELDERLY_50: "ELDER_50",
    Benefit.NORMAL: "DEFAULT",
}


class GuichepassTicketIssuer(TicketIssuer):
    def __init__(self, ota_config: OTAConfig):
        super().__init__(ota_config)
        self._session = GuichepassClient(
            base_url=cast(str, ota_config.config["base_url"]),
            client_id=cast(str, ota_config.config["client_id"]),
            log_prefix=self.ota_config.name,
            username=cast(str, ota_config.config["username"]),
            password=cast(str, ota_config.config["password"]),
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    async def _call(self, func, *args, retry_policy=None, **kwargs):
        retry_policy = retry_policy or self.retry_policy
        try:
            return await self._circuit_breaker.call(retry_policy(func), *args, **kwargs)
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    async def issue_ticket(self, ticket: Ticket) -> IssuedTicket:
        if not ticket.seat_block_key:
            raise ValueError("ticket.seat_block_key is None")

        benefit_extra = None
        if isinstance(
            ticket.pax_benefit, (ElderlyBenefit, YoungBenefit, DisabledBenefit)
        ):
            benefit_code = BENEFIT_CODE_MAP[ticket.pax_benefit.type]
            benefit_extra = ticket.seat_extra["benefits_extra"][benefit_code]

        response: TicketInfo = await self._call(
            self._session.confirm_reserve,
            retry_policy=NullRetry(),  # Emissão não é idempotente na Guichepass
            request=ConfirmReserveRequest(
                name=ticket.pax_name,
                document=ticket.pax_doc,
                price=ticket.price,
                reserve_id=int(ticket.seat_block_key),
                birthdate=ticket.pax_birthdate.isoformat()
                if ticket.pax_birthdate
                else None,
                ticketTypeId=benefit_extra["id"] if benefit_extra else None,
            ),
        )
        issued_ticket = IssuedTicket(
            localizador=response.ticketNumber,
            numero_pedido=response.orderId,
            numero_bilhete=response.ticketNumber,
            nome_agencia=None,
            emissao_em_contigencia=None,
            numero_embarque_code=None,
            embarque_code=None,
            tipo_embarque=None,
            poltrona=response.seat,
            extra=asdict(response),
        )
        if response.bpeInfo:
            issued_ticket.data_autorizacao = datetime.fromisoformat(
                response.bpeInfo.bpeAuthorizationDate
            )
            issued_ticket.protocolo_autorizacao = response.bpeInfo.bpeAuthProtocol
            issued_ticket.bpe_qrcode = response.bpeInfo.bpeQrCode
            issued_ticket.monitriip_code = response.bpeInfo.bpeMonitriipCode
            issued_ticket.numero_bpe = response.bpeInfo.bpeNumber
            issued_ticket.chave_bpe = response.bpeInfo.bpeAccessKey
            issued_ticket.serie_bpe = response.bpeInfo.bpeSeries
            issued_ticket.outros_tributos = response.bpeInfo.otherTributes
            issued_ticket.plataforma = response.bpeInfo.platform
            issued_ticket.linha = response.bpeInfo.line
            issued_ticket.prefixo = response.bpeInfo.prefix
            issued_ticket.servico = response.service
            issued_ticket.cnpj = response.bpeInfo.emitterHeaderCnpj
            issued_ticket.endereco_empresa = response.bpeInfo.emitterHeaderAddress
        if response.priceInfo:
            issued_ticket.preco = response.priceInfo.basePrice
            issued_ticket.preco_pedagio = response.priceInfo.tollPrice
            issued_ticket.preco_taxa_embarque = response.priceInfo.boardingPrice
            issued_ticket.preco_seguro = response.priceInfo.insurancePrice
            issued_ticket.preco_desconto = response.priceInfo.totalDiscount
            issued_ticket.preco_total = response.priceInfo.price
        return issued_ticket

    async def cancel_ticket(self, ticket: Ticket) -> dict:
        if ticket.seat_block_key is None:
            raise ValueError("ticket.seat_block_key is None")
        assert isinstance(ticket.seat_block_key, str)

        response = await self._call(
            self._session.cancel_reserve, int(ticket.seat_block_key)
        )

        return asdict(response)

    async def refresh_ticket(self, ticket: Ticket) -> RefreshedTicket:
        assert ticket.issued_ticket.extra, "issued ticket does not have extra"
        return await self.refresh_ticket_from_extra(ticket.issued_ticket.extra)

    async def refresh_ticket_from_extra(self, extra: dict) -> RefreshedTicket:
        raise NotImplementedError
