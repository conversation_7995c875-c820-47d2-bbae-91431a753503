from dataclasses import dataclass
from datetime import date
from decimal import Decimal
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPStatus
from json import J<PERSON>NDecodeError
from typing import Any, Dict, List, Optional, cast

import httpx
from yapcache import memoize

from marketplace.caching import NOT_FOUND, cache, lock
from marketplace.otas.exception import (
    IncorrectPrice,
    OTAConnectionError,
    OTAInvalidParameters,
)
from marketplace.otas.http import AsyncClient
from marketplace.otas.vexado.exception import SeatAlreadyTaken


@dataclass
class AuthResponse:
    accessToken: str
    refreshToken: str
    accessTokenSso: str
    refreshTokenSso: str
    expiresAt: str
    expiresSec: int
    currentDate: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            accessToken=raw["accessToken"],
            refreshToken=raw["refreshToken"],
            accessTokenSso=raw["accessTokenSso"],
            refreshTokenSso=raw["refreshTokenSso"],
            expiresAt=raw["expiresAt"],
            expiresSec=raw["expiresSec"],
            currentDate=raw["currentDate"],
        )


@dataclass
class Location:
    id: int
    name: str
    city: str
    state: str
    ibgeCityCode: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            name=raw["name"],
            city=raw["city"],
            state=raw["state"],
            ibgeCityCode=raw["ibgeCityCode"],
        )


@dataclass
class Journey:
    origin: str
    destination: str
    departure: str
    arrival: str
    service: str
    busCompany: str
    busType: str
    operationType: str
    amenities: str | None
    distance: float | None
    stopover: bool
    freeSeats: int
    price: float
    kind: str | None
    message: str
    hasIntinerary: bool
    queryOnly: bool
    connection: bool
    noSeatNumberRequired: bool
    busId: str | float | None
    discounts: list[Any]
    companyDiscount: float
    minAdvanceTime: str | float | None
    minAdvanceTimeInMinutes: str | float | None
    valueToAdd: str | float | None
    expirationDate: str | float | None
    inTransit: bool | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            origin=raw["origin"],
            destination=raw["destination"],
            departure=raw["departure"],
            arrival=raw["arrival"],
            service=raw["service"],
            busCompany=raw["busCompany"],
            busType=raw["busType"],
            operationType=raw["operationType"],
            amenities=raw.get("amenities"),
            distance=raw.get("distance"),
            stopover=raw["stopover"],
            freeSeats=raw["freeSeats"],
            price=raw["price"],
            kind=raw["kind"],
            message=raw["message"],
            hasIntinerary=raw["hasIntinerary"],
            queryOnly=raw["queryOnly"],
            connection=raw["connection"],
            noSeatNumberRequired=raw["noSeatNumberRequired"],
            busId=raw.get("busId"),
            discounts=raw["discounts"],
            companyDiscount=raw["companyDiscount"],
            minAdvanceTime=raw.get("minAdvanceTime"),
            minAdvanceTimeInMinutes=raw.get("minAdvanceTimeInMinutes"),
            valueToAdd=raw.get("valueToAdd"),
            expirationDate=raw.get("expirationDate"),
            inTransit=raw.get("inTransit"),
        )


@dataclass
class Checkpoint:
    id: int
    name: str
    sequence: int
    travelTime: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            name=raw["name"],
            sequence=raw["sequence"],
            travelTime=raw["travelTime"],
        )


@dataclass
class TicketType:
    id: int
    description: str
    salesStrategy: str
    priceValue: float
    isNominalSale: bool
    allowsPromotion: bool
    code: str
    validateRule: bool | None
    numberSeat: int | None
    amount: float | None
    quotType: str | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            description=raw["description"],
            salesStrategy=raw["salesStrategy"],
            priceValue=raw["priceValue"],
            isNominalSale=raw["isNominalSale"],
            allowsPromotion=raw["allowsPromotion"],
            code=raw["code"],
            validateRule=raw.get("validateRule"),
            numberSeat=raw.get("numberSeat"),
            amount=raw.get("amount"),
            quotType=raw.get("quotType"),
        )


@dataclass
class Seat:
    status: str
    x: int
    y: int
    z: int
    number: str
    description: str
    ticketType: list[TicketType]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            status=raw["status"],
            x=raw["x"],
            y=raw["y"],
            z=raw["z"],
            number=raw["number"],
            description=raw["description"],
            ticketType=[TicketType.from_dict(tt) for tt in raw["ticketType"]],
        )

    @property
    def default_price(self) -> float | None:
        for ticket_type in self.ticketType:
            if ticket_type.salesStrategy == "DEFAULT":
                return ticket_type.priceValue


@dataclass
class PriceInfo:
    basePrice: float
    insurancePrice: float
    taxPrice: float
    otherPrice: float
    tollPrice: float
    boardingPrice: float
    commission: float
    companyDiscount: float
    discounts: list[Any]
    cancelationFee: float | None
    price: float
    totalDiscount: float
    priceWithoutInsurance: float
    totalCompanyDiscount: float
    originalPriceWithoutInsurance: float
    priceWithBusCompanyDiscount: float
    priceWithInsurance: float
    originalPrice: float
    priceClassification: Optional[str]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            basePrice=raw["basePrice"],
            insurancePrice=raw["insurancePrice"],
            taxPrice=raw["taxPrice"],
            otherPrice=raw["otherPrice"],
            tollPrice=raw["tollPrice"],
            boardingPrice=raw["boardingPrice"],
            commission=raw["commission"],
            companyDiscount=raw["companyDiscount"],
            discounts=raw["discounts"],
            cancelationFee=raw.get("cancelationFee"),
            price=raw["price"],
            totalDiscount=raw["totalDiscount"],
            priceWithoutInsurance=raw["priceWithoutInsurance"],
            totalCompanyDiscount=raw["totalCompanyDiscount"],
            originalPriceWithoutInsurance=raw["originalPriceWithoutInsurance"],
            priceWithBusCompanyDiscount=raw["priceWithBusCompanyDiscount"],
            priceWithInsurance=raw["priceWithInsurance"],
            originalPrice=raw["originalPrice"],
            priceClassification=raw.get("priceClassification"),
        )


@dataclass
class Quota:
    id: int
    description: str
    salesStrategy: str
    priceValue: float
    isNominalSale: bool
    allowsPromotion: bool
    code: str
    validateRule: bool | None
    numberSeat: int | None
    amount: float | None
    quotType: str | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            description=raw["description"],
            salesStrategy=raw["salesStrategy"],
            priceValue=raw["priceValue"],
            isNominalSale=raw["isNominalSale"],
            allowsPromotion=raw["allowsPromotion"],
            code=raw["code"],
            validateRule=raw.get("validateRule"),
            numberSeat=raw.get("numberSeat"),
            amount=raw.get("amount"),
            quotType=raw.get("quotType"),
        )


@dataclass
class JourneyInfo:
    seats: list[Seat]
    travel: Journey
    priceInfo: PriceInfo
    listQuotas: list[Quota]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            seats=[Seat.from_dict(s) for s in raw["seats"]],
            travel=Journey.from_dict(raw["travel"]),
            priceInfo=PriceInfo.from_dict(raw["priceInfo"]),
            listQuotas=[Quota.from_dict(q) for q in raw["listQuotas"]],
        )


@dataclass
class BusType:
    name: str
    id: int

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(name=raw["name"], id=raw["id"])


@dataclass
class BarCode:
    id: Optional[int]
    value: str
    application: str
    standard: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BarCode":
        return cls(
            id=data.get("id"),
            value=data["value"],
            application=data["application"],
            standard=data["standard"],
        )


@dataclass
class BpeInfo:
    bpeQrCode: str
    platform: str
    prefix: str
    line: str
    totalAmount: float
    discountAmount: float
    paymentAmount: float
    bpeAccessKey: str
    contactTel: str
    specialContactTel: Optional[str]
    bpeQueryUrl: str
    paymentMethod: str
    paymentMethodAmount: float
    changeAmount: float
    discountType: str
    bpeNumber: str
    bpeSeries: str
    bpeAuthProtocol: str
    bpeAuthorizationDate: str
    systemNumber: str
    otherTributes: str
    contingency: bool
    anttOriginCode: Optional[str]
    anttDestinationCode: Optional[str]
    bpeMonitriipCode: Optional[str]
    taxAmount: float
    tollAmount: float
    boardingTaxAmount: float
    insuranceAmount: float
    othersAmounts: float
    agencyHeaderDistrict: Optional[str]
    agencyHeaderCity: Optional[str]
    agencyHeaderCnpj: Optional[str]
    agencyHeaderAddress: Optional[str]
    agencyHeaderNumber: Optional[str]
    agencyHeaderCompanyName: Optional[str]
    agencyHeaderState: Optional[str]
    emitterHeaderDistrict: str
    emitterHeaderPostalCode: str
    emitterHeaderCity: str
    emitterHeaderCnpj: str
    emitterHeaderAddress: str
    emitterHeaderStateRegistration: str
    emitterHeaderNumber: str
    emitterHeaderCompanyName: str
    emitterHeaderState: str
    optionalMessage: Optional[str]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BpeInfo":
        return cls(
            bpeQrCode=data["bpeQrCode"],
            platform=data["platform"],
            prefix=data["prefix"],
            line=data["line"],
            totalAmount=data["totalAmount"],
            discountAmount=data["discountAmount"],
            paymentAmount=data["paymentAmount"],
            bpeAccessKey=data["bpeAccessKey"],
            contactTel=data["contactTel"],
            specialContactTel=data.get("specialContactTel"),
            bpeQueryUrl=data["bpeQueryUrl"],
            paymentMethod=data["paymentMethod"],
            paymentMethodAmount=data["paymentMethodAmount"],
            changeAmount=data["changeAmount"],
            discountType=data["discountType"],
            bpeNumber=data["bpeNumber"],
            bpeSeries=data["bpeSeries"],
            bpeAuthProtocol=data["bpeAuthProtocol"],
            bpeAuthorizationDate=data["bpeAuthorizationDate"],
            systemNumber=data["systemNumber"],
            otherTributes=data["otherTributes"],
            contingency=data["contingency"],
            anttOriginCode=data.get("anttOriginCode"),
            anttDestinationCode=data.get("anttDestinationCode"),
            bpeMonitriipCode=data.get("bpeMonitriipCode"),
            taxAmount=data["taxAmount"],
            tollAmount=data["tollAmount"],
            boardingTaxAmount=data["boardingTaxAmount"],
            insuranceAmount=data["insuranceAmount"],
            othersAmounts=data["othersAmounts"],
            agencyHeaderDistrict=data.get("agencyHeaderDistrict"),
            agencyHeaderCity=data.get("agencyHeaderCity"),
            agencyHeaderCnpj=data.get("agencyHeaderCnpj"),
            agencyHeaderAddress=data.get("agencyHeaderAddress"),
            agencyHeaderNumber=data.get("agencyHeaderNumber"),
            agencyHeaderCompanyName=data.get("agencyHeaderCompanyName"),
            agencyHeaderState=data.get("agencyHeaderState"),
            emitterHeaderDistrict=data["emitterHeaderDistrict"],
            emitterHeaderPostalCode=data["emitterHeaderPostalCode"],
            emitterHeaderCity=data["emitterHeaderCity"],
            emitterHeaderCnpj=data["emitterHeaderCnpj"],
            emitterHeaderAddress=data["emitterHeaderAddress"],
            emitterHeaderStateRegistration=data["emitterHeaderStateRegistration"],
            emitterHeaderNumber=data["emitterHeaderNumber"],
            emitterHeaderCompanyName=data["emitterHeaderCompanyName"],
            emitterHeaderState=data["emitterHeaderState"],
            optionalMessage=data.get("optionalMessage"),
        )


@dataclass
class TicketInfo:
    origin: str
    destination: str
    departure: str
    arrival: str
    service: str
    busCompany: str
    busType: str
    operationType: str
    amenities: Optional[List[str]]
    distance: Optional[float]
    stopover: bool
    id: int
    seat: Optional[str]
    priceInfo: Optional[PriceInfo]
    alternativePrices: List
    status: str
    name: Optional[str]
    document: Optional[str]
    ticket: Optional[str]
    ticketNumber: Optional[str]
    message: Optional[str]
    metaData: Optional[Dict]
    orderId: Optional[str]
    buyerInfoId: Optional[str]
    optInInsurance: bool
    noSeatNumberRequired: bool
    barCodes: Optional[List[BarCode]]
    timestamp: str
    bpeInfo: Optional[BpeInfo]
    channel: Optional[str]
    insuranceSelected: bool

    @classmethod
    def from_dict(cls, data: Dict) -> "TicketInfo":
        price_info = (
            PriceInfo.from_dict(data["priceInfo"]) if data.get("priceInfo") else None
        )
        bar_codes = (
            [BarCode.from_dict(bc) for bc in data.get("barCodes", [])]
            if data.get("barCodes") is not None
            else None
        )
        return cls(
            origin=data["origin"],
            destination=data["destination"],
            departure=data["departure"],
            arrival=data["arrival"],
            service=data["service"],
            busCompany=data["busCompany"],
            busType=data["busType"],
            operationType=data["operationType"],
            amenities=data.get("amenities"),
            distance=data.get("distance"),
            stopover=data["stopover"],
            id=data["id"],
            seat=data.get("seat"),
            priceInfo=price_info,
            alternativePrices=data.get("alternativePrices", []),
            status=data["status"],
            name=data.get("name"),
            document=data.get("document"),
            ticket=data.get("ticket"),
            ticketNumber=data.get("ticketNumber"),
            message=data.get("message"),
            metaData=data.get("metaData"),
            orderId=data.get("orderId"),
            buyerInfoId=data.get("buyerInfoId"),
            optInInsurance=data["optInInsurance"],
            noSeatNumberRequired=data["noSeatNumberRequired"],
            barCodes=bar_codes,
            timestamp=data["timestamp"],
            bpeInfo=BpeInfo.from_dict(data["bpeInfo"]) if data.get("bpeInfo") else None,
            channel=data.get("channel"),
            insuranceSelected=data["insuranceSelected"],
        )


@dataclass
class ConfirmReserveRequest:
    name: str
    document: str
    price: Decimal
    reserve_id: int
    birthdate: str | None
    ticketTypeId: Optional[str]


async def handle_response(response: httpx.Response):
    if response.status_code == HTTPStatus.BAD_REQUEST:
        await response.aread()
        try:
            payload = response.json()
        except JSONDecodeError:
            payload = {}

        # guichepass da erro 400 com response vazio caso algum campo esteja inválido
        if not payload:
            raise OTAInvalidParameters()
    if response.status_code == HTTPStatus.BAD_GATEWAY:
        raise OTAConnectionError()


class GuichepassClient:
    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        client_id: str = "WEB_SALE",
        timeout: int = 20,
        log_prefix: str | None = None,
    ):
        self.username = username
        self.password = password
        self.base_url = base_url
        log_prefix = (
            f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__
        )
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={"ClientID": client_id},
            log_prefix=log_prefix,
            event_hooks={
                "response": [handle_response],
            },
        )

    async def call_with_auth(
        self,
        method: str,
        url: str,
        params: dict | None = None,
        json: dict | None = None,
        headers: dict | None = None,
        counter: int = 0,
    ) -> httpx.Response:
        auth: AuthResponse = await self._get_auth()
        authenticated_headers = (headers or {}) | {"X-Authorization": auth.accessToken}
        response = await self._client.request(
            method,
            url,
            params=params,
            json=json,
            headers=authenticated_headers,
        )
        # necessary while rodoviaria is concurrently refreshing tokens
        if response.status_code == HTTPStatus.UNAUTHORIZED and counter < 1:
            await self._delete_auth_cache()
            return await self.call_with_auth(
                method, url, params, json, headers, counter + 1
            )
        return response

    async def _delete_auth_cache(self):
        # TODO: add delete method to yapcache
        async with lock(f"guichepass:{self.base_url}:token:lock"):
            await cache.set(
                f"guichepass:{self.base_url}:token", NOT_FOUND, ttl=1, best_before=0
            )

    async def _get_auth(self) -> AuthResponse:
        auth_response = await self._get_cached_auth()
        if auth_response == NOT_FOUND:
            # TODO: Temporário até subir o delete cache no yapcache
            auth_response = await self.auth_login(
                username=cast(str, self.username),
                password=cast(str, self.password),
            )
        return auth_response

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: cast(AuthResponse, result).expiresSec,
        cache_key=lambda self: f"guichepass:{self.base_url}:token",
        best_before=lambda self, *args, **kwargs: float("inf"),
        lock=lock,
    )
    async def _get_cached_auth(self) -> AuthResponse:
        auth_response = await self.auth_login(
            username=cast(str, self.username),
            password=cast(str, self.password),
        )
        return auth_response

    async def auth_login(self, username: str, password: str) -> AuthResponse:
        response = await self._client.post(
            "/auth/login",
            headers={
                "username": username,
                "password": password,
            },
        )
        response.raise_for_status()

        return AuthResponse.from_dict(response.json())

    async def locations(self) -> list[Location]:
        response = await self.call_with_auth(
            HTTPMethod.GET,
            "/web-sale/leg-stops/no-page",
        )

        response.raise_for_status()
        return [Location.from_dict(t) for t in response.json()]

    async def journeys_search(
        self, origin: int, destination: int, departure_date: date
    ) -> list[Journey]:
        response = await self.call_with_auth(
            HTTPMethod.POST,
            "/web-sale/search",
            json={
                "origin": origin,
                "destination": destination,
                "date": departure_date.isoformat(),
            },
        )

        response.raise_for_status()
        return [Journey.from_dict(t) for t in response.json()]

    async def get_itinerary(self, service: str) -> list[Checkpoint]:
        response = await self.call_with_auth(
            HTTPMethod.GET,
            f"/web-sale/service/{service}/itinerary",
        )

        response.raise_for_status()
        return [Checkpoint.from_dict(t) for t in response.json()]

    async def get_bus_layout(
        self,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        service: str,
    ) -> JourneyInfo:
        response = await self.call_with_auth(
            HTTPMethod.POST,
            "/web-sale/bus-layout",
            json={
                "date": departure_date.isoformat(),
                "origin": origin,
                "destination": destination,
                "busCompany": company_id,
                "service": service,
            },
        )

        if response.status_code == HTTPStatus.UNPROCESSABLE_ENTITY:
            payload = response.json()
            if payload["text"] == "Serviço fora do período de vendas":
                raise OTAInvalidParameters("Serviço fora do período de vendas")

        response.raise_for_status()
        return JourneyInfo.from_dict(response.json())

    async def get_bus_types(self) -> list[BusType]:
        response = await self.call_with_auth(
            HTTPMethod.GET,
            "/web-sale/seatTypes",
        )

        response.raise_for_status()
        return [BusType.from_dict(bt) for bt in response.json()]

    async def create_reserve(
        self,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        service: str,
        seat: int,
    ) -> TicketInfo:
        response = await self.call_with_auth(
            HTTPMethod.POST,
            "/web-sale/create-reserve",
            json={
                "date": departure_date.strftime("%Y-%m-%d"),
                "origin": origin,
                "destination": destination,
                "busCompany": company_id,
                "service": service,
                "seat": seat,
            },
        )

        if response.status_code == HTTPStatus.UNPROCESSABLE_ENTITY:
            payload = response.json()
            if payload["text"] == "Assento Reservado":
                raise SeatAlreadyTaken("Assento Reservado")

        response.raise_for_status()
        return TicketInfo.from_dict(response.json())

    async def confirm_reserve(self, request: ConfirmReserveRequest):
        response = await self.call_with_auth(
            HTTPMethod.POST,
            "/web-sale/confirm-reserve",
            json={
                "name": request.name,
                "document": request.document,
                "price": str(request.price),
                "id": request.reserve_id,
                "birthdate": request.birthdate,
                "ticketTypeId": request.ticketTypeId,
            },
        )

        if response.status_code == HTTPStatus.UNPROCESSABLE_ENTITY:
            payload = response.json()
            if payload.get("code", "") == "reserve.value_invalid":
                expected_value = payload["text"].split("Expected value: ")[1]
                raise IncorrectPrice(Decimal(expected_value), request.price)

        response.raise_for_status()
        return TicketInfo.from_dict(response.json())

    async def cancel_reserve(self, reserve_id: int) -> TicketInfo:
        response = await self.call_with_auth(
            HTTPMethod.POST,
            "/web-sale/cancel-reserve",
            json={"id": reserve_id},
        )

        response.raise_for_status()
        return TicketInfo.from_dict(response.json())
