import asyncio
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from typing import cast, override

from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    SeatMap,
    Travel,
)
from marketplace.otas.exception import (
    IncompleteExtra,
    OTACircuitOpenError,
    OTAConnectionError,
    OTATimeoutException,
)
from marketplace.otas.guichepass.client import (
    Checkpoint as GuichepassCheckpoint,
)
from marketplace.otas.guichepass.client import (
    GuichepassClient,
    Journey,
    JourneyInfo,
)
from marketplace.searcher import OTASearcher


@dataclass
class GuichepassTravelExtra:
    origin: str
    destination: str
    service: str
    busCompany: str
    departure: str | None = None

    @classmethod
    def for_seat_map(cls, extra: dict) -> "GuichepassTravelExtra":
        try:
            return GuichepassTravelExtra(
                origin=extra["origin"],
                destination=extra["destination"],
                service=extra["service"],
                busCompany=extra["busCompany"],
            )
        except KeyError as error:
            raise IncompleteExtra(f"Missing extra field {error}") from error


class GuichepassSearcher(OTASearcher):
    TICKET_TYPE_MAP = {
        "DEFICIENT": Benefit.DISABLED,
        "YOUNG_LOW_INCOME": Benefit.YOUNG_100,
        "YOUNG_LOW_INCOME_50": Benefit.YOUNG_50,
        "ELDER": Benefit.ELDERLY_100,
        "ELDER_50": Benefit.ELDERLY_50,
        "DEFAULT": Benefit.NORMAL,
    }

    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        client_id: str,
        username: str,
        password: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._session = GuichepassClient(
            base_url=base_url,
            client_id=client_id,
            log_prefix=self.ota_config.name,
            username=username,
            password=password,
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    async def _call(self, func, *args, **kwargs):
        try:
            return await self._circuit_breaker.call(
                self.retry_policy(func), *args, **kwargs
            )
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        travels, last_synced, _ = await self._search_travels(
            departure_date=departure_date,
            origin=origin.extra["id"],
            destination=destination.extra["id"],
        )
        journeys = cast(list[Journey], travels)

        found = []
        for journey in journeys:
            company = self._get_ota_company(int(journey.busCompany))
            bus_type = await self._get_bus_type(journey.busType)
            seat_type = self._get_ota_seat_type_by_name(bus_type)
            if company is None or seat_type is None:
                continue

            found.append(
                Travel(
                    ota="guichepass",
                    ota_config_id=self.ota_config.id,
                    code=f"{journey.service}_{journey.origin}_{journey.destination}_{journey.service}_{journey.busCompany}",
                    service_code=str(journey.service),
                    itinerary_class_code=f"{journey.service}",
                    company=company,
                    itinerary=[],
                    origin=cast(Place, origin.place),
                    destination=cast(Place, destination.place),
                    departure_at=datetime.fromisoformat(journey.departure),
                    arrival_at=datetime.fromisoformat(journey.arrival),
                    seat_type=seat_type,
                    price=journey.price,
                    available_seats=journey.freeSeats,
                    total_seats=journey.freeSeats,
                    last_synced=last_synced,
                    extra=asdict(
                        GuichepassTravelExtra(
                            origin=journey.origin,
                            destination=journey.destination,
                            departure=journey.departure,
                            busCompany=journey.busCompany,
                            service=journey.service,
                        )
                    ),
                )
            )

        return found

    def _has_few_seats_travel(self, search_response: list[Journey]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for travel in search_response:
            if travel.freeSeats < threshold:
                return True

        return False

    async def _get_bus_type(self, bus_type_id: str):
        bus_types_map = await self._bus_types_map()
        return bus_types_map[int(bus_type_id)]

    @memoize(cache, ttl=86400, cache_key=lambda self: f"bustypes_:{self.ota_config.id}")
    async def _bus_types_map(self) -> dict[int, str]:
        bus_types = await self._call(self._session.get_bus_types)
        return {bt.id: bt.name for bt in bus_types}

    @override
    async def list_places(self) -> list[OTAPlace]:
        locations = await self._call(self._session.locations)

        places = set()
        for location in locations:
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=f"{location.name} - {location.state}",
                    extra=location,
                )
            )

        return list(places)

    @override
    async def available_seats(
        self, travel: InputTravel, search_for_price: bool = True
    ) -> SeatMap:
        travel_extra = GuichepassTravelExtra(**travel.extra)
        seats = []
        assert travel.departure_at
        journey_info = await self._seating_map(
            departure_date=datetime.fromisoformat(travel.departure_at).date(),
            origin=travel_extra.origin,
            destination=travel_extra.destination,
            company_id=travel_extra.busCompany,
            service=travel_extra.service,
        )

        journey_info = cast(JourneyInfo, journey_info)
        for seat in journey_info.seats:
            seat_type = self._get_ota_seat_type_by_name(seat.description)
            if not seat_type:
                continue

            benefits = {
                category: asdict(ticket_type)
                for ticket_type in seat.ticketType
                if (
                    category := self._get_benefit(
                        ticket_type.salesStrategy,
                        ticket_type.description,
                        self.TICKET_TYPE_MAP.get(ticket_type.salesStrategy),
                    )
                )
            }
            seats.append(
                Seat(
                    number=seat.number,
                    floor=seat.z + 1,
                    row=seat.x + 1,
                    column=seat.y + 1,
                    available=seat.status == "FREE",
                    category=seat.description,
                    seat_type=seat_type.seat_type if seat_type else None,
                    benefits=list(benefits.keys()),
                    price=journey_info.priceInfo.price,
                    extra={
                        "description": seat.description,
                        "benefits_extra": {
                            str(category): benefit
                            for category, benefit in benefits.items()
                        },
                    },
                )
            )

        return SeatMap(seats=seats, base_price=journey_info.priceInfo.price)

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        def time_str_to_timedelta(time_str: str) -> timedelta:
            hours, minutes, seconds = time_str.split(":")
            return timedelta(
                hours=int(hours), minutes=int(minutes), seconds=int(seconds)
            )

        travel_extra = GuichepassTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        post_origin_checkpoint = False
        full_itinerary = await self._travel_itinerary(travel_extra.service)
        assert travel_extra.departure
        initial_departure = datetime.fromisoformat(travel_extra.departure)
        itinerary: list[Checkpoint] = []
        before_init_duration = timedelta(minutes=0)
        for cp in cast(list[GuichepassCheckpoint], full_itinerary):
            if cp.id == int(travel_extra.origin):
                post_origin_checkpoint = True
                before_init_duration = time_str_to_timedelta(cp.travelTime)
            if post_origin_checkpoint:
                duration = time_str_to_timedelta(cp.travelTime) - before_init_duration
                itinerary.append(
                    Checkpoint(
                        name=cp.name,
                        departure_at=initial_departure + duration,
                        distance_km=duration.total_seconds() * 60 / 3600,
                    )
                )
                if cp.id == int(travel_extra.destination):
                    break
        return itinerary

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        departure_date,
        origin,
        destination: f"search:{self.ota_config.id}:{departure_date}:{origin}:{destination}:v3",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self,
        departure_date: date,
        origin: int,
        destination: int,
    ) -> tuple[list[Journey] | None, datetime | None, int]:
        travels = await self._call(
            self._session.journeys_search,
            departure_date=departure_date,
            origin=origin,
            destination=destination,
        )
        last_synced = datetime.now(timezone.utc)
        cache_ttl = await self._dynamic_search_ttl(
            travels,
            origin,
            destination,
            departure_date,
        )
        return travels, last_synced, cache_ttl

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self, service: f"itinerary:{self.ota_config.id}:{service}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _travel_itinerary(self, service: str) -> list[GuichepassCheckpoint]:
        return await self._call(self._session.get_itinerary, service=service)

    @memoize(
        cache,
        ttl=45,
        cache_key=lambda self,
        departure_date,
        origin,
        destination,
        company_id,
        service: f"map:{self.ota_config.id}:{departure_date.isoformat()}:{origin}:{destination}:{company_id}:{service}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(
        self,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        service: str,
    ) -> JourneyInfo:
        return await self._call(
            self._session.get_bus_layout,
            departure_date=departure_date,
            origin=origin,
            destination=destination,
            company_id=company_id,
            service=service,
        )

    @override
    async def block_seat(
        self, travel: InputTravel, seat: Seat, benefit: Benefit = Benefit.NORMAL
    ) -> BlockedSeat:
        assert "benefits_extra" in seat.extra
        assert benefit in seat.extra["benefits_extra"], (
            f"Benefit {benefit} not available for seat {seat.number}"
        )
        assert travel.departure_at, "departure_at is required"

        departure_at = datetime.fromisoformat(travel.departure_at).date()
        travel_extra = GuichepassTravelExtra(**travel.extra)
        ticket_info = await self._session.create_reserve(
            departure_date=departure_at,
            origin=int(travel_extra.origin),
            destination=int(travel_extra.destination),
            company_id=travel_extra.busCompany,
            service=travel_extra.service,
            seat=int(seat.number),
        )

        benefit_extra = seat.extra["benefits_extra"][benefit]

        return BlockedSeat(
            **asdict(seat),
            best_before=datetime.now(timezone.utc) + timedelta(minutes=15),
            block_key=str(ticket_info.id),
            extra_rodoviaria={
                "reserva_id": ticket_info.id,
                "categoria_external_id": benefit_extra["id"],
                "preco": benefit_extra["priceValue"],
            },
        )

    @override
    async def unblock_seat(
        self, travel: InputTravel, blocked_seat: BlockedSeat
    ) -> bool:
        assert isinstance(blocked_seat.block_key, int)
        await self._session.cancel_reserve(blocked_seat.block_key)
        return True
