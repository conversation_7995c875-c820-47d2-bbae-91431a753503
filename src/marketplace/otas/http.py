import logging
import time
import typing

import httpx

from marketplace.otas.exception import OTAConnectionError, OTATimeoutException

logger = logging.getLogger(__name__)


class AsyncClient(httpx.AsyncClient):
    def __init__(self, log_prefix: str = "HTTPClient", *args, **kwargs):
        self.log_prefix = log_prefix
        super().__init__(*args, **kwargs)

    async def send(
        self,
        request: httpx.Request,
        *,
        stream: bool = False,
        auth: typing.Any = httpx.USE_CLIENT_DEFAULT,
        follow_redirects: typing.Any = httpx.USE_CLIENT_DEFAULT,
    ) -> httpx.Response:
        start = time.monotonic()
        try:
            response = await super().send(
                request,
                stream=stream,
                auth=auth,
                follow_redirects=follow_redirects,
            )
            log_http_request_response(
                request, response, time.monotonic() - start, self.log_prefix
            )
            return response
        except httpx.TimeoutException as exc:
            log_http_request_error(
                request, exc, time.monotonic() - start, self.log_prefix
            )
            raise OTATimeoutException(f"{self.log_prefix} Timed Out") from exc
        except (httpx.ConnectError, httpx.RequestError) as exc:
            log_http_request_error(
                request, exc, time.monotonic() - start, self.log_prefix
            )
            raise OTAConnectionError(f"{self.log_prefix} Connection Error") from exc
        except Exception as exc:
            log_http_request_error(
                request, exc, time.monotonic() - start, self.log_prefix
            )
            raise


def log_http_request_response(
    request: httpx.Request,
    http_response: httpx.Response,
    duration: float,
    log_prefix: str = "HTTPClient",
) -> None:
    extra = {
        "status_code": http_response.status_code,
        "duration": duration,
        "path": request.url.path,
        "method": request.method,
    }
    if http_response.is_error:
        try:
            if http_response.is_closed:
                extra["response"] = http_response.text
            else:
                extra["response"] = "<stream not read>"
        except Exception as exception:
            logger.warning(
                "%s Failed to log response body for %s %s",
                log_prefix,
                request.method,
                request.url,
                extra={
                    "error": str(exception) or exception.__class__.__name__,
                },
            )

    logger.info(
        "%s response %s %s (%s)",
        log_prefix,
        request.method,
        request.url,
        http_response.status_code,
        extra=extra,
    )


def log_http_request_error(
    request: httpx.Request,
    exception: Exception,
    duration: float,
    log_prefix: str = "HTTPClient",
) -> None:
    logger.warning(
        "%s response %s %s (0)",
        log_prefix,
        request.method,
        request.url,
        extra={
            "error": str(exception) or exception.__class__.__name__,
            "status_code": 0,
            "duration": duration,
            "path": request.url.path,
            "method": request.method,
        },
    )
