from dataclasses import asdict, dataclass
from datetime import date, datetime
from http import HTTPStatus
from json import JSONDecodeError
from typing import Optional

import dacite
import httpx

from marketplace.otas.exception import (
    ItineraryNotFound,
    OTAConnectionError,
    OTANotJSONResponse,
    OTATimeoutException,
    OTATooManyRequests,
    SeatNotBlocked,
    SeatUnavailable,
    TicketAlreadyCancelled,
    TicketCancellationPeriodExpired,
    TravelUnavailable,
)
from marketplace.otas.http import AsyncClient


class OTASectionalNotOffered(Exception): ...


@dataclass
class Empresa:
    id: int
    nome: str
    cnpj: Optional[str]


@dataclass
class Desconto:
    vendeApi: bool
    dataModificacao: str
    desconto: float
    descontoPorc: float
    descontoTarifa: bool
    descontoSeguro: bool
    descontoTMR: bool
    descontoTaxaEmbarque: bool
    descontoPedagio: bool


@dataclass
class Categoria:
    categoriaId: int
    desccategoria: str
    vendeApi: bool
    dataModificacao: str
    descontos: list[Desconto]


@dataclass
class Local:
    id: int
    cidade: str
    sigla: str
    uf: str
    empresas: str


@dataclass
class OrigemDestinos:
    origem: Local
    destinos: list[Local]


@dataclass
class SearchResponse:
    origem: Local
    destino: Local
    data: str
    lsServicos: list["Servico"]


@dataclass
class Servico:
    identificadorViagem: str
    servico: str
    rutaId: int
    prefixoLinha: str
    marcaId: int
    grupo: str
    grupoOrigemId: int
    grupoDestinoId: int
    saida: str
    chegada: str
    dataCorrida: str
    dataSaida: str
    poltronasLivres: int
    poltronasTotal: int
    preco: float
    precoOriginal: float
    classe: str
    empresa: str
    empresaId: int
    mensagemServico: str
    vende: bool
    bpe: bool
    km: float
    modalidadeBpe: str
    bpeImpressaoPosterior: bool
    numeroOnibus: Optional[str]
    tarifa: float
    restricaoDinamicaBloqueioTrecho: bool
    indRutaInternacional: bool
    conexao: Optional["Conexao"] | None = None
    cnpj: Optional[str] | None = None


@dataclass
class Conexao:
    servicoConexao: str
    localidadeConexao: str
    localidadeConexaoId: int
    empresa: str
    empresaId: int
    rutaId: int
    marcaId: int
    dataCorridaConexao: str
    dataSaidaConexao: str
    primeiroTrechoPoltronasLivres: int
    primeiroTrechoPoltronasTotal: int
    primeiroTrechoClasse: str
    primeiroTrechoDataCorrida: str
    primeiroTrechoDataSaida: str
    primeiroTrechoDataChegada: str
    primeiroTrechoHoraSaida: str
    primeiroTrechoHoraChegada: str
    primeiroTrechoPreco: str
    primeiroTrechoPrecoOriginal: str
    primeiroTrechoServico: str
    primeiroTrechoLinha: int
    primeiroTrechoEmpresa: str
    primeiroTrechoEmpresaId: int
    primeiroTrechoMarca: int
    primeiroTrechoOrigem: int
    primeiroTrechoOrigemDescricao: str
    primeiroTrechoDestino: int
    primeiroTrechoDestinoDescricao: str
    primeiroTrechoVende: bool
    primeiroTrechoIsBpe: bool
    primeiroTrechoSequencia: int
    segundoTrechoPoltronasLivres: int
    segundoTrechoPoltronasTotal: int
    segundoTrechoClasse: str
    segundoTrechoDataCorrida: str
    segundoTrechoDataSaida: str
    segundoTrechoDataChegada: str
    segundoTrechoHoraSaida: str
    segundoTrechoHoraChegada: str
    segundoTrechoPreco: str
    segundoTrechoPrecoOriginal: str
    segundoTrechoServico: str
    segundoTrechoLinha: int
    segundoTrechoEmpresa: str
    segundoTrechoEmpresaId: int
    segundoTrechoMarca: int
    segundoTrechoOrigem: int
    segundoTrechoOrigemDescricao: str
    segundoTrechoDestino: int
    segundoTrechoDestinoDescricao: str
    segundoTrechoVende: bool
    segundoTrechoIsBpe: bool
    segundoTrechoSequencia: int
    terceiroTrechoVende: bool
    terceiroTrechoIsBpe: bool
    vende: bool
    km: float
    conexionCtrlId: int
    conexionGrupo: int
    bpe: bool
    cnpj: Optional[str] | None = None

    @property
    def first_leg_arrival_at(self):
        first_leg_arrival_date = self.primeiroTrechoDataChegada
        first_leg_arrivel_time = self.primeiroTrechoHoraChegada
        return datetime.strptime(
            f"{first_leg_arrival_date} {first_leg_arrivel_time}", "%d/%m/%Y %H:%M"
        )

    @property
    def first_leg_departure_at(self):
        first_leg_departure_date = self.primeiroTrechoDataSaida
        first_leg_departure_time = self.primeiroTrechoHoraSaida
        return datetime.strptime(
            f"{first_leg_departure_date} {first_leg_departure_time}", "%d/%m/%Y %H:%M"
        )

    @property
    def second_leg_departure_at(self):
        second_leg_departure_date = self.segundoTrechoDataSaida
        second_leg_departure_time = self.segundoTrechoHoraSaida
        return datetime.strptime(
            f"{second_leg_departure_date} {second_leg_departure_time}", "%d/%m/%Y %H:%M"
        )


@dataclass
class MapaPoltrona:
    x: str
    y: str
    disponivel: bool
    numero: str
    categoriaReservadaId: int


@dataclass
class Localidade:
    id: int
    descripcion: str
    cve: str
    indIntegracaoW2i: bool


@dataclass
class PricingSequencia:
    quantidaPoltronas: str
    precoPoltrona: str
    sequencia: str
    tipo: str


@dataclass
class PricingPoltrona:
    numero: str
    precoNumero: str
    pricingId: int
    porcentagem: float
    nome: Optional[str] = None


@dataclass
class BuscaOnibusResponse:
    origem: Local
    destino: Local
    data: str
    servico: str
    dataSaida: str
    mapaPoltrona: list[MapaPoltrona]
    pricingSequencia: list[PricingSequencia]
    pricingPoltrona: list[PricingPoltrona]
    lsLocalidadeEmbarque: Optional[list[Localidade]] = None
    lsLocalidadeDesembarque: Optional[list[Localidade]] = None
    dataChegada: Optional[str] = None
    poltronasLivres: Optional[int] = None
    empresaCorridaId: Optional[int] = None
    classeServico: Optional[str] = None
    dataCorrida: Optional[str] = None


@dataclass
class Preco:
    tarifa: str
    outros: str
    pedagio: str
    seguro: str
    preco: str
    tarifaComPricing: str
    taxaEmbarque: str
    seguroW2I: str


@dataclass
class SeguroOpcional:
    km: Optional[int]
    valor: Optional[float]


@dataclass
class BloquearPoltronaResponse:
    origem: Local
    destino: Local
    data: str
    servico: str
    assento: str
    duracao: int
    transacao: str
    preco: Preco
    rutaid: str
    seguroOpcional: Optional[SeguroOpcional]
    numOperacion: str
    localizador: str
    boletoId: int
    empresaCorridaId: int
    dataSaida: str
    dataChegada: str
    dataCorrida: str
    classeServicoId: int


@dataclass
class LocalidadeParada:
    id: int
    cidade: str
    uf: str

    @property
    def cidade_com_uf(self):
        return f"{self.cidade} - {self.uf}"


@dataclass
class Parada:
    localidade: LocalidadeParada
    distancia: str  # ou float, se quiser converter (ex: float("112.96"))
    permanencia: str  # formato HH:MM
    data: str  # formato: YYYY-MM-DD
    hora: str  # formato HH:MM
    bloqueioCanalVenda: bool

    @property
    def data_hora_partida(self):
        return f"{self.data}T{self.hora}:00"


@dataclass
class ItinerarioResponse:
    servico: str
    data: str
    lsParadas: list[Parada]


@dataclass
class CancelarVendaResponse:
    statusCancelaApenasSeguroOpcional: bool
    status: bool


@dataclass
class DevolverVendaResponse(CancelarVendaResponse):
    multa: str


async def handle_response(response: httpx.Response, log_prefix: str = "HTTPClient"):
    if response.status_code == HTTPStatus.BAD_REQUEST:
        await response.aread()
        try:
            payload = response.json()
        except JSONDecodeError as exc:
            raise OTANotJSONResponse("NotJSONResponse") from exc

        if "Unable to acquire JDBC Connection" in payload["message"]:
            raise OTAConnectionError("Unable to acquire JDBC Connection")
        if "408 Request Time-out" in payload["message"]:
            raise OTATimeoutException("408 Request Time-out")
    if response.status_code in (
        HTTPStatus.INTERNAL_SERVER_ERROR,
        HTTPStatus.NOT_ACCEPTABLE,
    ):
        raise OTAConnectionError()
    if response.status_code == HTTPStatus.REQUEST_TIMEOUT:
        raise OTATimeoutException()
    if response.status_code == HTTPStatus.SERVICE_UNAVAILABLE:
        raise OTATooManyRequests()


class TotalbusClient:
    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        tenant_id: str,
        timeout: int = 20,
        log_prefix: Optional[str] = None,
    ):
        log_prefix = (
            f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__
        )
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={
                "x-tenant-id": tenant_id,
            },
            auth=httpx.BasicAuth(username=username, password=password),
            log_prefix=log_prefix,
            event_hooks={
                "response": [handle_response],
            },
        )

    async def consultar_empresas(self):
        response = await self._client.get("/api-gateway/catalogo/consultarEmpresas")
        response.raise_for_status()
        data = response.json()
        return [dacite.from_dict(Empresa, item) for item in data]

    async def consultar_beneficios(self) -> list[Categoria]:
        response = await self._client.get("/api-gateway/categoria/consultarCategorias")
        response.raise_for_status()
        data = response.json()
        return [dacite.from_dict(Categoria, item) for item in data]

    async def buscar_origem_destino(self, company_id: int):
        response = await self._client.get(
            f"/api-gateway/localidade/buscarOrigenDestino/{company_id}"
        )
        response.raise_for_status()
        data = response.json()
        return [dacite.from_dict(OrigemDestinos, item) for item in data]

    async def buscar_corrida(
        self, origem: int, destino: int, data: date, volta: bool = False
    ) -> SearchResponse:
        response = await self._client.post(
            "/api-gateway/consultacorrida/buscaCorrida",
            json={
                "origem": origem,
                "destino": destino,
                "data": data.isoformat(),
                "volta": volta,
            },
        )

        if (
            response.status_code == 400
            and response.json()["message"] == "Trecho não disponível"
        ):
            raise OTASectionalNotOffered

        response.raise_for_status()
        return dacite.from_dict(SearchResponse, response.json())

    async def buscar_onibus_detalhado(
        self,
        origem: int,
        destino: int,
        data: date,
        servico: int,
    ) -> BuscaOnibusResponse:
        response = await self._client.post(
            "/api-gateway/consultaonibus/buscaOnibus",
            json={
                "origem": origem,
                "destino": destino,
                "data": data.isoformat(),
                "servico": servico,
            },
        )

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            message = payload["message"]
            if "SERVICO NAO LOCALIZADO" in message:
                raise TravelUnavailable

        response.raise_for_status()
        return dacite.from_dict(BuscaOnibusResponse, response.json())

    async def busca_itinerario_corrida(
        self, servico: int, data_corrida: str
    ) -> ItinerarioResponse:
        response = await self._client.post(
            "/api-gateway/itinerario/buscarItinerarioCorrida",
            json={"servico": servico, "data": data_corrida},
        )

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            message = payload["message"]
            if "ITINERARIO NAO LOCALIZADO" in message:
                raise ItineraryNotFound

        response.raise_for_status()
        return dacite.from_dict(ItinerarioResponse, response.json())

    async def bloquear_poltrona(
        self,
        origem: int,
        destino: int,
        data: date,
        servico: int,
        poltrona: str,
        categoriaId: str | None = None,
    ):
        params = {
            "origem": origem,
            "destino": destino,
            "data": data.isoformat(),
            "servico": servico,
            "poltrona": poltrona,
        }

        if categoriaId:
            params["categoriaId"] = categoriaId

        response = await self._client.post(
            "/api-gateway/bloqueiopoltrona/bloquearPoltrona",
            json=params,
        )

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            message = payload["message"]
            if (
                "asientoNoExiste" in message
                or "asientoNoDisponibleEmpresa" in message
                or "A poltrona já foi selecionada" in message
                or "Poltrona informada não esta disponível para a categoria desejada"
                in message
            ):
                raise SeatUnavailable
            if (
                "Bloqueio não concluído" in message
                or "já não tem disponibilidade" in message
                or "Categoria indisponível" in message
                or "Trecho bloqueado para venda" in message
            ):
                raise TravelUnavailable
            raise SeatNotBlocked(message)

        response.raise_for_status()
        return dacite.from_dict(BloquearPoltronaResponse, response.json())

    async def desbloquear_poltrona(self, transacao: str) -> bool:
        response = await self._client.post(
            "/api-gateway/desbloqueiopoltrona/desbloquearPoltrona",
            json={"transacao": transacao},
        )
        response.raise_for_status()
        data = response.json()
        return data["status"]

    async def confirmar_venda(
        self,
        transacao: str,
        nome_passageiro: str,
        documento_passageiro: str,
        tipo_documento_passageiro: str,
        telefone: str | None = None,
        data_nascimento: str | None = None,
    ):
        # TODO - Implementar idFormaPagamento e formaPagamento
        response = await self._client.post(
            "/api-gateway/confirmavenda/confirmarVenda",
            json={
                "transacao": transacao,
                "nomePassageiro": nome_passageiro,
                "documentoPassageiro": documento_passageiro,
                "tipoDocumentoPassageiro": tipo_documento_passageiro,
                "telefone": telefone,
                "dataNascimento": data_nascimento,
            },
        )
        response.raise_for_status()
        data = response.json()
        # TODO: type this response (create ConfirmarVendaResponse dataclass)
        return data

    async def cancelar_venda(
        self, transacao: str, validar_multa: bool = False
    ) -> CancelarVendaResponse | DevolverVendaResponse:
        """
        Cancela uma venda ou devolve com multa, dependendo do parâmetro fornecido.

        transacao : str
            Chave de bloqueio da poltrona associada à venda.
        validar_multa : bool, optional
            Define o comportamento do cancelamento:
            - False (padrão): cancela a venda sem multa.
            - True: devolve o valor com aplicação de multa.
        """

        response = await self._client.post(
            "/api-gateway/cancelavenda/cancelarVenda",
            json={
                "transacao": transacao,
                "validarMulta": validar_multa,
            },
        )

        if (
            response.status_code == HTTPStatus.BAD_REQUEST
            and response.json().get("message")
            == "O tempo permitido para cancelamento de bilhetes desta agência já foi ultrapassado."
        ):
            raise TicketCancellationPeriodExpired(
                "O tempo permitido para cancelamento de bilhetes desta agência já foi ultrapassado."
            )
        if (
            response.status_code == HTTPStatus.BAD_REQUEST
            and response.json().get("message") == "O bilhete já foi cancelado."
        ):
            raise TicketAlreadyCancelled("Ticket already cancelled", response.json())

        response.raise_for_status()

        if validar_multa:
            response = DevolverVendaResponse(**response.json())
        else:
            response = CancelarVendaResponse(**response.json())

        if not response.status:
            raise TotalBusClientError("Venda não cancelada", asdict(response))

        return response


class TotalBusClientError(RuntimeError):
    def __init__(self, message: str, response: dict):
        self.message = message
        self.response = response
        super().__init__(message)
