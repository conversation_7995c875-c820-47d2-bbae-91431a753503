import asyncio
from dataclasses import asdict
from datetime import timedelta
from typing import cast

from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)

from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import OTAConfig, Pax
from marketplace.otas.exception import (
    OTACircuitOpenError,
    OTAConnectionError,
    OTATimeoutException,
    TicketCancellationPeriodExpired,
)
from marketplace.otas.retry import NullRetry
from marketplace.otas.totalbus.client import TotalbusClient
from marketplace.tickets import IssuedTicket, RefreshedTicket, Ticket, TicketIssuer


class TotalbusTicketIssuer(TicketIssuer):
    def __init__(self, ota_config: OTAConfig):
        super().__init__(ota_config)
        self._session = TotalbusClient(
            base_url=cast(str, ota_config.config["base_url"]),
            username=cast(str, ota_config.config["username"]),
            password=cast(str, ota_config.config["password"]),
            tenant_id=cast(str, ota_config.config["tenant_id"]),
            log_prefix=self.ota_config.name,
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    async def _call(self, func, *args, retry_policy=None, **kwargs):
        retry_policy = retry_policy or self.retry_policy
        try:
            return await self._circuit_breaker.call(retry_policy(func), *args, **kwargs)
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    async def issue_ticket(self, ticket: Ticket) -> IssuedTicket:
        if ticket.seat_block_key is None:
            raise ValueError("ticket.seat_block_key is None")
        assert isinstance(ticket.seat_block_key, str)

        pax_doc = None
        pax_doc_type = None

        if ticket.pax_cpf:
            pax_doc = ticket.pax_cpf
            pax_doc_type = "CPF"
        elif ticket.pax_doc == Pax.DocType.PASSPORT:
            pax_doc = ticket.pax_doc
            pax_doc_type = "PASSAPORTE"
        else:
            pax_doc = ticket.pax_doc
            pax_doc_type = "RG"

        response = await self._call(
            self._session.confirmar_venda,
            retry_policy=NullRetry(),  # Emissão não é idempotente na TotalBus
            transacao=ticket.seat_block_key,
            nome_passageiro=ticket.pax_name,
            documento_passageiro=pax_doc,
            tipo_documento_passageiro=pax_doc_type,
            telefone=ticket.pax_phone,
            data_nascimento=ticket.pax_birthdate.isoformat()
            if ticket.pax_birthdate
            else None,
        )
        return IssuedTicket(
            localizador=None,
            numero_pedido=None,
            numero_bilhete=None,
            numero_bpe=None,
            chave_bpe=None,
            serie_bpe=None,
            protocolo_autorizacao=None,
            data_autorizacao=None,
            nome_agencia=None,
            emissao_em_contigencia=None,
            bpe_qrcode=None,
            monitriip_code=None,
            numero_embarque_code=None,
            embarque_code=None,
            tipo_embarque=None,
            preco=None,
            preco_pedagio=None,
            preco_taxa_embarque=None,
            preco_seguro=None,
            preco_desconto=None,
            preco_total=None,
            outros_tributos=None,
            poltrona=None,
            plataforma=None,
            linha=None,
            prefixo=None,
            servico=None,
            cnpj=None,
            endereco_empresa=None,
            extra=response,
        )

    async def cancel_ticket(self, ticket: Ticket) -> dict:
        if ticket.seat_block_key is None:
            raise ValueError("ticket.seat_block_key is None")
        assert isinstance(ticket.seat_block_key, str)

        try:
            response = await self._call(
                self._session.cancelar_venda, transacao=ticket.seat_block_key
            )
            return asdict(response)
        except TicketCancellationPeriodExpired:
            response = await self._call(
                self._session.cancelar_venda,
                transacao=ticket.seat_block_key,
                validar_multa=True,
            )
            return asdict(response)

    async def refresh_ticket(self, ticket: Ticket) -> RefreshedTicket:
        raise NotImplementedError

    async def refresh_ticket_from_extra(self, extra: dict) -> RefreshedTicket:
        raise NotImplementedError
