import asyncio
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from decimal import Decimal
from typing import cast, override

from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTABenefit,
    OTABenefitStatus,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    SeatMap,
    Stopover,
    Travel,
)
from marketplace.otas.exception import (
    IncompleteExtra,
    OTACircuitOpenError,
    OTAConnectionError,
    OTATimeoutException,
    TravelUnavailable,
)
from marketplace.otas.totalbus import commons
from marketplace.otas.totalbus.client import (
    BloquearPoltronaResponse,
    BuscaOnibusResponse,
    ItinerarioResponse,
    OTASectionalNotOffered,
    SearchResponse,
    Servico,
    TotalbusClient,
)
from marketplace.searcher import OTASearcher


@dataclass
class TotalbusTravelExtra:
    origem: int
    destino: int
    dataCorrida: str
    servico: int
    classe: str | None = None
    saida: str | None = None

    @classmethod
    def for_seat_map(cls, extra: dict) -> "TotalbusTravelExtra":
        try:
            return TotalbusTravelExtra(
                origem=extra["origem"],
                destino=extra["destino"],
                dataCorrida=extra["dataCorrida"],
                servico=extra["servico"],
                classe=extra.get("classe"),
                saida=extra.get("saida"),
            )
        except KeyError as error:
            raise IncompleteExtra(f"Missing extra field {error}") from error


class TotalbusSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        username: str,
        password: str,
        tenant_id: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._session = TotalbusClient(
            base_url=base_url,
            username=username,
            password=password,
            tenant_id=tenant_id,
            log_prefix=self.ota_config.name,
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
                ignored_exceptions=(OTASectionalNotOffered,),
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    @property
    def company_id(self):
        return int(self.ota_config.config["company_id"])

    async def _call(self, func, *args, **kwargs):
        try:
            return await self._circuit_breaker.call(
                self.retry_policy(func), *args, **kwargs
            )
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        search_response, last_synced, _ = await self._search_travels(
            departure_date=departure_date,
            origem_id=origin.extra["id"],
            destino_id=destination.extra["id"],
        )
        search_response = cast(SearchResponse, search_response)

        if search_response is None:
            return []

        found = []
        for servico in search_response.lsServicos:
            if servico.vende is False:
                continue

            seat_type = self._get_ota_seat_type_by_name(servico.classe)
            company = self._get_ota_company(servico.empresaId, name=servico.empresa)
            if seat_type is None or company is None:
                continue

            travel = Travel(
                ota="totalbus",
                ota_config_id=self.ota_config.id,
                code=servico.identificadorViagem,
                itinerary_class_code=f"{servico.servico}",
                group_initial_date=datetime.fromisoformat(servico.dataCorrida).date(),
                service_code=servico.servico,
                company=company,
                itinerary=[],
                origin=cast(Place, origin.place),
                destination=cast(Place, destination.place),
                departure_at=datetime.fromisoformat(servico.saida),
                arrival_at=datetime.fromisoformat(servico.chegada),
                seat_type=seat_type,
                price=servico.preco,
                available_seats=servico.poltronasLivres,
                total_seats=servico.poltronasTotal,
                last_synced=last_synced,
                extra=asdict(
                    TotalbusTravelExtra(
                        origem=servico.grupoOrigemId,
                        destino=servico.grupoDestinoId,
                        dataCorrida=servico.dataCorrida,
                        servico=int(servico.servico),
                        classe=servico.classe,
                        saida=servico.saida,
                    )
                ),
            )
            if servico.conexao:
                if (
                    servico.conexao.second_leg_departure_at
                    - servico.conexao.first_leg_arrival_at
                ) > timedelta(minutes=10):
                    # TODO implements connections
                    continue
                stopovers = self._build_stopovers(servico)
                if not stopovers:
                    continue
                travel.stopovers = self._build_stopovers(servico)
            found.append(travel)

        return found

    def _build_stopovers(self, servico: Servico) -> list[Stopover] | None:
        assert servico.conexao
        seat_type_second_leg = self._get_ota_seat_type_by_name(
            servico.conexao.segundoTrechoClasse
        )
        company_second_leg = self._get_ota_company(
            servico.conexao.segundoTrechoEmpresaId
        )
        if not seat_type_second_leg or not company_second_leg:
            return None
        return [
            Stopover(
                origin_ota_place_id=servico.conexao.primeiroTrechoOrigem,
                destination_ota_place_id=servico.conexao.primeiroTrechoDestino,
                departure_at=servico.conexao.first_leg_departure_at,
                extra=asdict(
                    TotalbusTravelExtra(
                        origem=servico.conexao.primeiroTrechoOrigem,
                        destino=servico.conexao.primeiroTrechoDestino,
                        dataCorrida=datetime.strptime(
                            servico.conexao.primeiroTrechoDataCorrida, "%d/%m/%Y"
                        )
                        .date()
                        .isoformat(),
                        servico=int(servico.conexao.primeiroTrechoServico),
                        classe=servico.conexao.primeiroTrechoClasse,
                        saida=servico.conexao.first_leg_departure_at.isoformat(),
                    )
                ),
            ),
            Stopover(
                origin_ota_place_id=servico.conexao.segundoTrechoOrigem,
                destination_ota_place_id=servico.conexao.segundoTrechoDestino,
                departure_at=servico.conexao.second_leg_departure_at,
                extra=asdict(
                    TotalbusTravelExtra(
                        origem=servico.conexao.segundoTrechoOrigem,
                        destino=servico.conexao.segundoTrechoDestino,
                        dataCorrida=datetime.strptime(
                            servico.conexao.segundoTrechoDataCorrida, "%d/%m/%Y"
                        )
                        .date()
                        .isoformat(),
                        servico=int(servico.conexao.segundoTrechoServico),
                        classe=servico.conexao.segundoTrechoClasse,
                        saida=servico.conexao.second_leg_departure_at.isoformat(),
                    )
                ),
            ),
        ]

    @override
    async def available_seats(
        self, travel: InputTravel, search_for_price: bool = True
    ) -> SeatMap:
        travel_extra = TotalbusTravelExtra.for_seat_map(travel.extra)
        assert travel.departure_at
        if search_for_price:
            response_poltronas, response_corrida = await asyncio.gather(
                self._seating_map(
                    travel_extra.origem,
                    travel_extra.destino,
                    datetime.fromisoformat(travel_extra.dataCorrida).date(),
                    travel_extra.servico,
                ),
                self._search_travel_by_code(
                    departure_at=datetime.fromisoformat(travel.departure_at),
                    origem_id=travel_extra.origem,
                    destino_id=travel_extra.destino,
                    servico=travel_extra.servico,
                    classe=travel_extra.classe,
                ),
            )
            response_corrida = cast(Servico, response_corrida)
            if not response_poltronas.classeServico:
                return SeatMap(seats=[], base_price=response_corrida.preco)
        else:
            response_poltronas = await self._seating_map(
                travel_extra.origem,
                travel_extra.destino,
                datetime.fromisoformat(travel_extra.dataCorrida).date(),
                travel_extra.servico,
            )
            response_corrida = None

        seats = []
        seat_type = self._get_ota_seat_type_by_name(response_poltronas.classeServico)
        for poltrona in response_poltronas.mapaPoltrona:
            benefit_type, description = None, None
            if poltrona.categoriaReservadaId == -1:
                # TODO - Precisa consultar em outro endpoint
                # Se realmente tem vagas para esse serviço categoria/consultarCategoriasCorrida
                # https://rjdesk.rjconsultores.com.br/rjdesk/front/ticket.form.php?id=88622
                benefits = self._get_available_benefits()
            else:
                benefit = self._get_benefit(
                    str(poltrona.categoriaReservadaId),
                    description,
                    benefit_type,
                )
                benefits = [benefit] if benefit else []

            if not benefits:
                continue
            seats.append(
                Seat(
                    number=poltrona.numero,
                    floor=1,
                    row=int(poltrona.x),
                    column=int(poltrona.y) + 1,
                    available=poltrona.disponivel,
                    price=response_corrida.preco if response_corrida else None,
                    category=response_poltronas.classeServico,
                    benefits=benefits,
                    seat_type=seat_type.seat_type if seat_type else None,
                    extra={
                        "categoriaReservadaId": poltrona.categoriaReservadaId,
                    },
                )
            )
        return SeatMap(
            seats=seats, base_price=response_corrida.preco if response_corrida else None
        )

    @override
    async def block_seat(
        self, travel: InputTravel, seat: Seat, benefit: Benefit = Benefit.NORMAL
    ) -> BlockedSeat:
        categoriaId = commons.get_categoria_id(self.ota_config, benefit)

        response = await self._call(
            self._session.bloquear_poltrona,
            origem=travel.extra["origem"],
            destino=travel.extra["destino"],
            data=datetime.fromisoformat(travel.extra["dataCorrida"]).date(),
            servico=travel.extra["servico"],
            poltrona=seat.number,
            categoriaId=categoriaId,
        )
        seat_type = self._get_ota_seat_type_by_name(seat.category)

        return BlockedSeat(
            number=response.assento,
            available=True,
            category=seat.category,
            seat_type=seat_type.seat_type if seat_type else None,
            price=self._seat_total_price(response),
            floor=seat.floor,
            row=seat.row,
            column=seat.column,
            extra={
                **asdict(response.preco),
            },
            block_key=response.transacao,
            best_before=datetime.now(timezone.utc)
            + timedelta(minutes=response.duracao),
            benefit=benefit,
            extra_rodoviaria=[
                {
                    "transacao": response.transacao,
                    "preco": self._seat_total_price(response),
                    "preco_conexao": None,
                    "origem": response.origem.cidade,
                    "destino": response.destino.cidade,
                    "data_hora_partida": response.dataSaida,
                    "is_conexao": False,
                }
            ],
        )

    @override
    async def unblock_seat(
        self, travel: InputTravel, blocked_seat: BlockedSeat
    ) -> bool:
        assert isinstance(blocked_seat.block_key, str)
        return await self._call(
            self._session.desbloquear_poltrona,
            transacao=blocked_seat.block_key,
        )

    @override
    async def list_places(self) -> list[OTAPlace]:
        places = set()
        for empresa in await self._call(self._session.consultar_empresas):
            places |= await self.list_company_places(empresa.id)
        return list(places)

    @override
    async def list_benefits(self) -> set[OTABenefit]:
        benefits = await self._call(self._session.consultar_beneficios)
        result_benefits = []
        for category in benefits:
            existing_benefit = self.ota_config.benefits_mapping.get(
                category.categoriaId
            )
            _type = (
                existing_benefit.type
                if existing_benefit
                else Benefit.NORMAL
                if category.desccategoria == "NORMAL"
                else None
            )
            _status = (
                existing_benefit.status
                if existing_benefit
                else OTABenefitStatus.AVAILABLE
            )

            result_benefits.append(
                OTABenefit(
                    ota_config_id=self.ota_config.id,
                    description=category.desccategoria,
                    external_code=str(category.categoriaId),
                    status=_status,
                    type=_type,
                    created_at=existing_benefit.created_at
                    if existing_benefit
                    else None,
                    updated_at=existing_benefit.updated_at
                    if existing_benefit
                    else None,
                )
            )
        return set(result_benefits)

    async def list_company_places(self, company_id: int) -> set[OTAPlace]:
        response = await self._call(self._session.buscar_origem_destino, company_id)

        places = set()
        for item in response:
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=item.origem.cidade,
                    extra=item.origem,
                )
            )
            for destination in item.destinos:
                places.add(
                    OTAPlace(
                        ota_config_id=self.ota_config.id,
                        name=destination.cidade,
                        extra=destination,
                    )
                )
        return places

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        itinerary: list[Checkpoint] = []
        ota_itinerary: ItinerarioResponse = await self._busca_itinerario_corrida(
            travel.extra["servico"], travel.extra["dataCorrida"]
        )
        post_origin_checkpoint = False
        last_distance = 0
        for parada in ota_itinerary.lsParadas:
            post_origin_checkpoint = (
                post_origin_checkpoint or parada.localidade.id == travel.extra["origem"]
            )
            if post_origin_checkpoint:
                itinerary.append(
                    Checkpoint(
                        name=parada.localidade.cidade_com_uf,
                        departure_at=datetime.fromisoformat(parada.data_hora_partida),
                        distance_km=last_distance,
                    )
                )
            last_distance = float(parada.distancia)
            if parada.localidade.id == travel.extra["destino"]:
                break
        return itinerary

    @memoize(cache, ttl=3600, cache_key=lambda self: f"places:{self.ota_config.id}")
    async def _places(self) -> dict[str, OTAPlace]:
        return {place.name: place for place in await self.list_places()}

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        departure_date,
        origem_id,
        destino_id: f"search:{self.ota_config.id}:{departure_date}:{origem_id}:{destino_id}:v2",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
    )
    async def _search_travels(
        self,
        departure_date: date,
        origem_id: int,
        destino_id: int,
    ) -> tuple[SearchResponse | None, datetime | None, int]:
        try:
            search_response = await self._call(
                self._session.buscar_corrida,
                data=departure_date,
                origem=origem_id,
                destino=destino_id,
            )
            last_synced = datetime.now(timezone.utc)
            travels = search_response.lsServicos
            cache_ttl = await self._dynamic_search_ttl(
                travels, origem_id, destino_id, departure_date
            )
            return search_response, last_synced, cache_ttl
        except OTASectionalNotOffered:
            return (
                None,
                datetime.now(timezone.utc),
                self.ota_config.search_cache.ttl_not_available,
            )

    @memoize(
        cache,
        ttl=lambda result,
        self,
        *args,
        **kwargs: self.ota_config.search_cache.seat_mapping_ttl,  # pyright: ignore
        cache_key=lambda self,
        departure_at,
        origem_id,
        destino_id,
        servico,
        classe: f"search:price:{self.ota_config.id}:{departure_at}:{origem_id}:{destino_id}:{servico}:{classe}",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: float("inf"),
    )
    async def _search_travel_by_code(
        self,
        departure_at: datetime,
        origem_id: int,
        destino_id: int,
        servico: int,
        classe: str,
    ) -> Servico:
        # OTA não retorna data com timezone. Garante comparação.
        departure_at = departure_at.replace(tzinfo=None)
        search_response = await self._call(
            self._session.buscar_corrida,
            data=departure_at.date(),
            origem=origem_id,
            destino=destino_id,
        )

        found_travel = None
        for travel in search_response.lsServicos:
            if int(travel.servico) == servico:
                found_travel = travel
                break

        if not found_travel:
            raise TravelUnavailable(
                f"Not found any travel with the code {servico} at {departure_at.date()}"
            )
        if found_travel.classe != classe:
            raise TravelUnavailable(
                f"The travel found for the {servico} at {departure_at.date()} has incompatible class"
            )
        if self._get_ota_seat_type_by_name(found_travel.classe) is None:
            raise TravelUnavailable(
                f"The travel found for the {servico} at {departure_at.date()} has incompatible class"
            )
        if datetime.fromisoformat(found_travel.saida) != departure_at:
            raise TravelUnavailable(
                f"The travel found for the {servico} at {departure_at.date()} has incompatible departure time"
            )
        return found_travel

    def _has_few_seats_travel(self, search_response: list[Servico]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for servico in search_response:
            if servico.vende is True and servico.poltronasLivres < threshold:
                return True

        return False

    @memoize(
        cache,
        ttl=45,
        cache_key=lambda self,
        origem,
        destino,
        data,
        servico: f"search:seating_map:{self.ota_config.id}:{origem}:{destino}:{data}:{servico}",
        lock=lock,
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(
        self, origem: int, destino: int, data: date, servico: int
    ) -> BuscaOnibusResponse:
        seating_map = await self._call(
            self._session.buscar_onibus_detalhado,
            origem=origem,
            destino=destino,
            data=data,
            servico=servico,
        )
        return seating_map

    def _seat_total_price(self, response: BloquearPoltronaResponse) -> float:
        return float(
            Decimal(response.preco.outros)
            + Decimal(response.preco.pedagio)
            + Decimal(response.preco.seguro)
            + Decimal(response.preco.preco)
            + Decimal(response.preco.taxaEmbarque)
            + Decimal(response.preco.seguroW2I)
        )

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self,
        servico,
        data_corrida: f"itinerary:{self.ota_config.id}:{servico}:{data_corrida}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _busca_itinerario_corrida(
        self, servico: int, data_corrida: str
    ) -> ItinerarioResponse:
        return await self._call(
            self._session.busca_itinerario_corrida,
            servico=servico,
            data_corrida=data_corrida,
        )
