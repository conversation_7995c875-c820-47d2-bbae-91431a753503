import asyncio
import itertools
import logging
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from typing import Optional, cast, override

from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Benefit,
    BlockedSeat,
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    SeatMap,
    Travel,
    from_dict,
)
from marketplace.otas.eulabs.client import (
    BlockSeatRequest,
    EulabsClient,
    SeatingMap,
    Tariff,
    TravelLeg,
    TravelSummary,
)
from marketplace.otas.eulabs.client import (
    Travel as EulabsTravel,
)
from marketplace.otas.exception import (
    IncompleteExtra,
    OTACircuitOpenError,
    OTAConnectionError,
    OTASearcherTravelNotFound,
    OTATimeoutException,
)
from marketplace.searcher import OTASearcher

logger = logging.getLogger(__name__)


@dataclass
class EulabsTravelExtra:
    key: str
    id: int
    origin_id: int
    destination_id: int
    category_description: str
    class_info_long_name: str | None = None
    datetime_departure: str | None = None

    @classmethod
    def for_seat_map(cls, extra: dict) -> "EulabsTravelExtra":
        try:
            return EulabsTravelExtra(
                key=extra["key"],
                id=extra["id"],
                origin_id=extra["origin_id"],
                destination_id=extra["destination_id"],
                category_description=extra["category_description"],
            )
        except KeyError as error:
            raise IncompleteExtra(f"Missing extra field {error}") from error


@dataclass
class EulabsTravelInfo:
    datetime_departure: str
    category_description: str
    amount: float


@dataclass
class ExtraConfigEulabs:
    use_promotional_price: Optional[bool] = False

    def __init__(self, **kwargs):
        # Atribui apenas os campos que são definidos no dataclass
        for key, value in kwargs.items():
            if key in self.__dataclass_fields__:
                setattr(self, key, value)


class EulabsSearcher(OTASearcher):
    BENEFIT_MAP = {
        "deficient": Benefit.DISABLED,
        "young": Benefit.YOUNG_100,
        "partial_young": Benefit.YOUNG_50,
        "elderly": Benefit.ELDERLY_100,
        "partial_elderly": Benefit.ELDERLY_50,
    }

    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        api_id: str,
        api_key: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self.extra_config = ExtraConfigEulabs(**kwargs)
        self._session = EulabsClient(
            base_url=base_url,
            api_id=api_id,
            api_key=api_key,
            log_prefix=self.ota_config.name,
        )
        self._cache = cache

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    async def _call(self, func, *args, **kwargs):
        try:
            return await self._circuit_breaker.call(
                self.retry_policy(func), *args, **kwargs
            )
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        travels, last_synced, _ = await self._search_travels(
            departure_date=departure_date,
            origin_sectional_id=origin.extra["id"],
            destiny_sectional_id=destination.extra["id"],
        )
        travels = cast(list[EulabsTravel], travels)

        found = []
        for travel in travels:
            if not travel.items:
                continue

            for item in travel.items:
                if item.tariffs is None:
                    continue

                company = self._get_ota_company(item.company.id, name=item.company.name)
                if company is None:
                    continue

                for tariff in item.tariffs:
                    seat_type = self._get_ota_seat_type_by_name(
                        tariff.category.description
                    )
                    if seat_type is None:
                        continue

                    found.append(
                        Travel(
                            ota="eulabs",
                            ota_config_id=self.ota_config.id,
                            code=f"{item.id}_{tariff.category.short_description}",
                            class_code=f"{tariff.category.short_description}",
                            group_code=str(item.id),
                            service_code=str(item.id),
                            company=company,
                            itinerary=[],
                            origin=cast(Place, origin.place),
                            destination=cast(Place, destination.place),
                            departure_at=datetime.fromisoformat(
                                travel.datetime_departure
                            ),
                            arrival_at=datetime.fromisoformat(travel.datetime_arrival),
                            seat_type=seat_type,
                            price=self._get_price(tariff),
                            available_seats=tariff.category.free_seats,
                            total_seats=tariff.category.final_seat
                            - tariff.category.initial_seat
                            + 1,
                            last_synced=last_synced,
                            extra=asdict(
                                EulabsTravelExtra(
                                    category_description=tariff.category.description,
                                    class_info_long_name=item.class_info.long_name,
                                    key=travel.key,
                                    id=item.id,
                                    origin_id=origin.extra["id"],
                                    destination_id=destination.extra["id"],
                                    datetime_departure=travel.datetime_departure,
                                )
                            ),
                        )
                    )

        return found

    def _has_few_seats_travel(self, search_response: list[EulabsTravel]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for travel in search_response:
            if not travel.items:
                continue

            for item in travel.items:
                if item.tariffs is None:
                    continue

                for tariff in item.tariffs:
                    if (
                        tariff.category.free_seats
                        and tariff.category.free_seats < threshold
                    ):
                        return True

        return False

    async def list_places(self) -> list[OTAPlace]:
        sectionals = await self._call(self._session.sectionals)
        return [
            OTAPlace(
                ota_config_id=self.ota_config.id,
                name=f"{s.description} {s.uf_acronym}",
                extra=s,
            )
            for s in sectionals
        ]

    def _get_price(self, tariff: Tariff):
        if (
            self.extra_config.use_promotional_price
            and tariff.price_promotional
            and tariff.price_promotional > 0
        ):
            return tariff.price_promotional
        return tariff.amount

    @memoize(cache, ttl=3600, cache_key=lambda self: f"places:{self.ota_config.id}")
    async def _sectionals_by_description(self):
        return {
            f"{s.description} {s.uf_acronym}": s
            for s in await self._call(self._session.sectionals)
        }

    @override
    async def travel_price(self, travel: Travel) -> float | None:
        found_travel, _ = await self._get_travels_from_search(travel)
        if found_travel is None or found_travel.items is None:
            return None

        for item in found_travel.items:
            if item.tariffs is None:
                continue
            for tariff in item.tariffs:
                if tariff.category.description == travel.seat_type:
                    return tariff.amount

        return None

    @override
    async def available_seats(
        self, travel: InputTravel, search_for_price: bool = True
    ) -> SeatMap:
        travel_extra = EulabsTravelExtra.for_seat_map(travel.extra)
        assert travel.departure_at and travel.seat_type
        price, seating_maps = await self._get_price_and_seating_map(
            travel_extra, travel.departure_at, travel.seat_type
        )

        seats = []
        for seating_map in seating_maps:
            for seat_info, floor in itertools.chain(
                zip(seating_map.floor_1, itertools.repeat(1, len(seating_map.floor_1))),
                zip(
                    seating_map.floor_2 or [],
                    itertools.repeat(2, len(seating_map.floor_2 or [])),
                ),
            ):
                benefits = [
                    category
                    for benefit in seat_info.benefits_values
                    if (
                        category := self._get_benefit(
                            benefit.type,
                            benefit.type,
                            self.BENEFIT_MAP.get(benefit.type),
                        )
                    )
                ] + [Benefit.NORMAL]
                seat_type = self._get_ota_seat_type_by_name(seat_info.category)
                seats.append(
                    Seat(
                        number=str(seat_info.number),
                        floor=floor,
                        row=seat_info.line,
                        column=seat_info.column + 1
                        if seat_info.column > 2
                        else seat_info.column,
                        available=not seat_info.busy,
                        category=seat_info.category,
                        seat_type=seat_type.seat_type if seat_type else None,
                        price=seat_info.amount,
                        benefits=benefits,
                        extra={
                            "tariff": seat_info.tariff,
                            "aditional_amount": seat_info.aditional_amount,
                            "price_discount": seat_info.price_discount,
                            "woman_space": seat_info.woman_space,
                            "beneffits_values": [
                                asdict(v) for v in seat_info.benefits_values
                            ],
                        },
                    )
                )

        return SeatMap(seats=seats, base_price=price)

    def _get_block_seat_benefit_type(self, benefit: Benefit) -> BlockSeatRequest.Type:
        if benefit == Benefit.NORMAL:
            return BlockSeatRequest.Type.INTERNET
        ota_benefit = self.ota_config.get_benefit(benefit)
        type = ota_benefit.external_code if ota_benefit else None
        if not type:
            raise ValueError(f"Benefit {benefit} is not mapped.")
        return BlockSeatRequest.Type(type)

    @override
    async def block_seat(
        self, travel: InputTravel, seat: Seat, benefit: Benefit = Benefit.NORMAL
    ) -> BlockedSeat:
        benefit_type = self._get_block_seat_benefit_type(benefit)

        response = await self._call(
            self._session.block_seats,
            travel_key=travel.extra["key"],
            block_seats=[BlockSeatRequest(seat=int(seat.number), type=benefit_type)],
        )
        best_before = datetime.now(timezone.utc) + timedelta(
            seconds=self.ota_config.search_cache.ttl_block_seat
        )
        return BlockedSeat(
            **asdict(seat),
            block_key=response.selected_seat_Key,
            best_before=best_before,
            benefit=benefit,
            extra_rodoviaria=response.selected_seat_Key,
        )

    @override
    async def unblock_seat(
        self, travel: InputTravel, blocked_seat: BlockedSeat
    ) -> bool:
        if blocked_seat.block_key is None:
            raise ValueError("BlockedSeat does not have a `block_key` defined.")
        assert isinstance(blocked_seat.block_key, str)

        await self._call(
            self._session.unblock_seats,
            travel_key=travel.extra["key"],
            seat_key=blocked_seat.block_key,
        )
        return True

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        itinerary: list[Checkpoint] = []
        last_total_km = 0
        post_origin_checkpoint = False
        for travel_leg in await self._travel_summary(travel.extra["id"]):
            post_origin_checkpoint = (
                post_origin_checkpoint
                or travel_leg.seccional_id == travel.extra["origin_id"]
            )
            if post_origin_checkpoint:
                itinerary.append(
                    Checkpoint(
                        name=travel_leg.seccional_name,
                        departure_at=datetime.fromisoformat(travel_leg.local_exit),
                        distance_km=(
                            0
                            if travel_leg.seccional_id == travel.extra["origin_id"]
                            else round(travel_leg.total_km - last_total_km, 2)
                        ),
                    )
                )
                if travel_leg.seccional_id == travel.extra["destination_id"]:
                    break
                last_total_km = travel_leg.total_km
        return itinerary

    async def _get_travels_from_search(
        self, travel: Travel
    ) -> tuple[EulabsTravel, datetime] | tuple[None, None]:
        travel_summary = from_dict(TravelSummary, travel.extra["summary"])
        origin = from_dict(TravelLeg, travel.extra["origin"])
        destination = from_dict(TravelLeg, travel.extra["destination"])

        search_results, last_synced = await self._search_travels(
            departure_date=travel.departure_at.date(),
            origin_sectional_id=origin.seccional_id,
            destiny_sectional_id=destination.seccional_id,
        )

        for search_result in search_results:
            for item in search_result.items:
                if item.id == travel_summary.id:
                    return search_result, last_synced

        return None, None

    async def _get_price_and_seating_map(
        self, travel_extra: EulabsTravelExtra, departure_at: str, seat_type: str
    ) -> tuple[float | None, list[SeatingMap]]:
        if self.extra_config.use_promotional_price:
            seating_map: list[SeatingMap] = await self._seating_map(travel_extra.key)
            return min(
                [
                    s.amount
                    for s in seating_map[0].seats
                    if s.category == travel_extra.category_description
                ],
                default=None,
            ), seating_map

        # TODO: remove when price by seat is implemented
        seating_map, travel_response = await asyncio.gather(
            self._seating_map(travel_extra.key),
            self._search_travel_by_code(
                datetime.fromisoformat(departure_at),
                travel_extra.origin_id,
                travel_extra.destination_id,
                travel_extra.id,
                travel_extra.category_description,
                seat_type,
            ),
        )
        travel_response = cast(EulabsTravelInfo, travel_response)
        return travel_response.amount, seating_map

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        departure_date,
        origin_sectional_id,
        destiny_sectional_id: f"search:{self.ota_config.id}:{departure_date}:{origin_sectional_id}:{destiny_sectional_id}:v3",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self,
        departure_date: date,
        origin_sectional_id: int,
        destiny_sectional_id: int,
    ) -> tuple[list[EulabsTravel] | None, datetime | None, int]:
        eulabs_travels = await self._call(
            self._session.travels_search,
            departure_date=departure_date,
            origin_sectional_id=origin_sectional_id,
            destiny_sectional_id=destiny_sectional_id,
        )
        last_synced = datetime.now(timezone.utc)
        cache_ttl = await self._dynamic_search_ttl(
            eulabs_travels,
            origin_sectional_id,
            destiny_sectional_id,
            departure_date,
        )
        return eulabs_travels, last_synced, cache_ttl

    @memoize(
        cache,
        ttl=lambda result,
        self,
        *args,
        **kwargs: self.ota_config.search_cache.seat_mapping_ttl,  # pyright: ignore
        cache_key=lambda self,
        departure_at,
        origin_sectional_id,
        destiny_sectional_id,
        travel_id,
        category_description,
        seat_type: (
            f"search:price:{self.ota_config.id}:{departure_at}:{origin_sectional_id}:"
            f"{destiny_sectional_id}:{travel_id}:{category_description}:{seat_type}"
        ),
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: float("inf"),
    )
    async def _search_travel_by_code(
        self,
        departure_at: datetime,
        origin_sectional_id: int,
        destiny_sectional_id: int,
        travel_id: int,
        category_description: str,
        seat_type: str,
    ) -> EulabsTravelInfo:
        # OTA não retorna data com timezone. Garante comparação.
        departure_at = departure_at.replace(tzinfo=None)
        eulabs_travels = await self._call(
            self._session.travels_search,
            departure_date=departure_at.date(),
            origin_sectional_id=origin_sectional_id,
            destiny_sectional_id=destiny_sectional_id,
        )
        found_travel = None
        for travel in eulabs_travels:
            if not travel.items:
                continue
            for item in travel.items:
                if item.id != travel_id:
                    continue
                if item.tariffs is None:
                    continue
                for tariff in item.tariffs:
                    ota_seat_type = self._get_ota_seat_type_by_name(
                        tariff.category.description
                    )
                    if ota_seat_type and ota_seat_type.seat_type == seat_type:
                        found_travel = EulabsTravelInfo(
                            datetime_departure=travel.datetime_departure,
                            category_description=tariff.category.description,
                            amount=tariff.amount,
                        )

        if not found_travel:
            raise OTASearcherTravelNotFound(
                f"Not found any travel with the code {travel_id} and {category_description}"
            )

        if departure_at != datetime.fromisoformat(found_travel.datetime_departure):
            logger.warning(
                "[Eulabs][OTAConfig=%s] Travel %s and %s has incompatible departure time "
                "(expected departure_at %s, got %s)",
                self.ota_config.id,
                travel_id,
                category_description,
                departure_at,
                found_travel.datetime_departure,
            )
            raise OTASearcherTravelNotFound(
                f"The travel found for the {travel_id} and {category_description} has incompatible departure time"
            )
        return found_travel

    @memoize(
        cache,
        ttl=45,
        cache_key=lambda self, travel_key: f"map:{self.ota_config.id}:{travel_key}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(self, travel_key: str) -> list[SeatingMap]:
        return await self._call(self._session.seating_map, travel_key=travel_key)

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self, travel_id: f"itinerary:{self.ota_config.id}:{travel_id}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _travel_summary(self, travel_id: int) -> list[TravelLeg]:
        return await self._call(
            self._session.get_road_travel_summary, travel_id=travel_id
        )
