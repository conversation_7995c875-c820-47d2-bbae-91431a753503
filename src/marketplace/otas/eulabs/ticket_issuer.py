import asyncio
from dataclasses import asdict
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import cast

from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)

from marketplace.circuit_breaker import (
    <PERSON>Breaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Benefit,
    DisabledBenefit,
    ElderlyBenefit,
    OTAConfig,
    Pax,
    PaxBenefit,
    YoungBenefit,
)
from marketplace.otas.eulabs.client import (
    EulabsClient,
    EulabsClientBPENotFound,
    SaleBenefitDeficientRequest,
    SaleBenefitElderlyRequest,
    SaleBenefitYoungRequest,
    SaleItemRequest,
    SaleItemRoadRequest,
    SaleResponse,
    SaleUtilizerRequest,
)
from marketplace.otas.exception import (
    OTACircuitOpenError,
    OTAConnectionError,
    OTATimeoutException,
)
from marketplace.otas.retry import NullRetry
from marketplace.qrcode import qrcode_data_extractor
from marketplace.tickets import IssuedTicket, Refreshed<PERSON>icket, Ticket, TicketIssuer

MAP_DOC_TYPE = {
    Pax.DocType.RG: SaleUtilizerRequest.DocType.RG,
    Pax.DocType.PASSPORT: SaleUtilizerRequest.DocType.PASSPORT,
}

MAP_BENEFIT_TYPE = {
    Benefit.ELDERLY_100: SaleBenefitElderlyRequest.Type.ELDERLY,
    Benefit.ELDERLY_50: SaleBenefitElderlyRequest.Type.PARTIAL_ELDERLY,
    Benefit.YOUNG_100: SaleBenefitYoungRequest.Type.YOUNG,
    Benefit.YOUNG_50: SaleBenefitYoungRequest.Type.PARTIAL_YOUNG,
}

MAP_JURISDICTION = {
    DisabledBenefit.Jurisdiction.FEDERAL: SaleBenefitDeficientRequest.Jurisdiction.FEDERAL,
    DisabledBenefit.Jurisdiction.ESTADUAL: SaleBenefitDeficientRequest.Jurisdiction.ESTADUAL,
}


class EulabsTicketIssuer(TicketIssuer):
    def __init__(self, ota_config: OTAConfig):
        super().__init__(ota_config)
        self._session = EulabsClient(
            base_url=cast(str, ota_config.config["base_url"]),
            api_id=cast(str, ota_config.config["api_id"]),
            api_key=cast(str, ota_config.config["api_key"]),
            log_prefix=self.ota_config.name,
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_exception_type((OTATimeoutException, OTAConnectionError)),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    async def _call(self, func, *args, retry_policy=None, **kwargs):
        retry_policy = retry_policy or self.retry_policy
        try:
            return await self._circuit_breaker.call(retry_policy(func), *args, **kwargs)
        except CircuitOpenError as error:
            raise OTACircuitOpenError from error

    def map_benefit(
        self, benefit: PaxBenefit | None
    ) -> (
        SaleBenefitDeficientRequest
        | SaleBenefitElderlyRequest
        | SaleBenefitYoungRequest
        | None
    ):
        if not benefit:
            return None

        if isinstance(benefit, ElderlyBenefit):
            return SaleBenefitElderlyRequest(
                type=MAP_BENEFIT_TYPE[benefit.type],
                income=float(benefit.monthly_income),
                benefit_expiration=benefit.expiration_date,
            )

        if isinstance(benefit, YoungBenefit):
            return SaleBenefitYoungRequest(
                type=MAP_BENEFIT_TYPE[benefit.type],
                benefit_number=benefit.number,
                benefit_issue_date=benefit.issue_date,
                benefit_expiration=benefit.expiration_date,
            )

        if isinstance(benefit, DisabledBenefit):
            return SaleBenefitDeficientRequest(
                benefit_number=benefit.number,
                benefit_expiration=benefit.expiration_date,
                validity_begin=benefit.issue_date,
                jurisdiction=MAP_JURISDICTION[benefit.jurisdiction],
                boarding_aid=benefit.require_boarding_assistance,
                boarding_aid_type=SaleBenefitDeficientRequest.BoardingAidType.CADEIRANTE
                if benefit.require_boarding_assistance
                else None,
            )

    async def issue_ticket(self, ticket: Ticket) -> IssuedTicket:
        if not ticket.seat_block_key:
            raise ValueError("ticket.seat_block_key is None")
        assert isinstance(ticket.seat_block_key, str)

        first_name = ticket.pax_name.split(" ")[0]
        last_name = ticket.pax_name.split(" ")[-1]

        doc_type = MAP_DOC_TYPE[ticket.pax_doc_type]

        benefit = self.map_benefit(ticket.pax_benefit)

        gender = None
        if benefit:
            gender = SaleUtilizerRequest.Gender.MALE

        response: SaleResponse = await self._call(
            self._session.post_sales,
            retry_policy=NullRetry(),  # Emissão não é idempotente na Eulabs
            sales=[
                SaleItemRequest(
                    road=SaleItemRoadRequest(
                        selected_seat_key=ticket.seat_block_key,
                        utilizer=SaleUtilizerRequest(
                            first_name=first_name,
                            last_name=last_name,
                            full_name=ticket.pax_name,
                            document_type=doc_type,
                            document_number=ticket.pax_doc,
                            cpf=ticket.pax_cpf,
                            phone=ticket.pax_phone,
                            birth=ticket.pax_birthdate,
                            benefit=benefit,
                            gender=gender,
                        ),
                    )
                )
            ],
        )
        sale_item = response.items[0]
        return IssuedTicket(
            localizador=sale_item.localizer,
            numero_pedido=str(response.sale_id),
            numero_bilhete=None,
            numero_bpe=None,
            chave_bpe=None,
            serie_bpe=None,
            protocolo_autorizacao=None,
            data_autorizacao=None,
            nome_agencia=None,
            emissao_em_contigencia=None,
            bpe_qrcode=None,
            monitriip_code=None,
            numero_embarque_code=None,
            embarque_code=None,
            tipo_embarque=None,
            preco=sale_item.amount,
            preco_pedagio=None,
            preco_taxa_embarque=None,
            preco_seguro=None,
            preco_desconto=None,
            preco_total=None,
            outros_tributos=None,
            poltrona=str(sale_item.seat),
            plataforma=None,
            linha=None,
            prefixo=None,
            servico=None,
            cnpj=None,
            endereco_empresa=None,
            extra=asdict(response),
        )

    async def cancel_ticket(self, ticket: Ticket) -> dict:
        try:
            issued_ticket = ticket.issued_ticket
            assert issued_ticket.extra
            key = issued_ticket.extra["items"][0]["key"]
        except (AssertionError, KeyError) as ex:
            raise ValueError("ticket issue result is invalid") from ex

        cancel_conditions = await self._call(self._session.get_cancel_conditions, key)
        response = await self._call(
            self._session.post_cancel_sale, cancel_conditions.key
        )

        return asdict(response) | asdict(cancel_conditions)

    async def refresh_ticket(self, ticket: Ticket) -> RefreshedTicket:
        assert ticket.issued_ticket.extra, "issued ticket does not have extra"
        return await self.refresh_ticket_from_extra(ticket.issued_ticket.extra)

    async def refresh_ticket_from_extra(self, extra: dict) -> RefreshedTicket:
        async def _get_bpe_info(sale_id: int, item_key: str):
            try:
                return await self._session.get_bpe_info(sale_id, item_key)
            except EulabsClientBPENotFound:
                return None

        try:
            sale_id = extra["sale_id"]

            if extra.get("item"):
                # Rodoviária, apagar quando a emissão estiver aqui grrr
                item_key = extra["item"]["key"]
            else:
                item_key = extra["items"][0]["key"]
        except KeyError:
            raise ValueError(f"ticket issue result is invalid {extra}")

        sales_info, bpe_info = await asyncio.gather(
            self._call(self._session.get_sales_info, sale_id),
            self._call(_get_bpe_info, sale_id, item_key),
        )

        chave_bpe = bpe_info.items[0].bpe.access_key if bpe_info else ""
        numero_bpe = bpe_info.items[0].numero_bpe if bpe_info else ""
        serie_bpe = bpe_info.items[0].serie_bpe if bpe_info else ""
        bpe_qrcode = (
            qrcode_data_extractor(bpe_info.items[0].bpe.qrcode) if bpe_info else ""
        )

        linha = sales_info.items[0].road.line.description
        prefixo = sales_info.items[0].road.line.prefix
        outros_tributos = sales_info.items[0].road.tribute.to_str()
        embarque_code = sales_info.items[0].road.embarque_eletronico_str
        numero_embarque_code = sales_info.items[
            0
        ].road.electronic_boarding.TicketGate.number
        preco_taxa_embarque = Decimal(str(sales_info.items[0].boarding_fee))
        preco_seguro = Decimal(str(sales_info.items[0].insurance))
        preco_pedagio = Decimal(str(sales_info.items[0].toll))

        return RefreshedTicket(
            chave_bpe=chave_bpe,
            numero_bpe=numero_bpe,
            serie_bpe=serie_bpe,
            bpe_qrcode=bpe_qrcode,
            linha=linha,
            prefixo=prefixo,
            outros_tributos=outros_tributos,
            embarque_code=embarque_code,
            numero_embarque_code=numero_embarque_code,
            preco_taxa_embarque=preco_taxa_embarque,
            preco_seguro=preco_seguro,
            preco_pedagio=preco_pedagio,
        )
