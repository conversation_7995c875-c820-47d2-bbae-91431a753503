import json
from dataclasses import asdict, dataclass, field
from datetime import date
from enum import StrEnum
from http import HTTPStatus
from typing import Optional

import dacite
import httpx

from marketplace.otas.exception import (
    OTAConnectionError,
    OTAIssuerInvalidParameters,
    OTAIssuerInvalidResponse,
    OTATooManyRequests,
    SeatUnavailable,
    TravelUnavailable,
    TravelUnavailableForBenefit,
)
from marketplace.otas.http import AsyncClient


class EulabsClientBPENotFound(Exception):
    pass


@dataclass
class TravelSummary:
    id: int
    line_code: str
    departure_date: str
    departure_time: str
    description: str
    company_id: int
    schedule_id: int


@dataclass
class Sectional:
    code: str
    description: str
    id: int
    uf_acronym: str


@dataclass
class TravelLeg:
    arrival_zone: str
    departure_time_zone: str
    local_arrival_date_time: str
    local_exit: str
    seccional_code: str
    seccional_id: int
    seccional_name: str
    stop_time: str
    total_time: str
    total_km: float
    uf_acronym: str


@dataclass
class TravelDetail:
    origin_id: int
    origin_name: str
    destination_id: int
    destination_name: str
    class_code: str
    seats: int
    capacity: int
    class_description: str


@dataclass
class ClassCategory:
    seat_map_id: int
    vehicle_type_id: int
    description: str
    short_description: str
    category_sat: str
    initial_seat: int
    final_seat: int
    free_seats: int


@dataclass
class Tariff:
    tariff: float
    insurance: float
    toll: float
    boarding_fee: float
    ferry: float
    additional: float
    calculation: float
    referential: float
    resource_discount: float
    total_km: float
    price_promotional: float
    amount: float
    category: ClassCategory


@dataclass
class Company:
    id: int
    code: str
    name: str
    logo: str


@dataclass
class ClassInfo:
    id: int
    long_name: str
    short_name: str


@dataclass
class Item:
    id: int
    road_item_id: int
    gateway_id: int
    gateway_type: str
    station_id_origin: int
    station_id_destiny: int
    service_travel: str
    reference_travel: str
    datetime_departure: str
    datetime_arrival: str
    duration: str
    free_seats: int
    tariff: float
    insurance: float
    fee: float
    travel_item_toll: float
    price: float
    price_promotional: float
    tax: float
    class_info: ClassInfo
    company: Company
    line_code: str
    direction: str
    vehicle_id: int
    tariffs: Optional[list[Tariff]]


@dataclass
class FreeSeatBenefit:
    type: str
    free: int
    amount: int


@dataclass
class Travel:
    key: str
    id: int
    origin_sectional_id: int
    destiny_sectional_id: int
    datetime_departure: str
    datetime_arrival: str
    duration: str
    price: float
    price_promotional: float
    free_seats: int
    free_seats_benefits: list[FreeSeatBenefit] | None
    items: Optional[list[Item]]


@dataclass
class BenefitValue:
    type: str
    tarrif: float
    price_discount: float
    amount: float


@dataclass
class Seat:
    number: int
    line: int
    column: int
    busy: bool
    category: str
    tariff: float
    amount: float
    aditional_amount: float
    price_discount: float
    woman_space: bool
    benefits_values: list[BenefitValue]


@dataclass
class SeatingMap:
    floor_1: list[Seat]
    floor_2: Optional[list[Seat]]
    free_seats: int

    @property
    def seats(self):
        if self.floor_2:
            return self.floor_1 + self.floor_2
        return self.floor_1


@dataclass
class BlockSeatRequest:
    class Type(StrEnum):
        INTERNET = "internet"
        ELDERLY = "elderly"
        PARTIAL_ELDERLY = "partial_elderly"
        YOUNG = "young"
        PARTIAL_YOUNG = "partial_young"
        DEFICIENT = "deficient"

    seat: int
    type: Type = Type.INTERNET
    required_companion: bool = field(init=False, default=False)


@dataclass
class BlockSeatResponse:
    selected_seat_Key: str


@dataclass
class SaleBenefitDeficientRequest:
    class BoardingAidType(StrEnum):
        CADEIRANTE = "cadeirante"
        OUTROS = "outros"

    class Jurisdiction(StrEnum):
        ESTADUAL = "estadual"
        FEDERAL = "federal"

    benefit_number: str
    benefit_expiration: date
    boarding_aid: bool
    boarding_aid_type: BoardingAidType | None  # required if boarding_aid is True
    validity_begin: date
    jurisdiction: Jurisdiction
    type: str = field(init=False, default="deficient")
    required_companion: bool = field(init=False, default=False)


@dataclass
class SaleBenefitElderlyRequest:
    class Type(StrEnum):
        ELDERLY = "elderly"
        PARTIAL_ELDERLY = "partial_elderly"

    type: Type
    income: float
    benefit_expiration: date
    benefit_number: str | None = None
    benefit_type_doc: str | None = None
    benefit_issuer: str | None = None
    benefit_institution: str | None = None
    benefit_issue_date: date | None = None


@dataclass
class SaleBenefitYoungRequest:
    class Type(StrEnum):
        YOUNG = "young"
        PARTIAL_YOUNG = "partial_young"

    benefit_number: str
    benefit_issue_date: date
    benefit_expiration: date
    type: Type
    enabled: bool = field(init=False, default=True)


@dataclass
class SaleUtilizerRequest:
    class Gender(StrEnum):
        MALE = "m"
        FEMALE = "f"

    class DocType(StrEnum):
        RG = "RG"
        CPF = "CPF"
        CNH = "CNH"
        PASSPORT = "Passaporte"
        CTPS = "CTPS"

    first_name: str
    last_name: str
    full_name: str | None
    document_type: DocType
    document_number: str

    cpf: str | None
    phone: str | None
    birth: date | None
    gender: Gender | None = None
    benefit: (
        SaleBenefitDeficientRequest
        | SaleBenefitElderlyRequest
        | SaleBenefitYoungRequest
        | None
    ) = None


@dataclass
class SaleItemRoadRequest:
    selected_seat_key: str
    utilizer: SaleUtilizerRequest
    with_return: bool = field(init=False, default=True)


@dataclass
class SaleItemRequest:
    road: SaleItemRoadRequest


@dataclass
class SaleUtilizerResponse:
    first_name: str
    last_name: str
    full_name: str
    document_type: str
    document_number: str
    cpf: str
    phone: str | None
    birth: str  # YYYY-MM-DD


@dataclass
class SaleBoardingLandingResponse:
    id: int
    name: str
    uf: str


@dataclass
class SaleStatusResponse:
    id: int
    sale_item_type: str
    sale_item_id: int
    status: str
    created_at: str  # "YYYY-MM-DD HH:MM:SS"


@dataclass
class SaleItemResponse:
    seat: int
    key: str
    localizer: str
    amount: float
    utilizer: SaleUtilizerResponse
    boarding: SaleBoardingLandingResponse
    landing: SaleBoardingLandingResponse
    statuses: list[SaleStatusResponse]


@dataclass
class SaleInfoBPE:
    id: int
    contingency: str
    access_key: str
    protocol: str
    authorization: str
    public_url: str
    qrcode: str


@dataclass
class SaleInfoNFSE:
    id: int


@dataclass
class SaleInfoCompanyIssued:
    acronym_state: str


@dataclass
class SaleInfoState:
    acronym: str


@dataclass
class SaleInfoLocality:
    name: str
    state: SaleInfoState


@dataclass
class SaleInfoBoardingLandingResponse:
    id: int
    code: str
    name: str
    locality: SaleInfoLocality


@dataclass
class SaleInfoCompany:
    id: int
    code: str
    usual_name: str
    logo: str


@dataclass
class SaleInfoLine:
    code: str
    prefix: str
    description: str
    federal_code: str
    federal_description: str


@dataclass
class SaleInfoTribute:
    icms_percentage: float
    icms_value: float
    other_taxes_percentage: float
    other_taxes_value: float

    def to_str(self):
        return (
            f"ICMS {self.icms_value} ({self.icms_percentage}%) OUTROS TRIB:"
            f" {self.other_taxes_value} ({self.other_taxes_percentage}%)"
        )


@dataclass
class SaleInfoStateAddress:
    acronym: str
    icsm_percentage: str


@dataclass
class SalesInfoLocalityAddress:
    name: str
    ibge_code: str


@dataclass
class SalesInfoVehicle:
    bpe_code: str
    bpe_description: str


@dataclass
class Address:
    public_place: str
    number: str
    neighborhood: str
    cep: str
    complement: str
    locality: SalesInfoLocalityAddress


@dataclass
class CentralizingCompany:
    name: str
    cnpj: str
    state_registration: str
    municipal_registration: str
    address: Address


@dataclass
class TicketGate:
    type: str
    number: str
    type_tariff: str
    image: str


@dataclass
class ElectronicBoarding:
    qrcode_bpe: str
    TicketGate: TicketGate


@dataclass
class SaleInfoUtilizer:
    first_name: str
    last_name: str
    full_name: str
    document_type: str
    document_number: str
    birth: str


@dataclass
class Road:
    travel_item_id: int
    ticket_item_id: int
    travel_item_service_travel: str
    datetime_start: str
    datetime_departure: str
    datetime_arrival: str
    duration: str
    eticket_number: str
    seat_number: int
    direction: str
    status: str
    company_issued: SaleInfoCompanyIssued
    boarding: SaleInfoBoardingLandingResponse
    landing: SaleInfoBoardingLandingResponse
    company: SaleInfoCompany
    line: SaleInfoLine
    tribute: SaleInfoTribute
    centralizing_company: CentralizingCompany
    vehicle: SalesInfoVehicle
    electronic_boarding: ElectronicBoarding

    @property
    def embarque_eletronico_str(self) -> str:
        embarque_eletronico = (
            "eucaturmobile/boarding?type=manual"
            f"&identification={self.ticket_item_id}"
            f"&code={self.boarding.code}"
            f"&line={self.line.code or self.line.federal_code}"
            f"&departure_travel={self.datetime_arrival}"
            f"&departure={self.datetime_start}"
            f"&direction={'Ida' if self.direction == 1 else 'Volta'}"
        )
        if self.boarding.locality.state.acronym == "RO":
            embarque_eletronico += (
                "&msg=GERAR BPE ATE O EMBARQUE, ANTES DO INICIO DA PRESTAÇAO DO SERVIÇO "
                "(SEÇAO V, ANEXO XIII, ART 23 $1º, RICMS/RO-DECR.22721/18). "
                "APÓS EMBARQUE VER BPE NO APP C/MOTORISTA"
            )

        return embarque_eletronico


@dataclass
class SaleInfoComponent:
    id: int
    sale_item_type: str
    sale_item_id: int
    type: str
    value: float


@dataclass
class SaleInfoStatus:
    id: int
    sale_item_type: str
    sale_item_id: int
    status: str
    created_at: str


@dataclass
class SaleInfoItemResponse:
    key: str
    sale_id: int
    localizer: str
    transaction: str
    created_at: str
    bpe: SaleInfoBPE
    nfse: SaleInfoNFSE
    road: Road
    amount: float
    utilizers: list[SaleInfoUtilizer]
    components: list[SaleInfoComponent] | None
    statuses: list[SaleInfoStatus] | None

    @property
    def serie_bpe(self) -> str:
        return self.localizer.split("-")[2]

    @property
    def numero_bpe(self) -> str:
        return self.localizer.split("-")[3]

    @property
    def boarding_fee(self) -> float:
        if not self.components:
            return 0
        return next(
            (comp.value for comp in self.components if comp.type == "boarding_fee"), 0
        )

    @property
    def insurance(self) -> float:
        if not self.components:
            return 0
        return next(
            (comp.value for comp in self.components if comp.type == "insurance"), 0
        )

    @property
    def toll(self) -> float:
        if not self.components:
            return 0
        return next((comp.value for comp in self.components if comp.type == "toll"), 0)


@dataclass
class SaleResponse:
    sale_id: int
    amount: float
    items: list[SaleItemResponse]


@dataclass
class SaleInfoResponse:
    id: int
    sectional_id: int
    sectional_cod: str
    sectional_name: str
    person_id: int
    person_client_id: int
    person_salesman_id: int
    user_id: int
    operation_date: str
    type: str
    system: str
    confirmed: bool
    sectional_request_id: int
    person_request_id: int
    created_at: str
    amount: float
    items: list[SaleInfoItemResponse]


@dataclass
class CancelConditionResponse:
    fine_amount: float
    refund_amount: float
    status: str
    type: list[str]
    key: str
    allow_refund: bool
    amount: float
    availability_digital_wallet: bool


@dataclass
class CancelSaleResponse:
    type_this_refund: str


@dataclass
class BPEResponse:
    id: int
    sectional_id: int
    sectional_cod: str
    sectional_name: str
    person_id: int
    person_client_id: int
    person_salesman_id: int
    user_id: int
    operation_date: str
    type: str
    system: str
    confirmed: bool
    sectional_request_id: int
    person_request_id: int
    created_at: str
    amount: float
    items: list[SaleInfoItemResponse]


def date_to_isoformat_factory(data):
    def convert_value(value):
        if isinstance(value, (date)):
            return value.isoformat()
        return value

    return {k: convert_value(v) for k, v in data}


async def handle_response(response: httpx.Response):
    if response.status_code == HTTPStatus.SERVICE_UNAVAILABLE:
        raise OTATooManyRequests()
    if response.status_code == HTTPStatus.BAD_GATEWAY:
        raise OTAConnectionError()


class EulabsClient:
    def __init__(
        self,
        base_url: str,
        api_id: str,
        api_key: str,
        timeout: int = 20,
        log_prefix: Optional[str] = None,
    ):
        log_prefix = (
            f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__
        )
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={
                "X-Eucatur-Api-Id": api_id,
                "X-Eucatur-Api-Key": api_key,
            },
            log_prefix=log_prefix,
            event_hooks={
                "response": [handle_response],
            },
        )

    async def travels_search(
        self, departure_date: date, origin_sectional_id: int, destiny_sectional_id: int
    ) -> list[Travel]:
        response = await self._client.get(
            "/road/travels/search",
            params={
                "departure_date": departure_date.isoformat(),
                "origin_sectional_id": origin_sectional_id,
                "destiny_sectional_id": destiny_sectional_id,
            },
        )

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            msg = payload["message"]
            if "error reading from server" in msg or "bad connection" in msg:
                raise OTAConnectionError

        response.raise_for_status()

        payload = response.json()

        for travel in payload:
            for item in travel.get("items") or []:
                item["class_info"] = item.pop("class")

        return [dacite.from_dict(Travel, item) for item in payload]

    async def sectionals(
        self,
        id: Optional[int] = None,
        code: Optional[str] = None,
        is_road_station: bool = False,
        locality_id: Optional[int] = None,
    ):
        response = await self._client.get(
            "/sectionals",
            params={
                "id": id,
                "code": code,
                "is_road_station": is_road_station,
                "locality_id": locality_id,
            },
        )
        response.raise_for_status()
        payload = response.json()
        return [dacite.from_dict(Sectional, item) for item in payload]

    async def seating_map(self, travel_key: str):
        response = await self._client.get(f"/road/travels/{travel_key}/seating-map")

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            msg = payload["message"]
            if "Viagem informada é inválida" in msg:
                raise TravelUnavailable
            if "não foi possível realizar a marcação solicitada" in msg:
                raise TravelUnavailable
            if "Table 'sge.proc_constarifa' doesn't exist" in msg:
                raise OTAConnectionError

        response.raise_for_status()
        payload = response.json()
        return [dacite.from_dict(SeatingMap, item) for item in payload]

    async def block_seats(
        self, travel_key: str, block_seats: list[BlockSeatRequest]
    ) -> BlockSeatResponse:
        response = await self._client.post(
            f"/road/travels/{travel_key}/seats",
            json={"items": [asdict(block_seat) for block_seat in block_seats]},
        )

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            if any(
                msg in payload["message"]
                for msg in [
                    "está sendo marcada.",
                    "essa poltrona está indisponível no momento",
                    "não está disponível",
                ]
            ):
                raise SeatUnavailable

            if (
                "todas as vagas disponíveis para esse benefício já foram preenchidas"
                in payload["message"]
            ):
                raise TravelUnavailableForBenefit
            if "Viagem informada é inválida" in payload["message"]:
                raise TravelUnavailable

        response.raise_for_status()
        return BlockSeatResponse(**response.json())

    async def unblock_seats(self, travel_key: str, seat_key: str):
        response = await self._client.delete(
            f"/road/travels/{travel_key}/selected_seats/{seat_key}"
        )

        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            if "Viagem informada é inválida" in payload["message"]:
                raise TravelUnavailable

        response.raise_for_status()

    async def list_road_travels_summary(
        self, initial_departure_date: date, final_departure_date: date
    ) -> list[TravelSummary]:
        response = await self._client.get(
            "/road/travels/summary",
            params={
                "initial_departure_date": initial_departure_date.isoformat(),
                "final_departure_date": final_departure_date.isoformat(),
            },
        )
        response.raise_for_status()

        payload = response.json()
        return [TravelSummary(**item) for item in payload]

    async def get_road_travel_summary(self, travel_id: int) -> list[TravelLeg]:
        response = await self._client.get(f"/road/travels/summary/{travel_id}")
        response.raise_for_status()

        payload = response.json()
        return [TravelLeg(**item) for item in payload]

    async def get_road_travel_detail(self, travel_id: int) -> list[TravelDetail]:
        response = await self._client.get(f"/road/travels/detail/{travel_id}")
        response.raise_for_status()

        payload = response.json()
        return [TravelDetail(class_code=item.pop("class"), **item) for item in payload]

    async def post_sales(self, sales: list[SaleItemRequest]) -> SaleResponse:
        response = await self._client.post(
            "/sales",
            params={"device_type": "desktop"},
            json={
                "items": [
                    asdict(sale, dict_factory=date_to_isoformat_factory)
                    for sale in sales
                ]
            },
        )

        if response.status_code == 422:
            response = response.json()
            raise OTAIssuerInvalidParameters(response)

        response.raise_for_status()
        try:
            response_json = response.json()
        except json.JSONDecodeError:
            raise OTAIssuerInvalidResponse(
                response={"message": "Not JSON", "response": response.text}
            )
        try:
            return dacite.from_dict(SaleResponse, response_json)
        except dacite.DaciteError as ex:
            raise OTAIssuerInvalidResponse(response=response_json) from ex

    async def get_cancel_conditions(self, ticket_key: str) -> CancelConditionResponse:
        response = await self._client.get(f"/sales/cancel_conditions/{ticket_key}")
        response.raise_for_status()
        return dacite.from_dict(CancelConditionResponse, response.json())

    async def post_cancel_sale(self, travel_cancel_key: str) -> CancelSaleResponse:
        response = await self._client.post(
            f"/sales/cancel_item/{travel_cancel_key}/Cancel"
        )
        response.raise_for_status()
        return dacite.from_dict(CancelSaleResponse, response.json())

    async def get_sales_info(self, sale_id: int) -> SaleInfoResponse:
        response = await self._client.get(f"/sales/find/{sale_id}")
        response.raise_for_status()
        return dacite.from_dict(SaleInfoResponse, response.json())

    async def get_bpe_info(self, sale_id: int, item_key: str) -> BPEResponse:
        response = await self._client.get(
            f"/sales/{sale_id}/items/{item_key}/request_bpe"
        )

        if response.status_code == 422:
            raise EulabsClientBPENotFound(
                f"BPE not found for sale_id {sale_id} and item_key {item_key}"
            )

        response.raise_for_status()
        return dacite.from_dict(BPEResponse, response.json())
