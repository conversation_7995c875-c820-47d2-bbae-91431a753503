from decimal import Decimal
from typing import Any


class OTAConnectionError(ConnectionError): ...


class OTATooManyRequests(OTAConnectionError): ...


class OTACircuitOpenError(OTATooManyRequests): ...


class OTATimeoutException(OTAConnectionError): ...


class OTASearcherException(Exception): ...


class OTAInvalidParameters(Exception): ...


class OTAIssuerInvalidParameters(Exception):
    def __init__(self, response: dict):
        self.response = response


class OTAIssuerInvalidResponse(Exception):
    def __init__(self, response: Any):
        self.response = response


class OTANotJSONResponse(Exception): ...


class OTASearcherTravelNotFound(OTASearcherException, Exception): ...


class IncompleteExtra(Exception): ...


class TicketAlreadyCancelled(Exception):
    def __init__(self, message: str, response: dict):
        super().__init__(message)
        self.response = response


class TicketCancellationPeriodExpired(Exception): ...


class SeatUnavailable(OTASearcherException): ...


class SeatNotBlocked(OTASearcherException): ...


class SeatNotUnblocked(OTASearcherException): ...


class SeatAlreadyUnblocked(OTASearcherException): ...


class TravelUnavailable(OTASearcherException): ...


class TravelUnavailableForBenefit(OTASearcherException): ...


class ItineraryNotFound(OTASearcherException): ...


class IncorrectPrice(OTASearcherException):
    def __init__(self, expected_price: Decimal, got_price: Decimal):
        super().__init__(f"Expected price {expected_price}, got {got_price}")
