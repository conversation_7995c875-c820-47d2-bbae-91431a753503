from glob import glob
from os import path
from typing import Any, cast

import aiosql
import asyncpg
import orjson

from marketplace.settings import DATABASE_URL

queries = cast(Any, aiosql.from_path("./src/queries", "asyncpg"))


async def _init_conn(conn: asyncpg.Connection):
    await conn.set_type_codec(
        "jsonb",
        schema="pg_catalog",
        encoder=lambda v: orjson.dumps(v).decode(),
        decoder=lambda v: orjson.loads(v),
    )
    await conn.set_builtin_type_codec("hstore", codec_name="pg_contrib.hstore")


def create_connection_pool(dsn, init_conn=_init_conn):
    return asyncpg.create_pool(dsn, min_size=2, max_size=10, init=_init_conn)


MIGRATIONS_DIR = "./migrations"


def list_available_migrations() -> list[str]:
    return list(
        sorted(
            [
                path.splitext(path.basename(p))[0]
                for p in glob(path.join(MIGRATIONS_DIR, "*.sql"))
            ]
        )
    )


async def list_applied_migrations() -> list[str]:
    async with create_connection_pool(DATABASE_URL, init_conn=None) as pool:
        async with pool.acquire() as conn:
            await create_schema_migrations_table_if_not_exists(conn)
            rows = await conn.fetch("select name from schema_migrations order by name")
            return [row["name"] for row in rows]


async def apply_migration(name: str, fake: bool = False):
    with open(path.join(MIGRATIONS_DIR, f"{name}.sql")) as sql_file:
        async with create_connection_pool(DATABASE_URL, init_conn=None) as pool:
            async with pool.acquire() as conn:
                await create_schema_migrations_table_if_not_exists(conn)
                async with conn.transaction():
                    if not fake:
                        await conn.execute(sql_file.read())
                    await conn.execute(
                        "insert into schema_migrations (name) values ($1)", name
                    )


async def create_schema_migrations_table_if_not_exists(conn: asyncpg.Connection):
    await conn.execute(
        """create table if not exists schema_migrations (
            name text,
            created_at timestamp default now(),
            primary key (name)
)"""
    )


# https://peps.python.org/pep-0562/
def __getattr__(name):
    if name == "db_pool":
        return create_connection_pool(DATABASE_URL)

    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")
