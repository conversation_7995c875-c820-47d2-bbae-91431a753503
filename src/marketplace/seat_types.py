import logging
from dataclasses import dataclass
from typing import override

from marketplace import database
from marketplace.models import OTAConfig, OTASeatTypeStatus
from marketplace.worker import WORKE<PERSON>, QueuedTask, QueuedWorker

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class CreateSeatTask(QueuedTask):
    ota_config: OTAConfig
    name: str

    @override
    async def run(self, worker: Queued<PERSON>orker):
        async with worker.db_pool.acquire() as conn:
            await database.queries.insert_ota_seat_type(
                conn,
                ota_config_id=self.ota_config.id,
                name=self.name,
                status=OTASeatTypeStatus.NOT_LISTED,
            )


def create_seat_type(ota_config: OTAConfig, name: str):
    WORKER.put_nowait(CreateSeatTask(ota_config, name))
