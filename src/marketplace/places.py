import asyncio
from dataclasses import asdict
from itertools import groupby
from os import cpu_count

import asyncpg
import numpy as np
import rapidfuzz
from jarowinkler import jarowinkler_similarity
from slugify import slugify

from marketplace import database, get_searcher_from_ota_config, list_ota_configs
from marketplace.models import OTAConfig, OTAPlace, OTAPlaceStatus, Place, from_dict


async def list_ota_places_by_slugs(
    db_conn: asyncpg.Connection, slugs: list[str]
) -> dict[int, dict[str | None, list[OTAPlace]]]:
    slugs = [slug_to_ltree(s) for s in slugs]
    records = await database.queries.list_ota_places_by_slugs(db_conn, slugs=slugs)
    items = list(from_dict(OTAPlace, r) for r in records)
    items.sort(
        key=lambda x: (x.ota_config_id, x.place.slug if x.place else None)
    )  # sort to groupby
    return {
        key: {
            slug: list(iter2)
            for slug, iter2 in groupby(
                iter,
                key=lambda ota_place: ota_place.place.slug
                if ota_place.place is not None
                else None,
            )
        }
        for key, iter in groupby(items, key=lambda item: item.ota_config_id)
    }


async def insert_places(db_conn: asyncpg.Connection, places: list[Place]):
    await database.queries.insert_places(db_conn, [asdict(place) for place in places])


async def list_all_places(db_conn: asyncpg.Connection) -> list[Place]:
    return [
        from_dict(Place, record)
        for record in await database.queries.list_places(db_conn)
    ]


async def list_ota_places(
    db_conn: asyncpg.Connection, config_id: int
) -> list[OTAPlace]:
    return [
        from_dict(OTAPlace, record)
        for record in await database.queries.list_ota_config_places(
            db_conn,
            config_id=config_id,
        )
    ]


async def fetch_all_ota_places():
    background_tasks = set()

    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            ota_configs = await list_ota_configs(db_conn)

            for ota_config in ota_configs:
                task = asyncio.create_task(fetch_ota_places(ota_config))
                background_tasks.add(task)
                task.add_done_callback(background_tasks.discard)

            places = await asyncio.gather(*background_tasks)

    return [place for sublist in places for place in sublist]


async def fetch_ota_places(ota_config: OTAConfig) -> list[OTAPlace]:
    searcher = get_searcher_from_ota_config(ota_config)
    ota_places = await searcher.list_places()
    return ota_places


async def refresh_all_ota_places(db_conn: asyncpg.Connection):
    all_places = {
        ltree_to_slug(row["slug"]): row["subplaces"]
        for row in await database.queries.list_places_with_subplaces(db_conn)
    }

    current_ota_places = set()
    for ota_config in await list_ota_configs(db_conn):
        current_ota_places |= set(
            await list_ota_places(db_conn, config_id=ota_config.id)
        )
    fetched_ota_places = set(await fetch_all_ota_places())

    system_managed_ota_places = (
        fetched_ota_places
        & {p for p in current_ota_places if p.status == OTAPlaceStatus.SYSTEM}
    ) | (fetched_ota_places - current_ota_places)

    result = []
    for ota_place, place in find_matching_places(
        list(system_managed_ota_places), list(all_places.keys())
    ):
        found = place
        if place and all_places[place] and len(all_places[place]) == 1:
            found = all_places[place][0]
        result.append(
            {
                **asdict(ota_place),
                "place": slug_to_ltree(found) if found else None,
            }
        )

    await database.queries.insert_ota_config_places(db_conn, result)


async def refresh_ota_places(db_conn: asyncpg.Connection, ota_config: OTAConfig):
    all_places = {
        ltree_to_slug(row["slug"]): row["subplaces"]
        for row in await database.queries.list_places_with_subplaces(db_conn)
    }

    current_ota_places = set(await list_ota_places(db_conn, config_id=ota_config.id))
    fetched_ota_places = set(await fetch_ota_places(ota_config))

    system_managed_ota_places = (
        fetched_ota_places
        & {p for p in current_ota_places if p.status == OTAPlaceStatus.SYSTEM}
    ) | (fetched_ota_places - current_ota_places)

    result = []
    for ota_place, place in find_matching_places(
        list(system_managed_ota_places), list(all_places.keys())
    ):
        found = place
        if place and all_places[place] and len(all_places[place]) == 1:
            found = all_places[place][0]
        result.append(
            {
                **asdict(ota_place),
                "place": slug_to_ltree(found) if found else None,
            }
        )

    await database.queries.insert_ota_config_places(db_conn, result)


def find_matching_places(
    ota_places: list[OTAPlace],
    places: list[str],  # list of Place.slug's
    score_cutoff: float = 0.95,
    scorer=jarowinkler_similarity,
    preprocess=lambda s: slugify(s),
) -> list[tuple[OTAPlace, str | None]]:
    ota_places_names = [preprocess(p.name) for p in ota_places]

    cdist = rapidfuzz.process.cdist(
        ota_places_names,
        places,
        scorer=jarowinkler_similarity,
        workers=cpu_count() or 1,
    )

    argm = np.argmax(cdist, axis=1)

    return [
        (ota_place, places[f] if cdist[i][f] > score_cutoff else None)
        for i, (ota_place, f) in enumerate(zip(ota_places, argm))
    ]


def slug_to_ltree(s):
    return s.replace("-", "_")


def ltree_to_slug(s):
    return s.replace("_", "-")
