import uuid

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from marketplace.correlation_context.context import correlation_id_var


class CorrelationIdMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Pega o header ou gera um novo ID se não vier
        correlation_id = request.headers.get("X-Correlation-ID", str(uuid.uuid4()))

        # Define o valor no contexto atual
        token = correlation_id_var.set(correlation_id)
        try:
            response = await call_next(request)
            response.headers["X-Correlation-ID"] = correlation_id
            return response
        finally:
            correlation_id_var.reset(token)
