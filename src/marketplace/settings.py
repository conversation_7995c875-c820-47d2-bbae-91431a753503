import os

LOG_LEVEL = os.getenv("MARKETPLACE_LOG_LEVEL", "DEBUG")

DATABASE_URL = os.getenv(
    "DATABASE_URL", "postgres://marketplace:marketplace@localhost/marketplace"
)

REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_MAX_CONNECTIONS = int(os.getenv("REDIS_MAX_CONNECTIONS", "100"))

SENTRY_DSN = os.getenv("SENTRY_DSN")
SENTRY_TRACES_SAMPLE_RATE = float(os.getenv("SENTRY_TRACES_SAMPLE_RATE", "0.01"))
SENTRY_PROFILES_SAMPLE_RATE = float(os.getenv("SENTRY_PROFILES_SAMPLE_RATE", "0.01"))

TESTING = os.getenv("TESTING", "false") == "true"

HTTP_REQUESTS_OUT_LOG_LEVEL = os.getenv("HTTP_REQUESTS_OUT_LOG_LEVEL", LOG_LEVEL)
HTTP_REQUESTS_OUT_LOG_SAMPLE_RATE = float(
    os.getenv("HTTP_REQUESTS_OUT_LOG_SAMPLE_RATE", "1.0")
)
