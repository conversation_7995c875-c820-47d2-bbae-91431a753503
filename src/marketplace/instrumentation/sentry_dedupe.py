import sentry_sdk
from cachebox import TTLCache
from sentry_sdk.integrations import Integration
from sentry_sdk.scope import add_global_event_processor


class SentryDedupeIntegration(Integration):
    identifier = "sentry-dedup-integration"

    def __init__(self, *, ttl=30):
        self._seen = TTLCache(1_000, ttl=ttl)

    @staticmethod
    def setup_once():
        add_global_event_processor(SentryDedupeIntegration.processor)

    @classmethod
    def processor(cls, event, hint):
        if hint is None:
            return event

        exc_info = hint.get("exc_info")
        if exc_info is None:
            return event

        integration: SentryDedupeIntegration = sentry_sdk.Hub.current.get_integration(
            cls
        )
        key = integration._build_key(exc_info)
        if integration._seen.insert(key, 1) is None:
            return event

        return None

    def _build_key(self, exc_info):
        lines = self._get_exc_filenames_and_lines(exc_info)
        key = "|".join(f"{fname}:{lineno}" for fname, lineno in lines)
        return key

    def _get_exc_filenames_and_lines(self, exc_info):
        _, _, exc_tb = exc_info
        while exc_tb is not None:
            yield (exc_tb.tb_frame.f_code.co_filename, exc_tb.tb_lineno)
            exc_tb = exc_tb.tb_next
