from dataclasses import asdict
from http import HTTPStatus

import asyncpg
from dacite import DaciteError
from starlette.requests import Request

from marketplace import (
    get_searcher_from_ota_config_id,
)
from marketplace.models import (
    Benefit,
    BlockedSeat,
    InputTravel,
    InputTravelSeatMap,
    Seat,
    from_dict,
)
from marketplace.otas.exception import (
    IncompleteExtra,
    OTASearcherTravelNotFound,
    SeatNotBlocked,
    SeatNotUnblocked,
    SeatUnavailable,
    TravelUnavailable,
    TravelUnavailableForBenefit,
)
from marketplace.views import OrjsonResponse, bad_request_response


def travel_unavailable_response(message: str | None = None) -> OrjsonResponse:
    response_body = {"code": "TRAVEL_UNAVAILABLE"}
    if message:
        response_body["message"] = message
    return OrjsonResponse(
        response_body,
        status_code=HTTPStatus.GONE,
    )


async def travel_seating_map(request: Request):
    try:
        seat_map_input = from_dict(InputTravelSeatMap, await request.json())
    except (ValueError, DaciteError) as ex:
        return bad_request_response(str(ex))

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_ota_config_id(
            db_conn, seat_map_input.travel.ota_config_id
        )

    try:
        seat_map = await searcher.available_seats(
            travel=seat_map_input.travel,
            search_for_price=seat_map_input.search_for_price,
        )
    except IncompleteExtra as ex:
        return bad_request_response(str(ex))
    except ValueError as ex:
        return bad_request_response(str(ex))
    except TravelUnavailable:
        return travel_unavailable_response()
    except OTASearcherTravelNotFound as exc:
        return OrjsonResponse(
            {"code": "TRAVEL_NOT_FOUND", "message": str(exc)}, status_code=404
        )
    return OrjsonResponse(
        {
            "seats": [asdict(seat) for seat in seat_map.seats],
            "base_price": seat_map.base_price,
        }
    )


async def travel_block_seat(request: Request):
    try:
        payload = await request.json()
        travel = from_dict(InputTravel, payload["travel"])
        seat = from_dict(Seat, payload["seat"])
        benefit = Benefit.NORMAL
        if "benefit" in payload and payload["benefit"]:
            benefit = Benefit(payload["benefit"])
    except (ValueError, KeyError, DaciteError) as ex:
        return bad_request_response(str(ex))

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_ota_config_id(db_conn, travel.ota_config_id)
        try:
            seat = await searcher.block_seat(
                travel=travel,
                seat=seat,
                benefit=benefit,
            )
            return OrjsonResponse(asdict(seat))
        except ValueError as ex:
            return bad_request_response(str(ex))
        except AssertionError as ex:
            return bad_request_response(str(ex))
        except SeatUnavailable:
            return OrjsonResponse(
                {"code": "SEAT_UNAVAILABLE"},
                status_code=HTTPStatus.CONFLICT,
            )
        except TravelUnavailableForBenefit:
            return OrjsonResponse(
                {"code": "TRAVEL_UNAVAILABLE_FOR_BENEFIT"},
                status_code=HTTPStatus.GONE,
            )
        except SeatNotBlocked:
            return OrjsonResponse(
                {"code": "SEAT_NOT_BLOCKED"},
                status_code=HTTPStatus.BAD_REQUEST,
            )
        except TravelUnavailable:
            return travel_unavailable_response()


async def travel_unblock_seat(request: Request):
    payload = await request.json()

    travel = from_dict(InputTravel, payload["travel"])
    seat = from_dict(BlockedSeat, payload["blocked_seat"])

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_ota_config_id(db_conn, travel.ota_config_id)
        try:
            ok = await searcher.unblock_seat(travel=travel, blocked_seat=seat)
            return OrjsonResponse({"ok": ok})
        except ValueError as ex:
            return bad_request_response(str(ex))
        except TravelUnavailable:
            return travel_unavailable_response()
        except SeatNotUnblocked:
            return OrjsonResponse(
                {"code": "SEAT_NOT_UNBLOCKED"},
                status_code=HTTPStatus.BAD_REQUEST,
            )
