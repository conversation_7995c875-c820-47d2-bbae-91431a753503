from dataclasses import asdict

import asyncpg
from starlette.requests import Request

from marketplace import (
    database,
    get_ota_config_by_id,
    get_searcher_from_ota_config,
)
from marketplace.models import OTABenefit, OTAConfig
from marketplace.views import OrjsonResponse


async def refresh_benefits_from_ota(request: Request):
    config_id = int(request.path_params["config_id"])
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_config = await get_ota_config_by_id(db_conn, config_id)
        existing_benefits = set(ota_config.benefits or [])
        new_benefits = await fetch_ota_benefits(ota_config)

        to_create = new_benefits - existing_benefits
        await database.queries.insert_ota_benefits(
            db_conn, [asdict(c) for c in to_create]
        )
        return OrjsonResponse({"ok": True})


async def fetch_ota_benefits(
    ota_config: OTAConfig,
) -> set[OTABenefit]:
    searcher = get_searcher_from_ota_config(ota_config)
    return await searcher.list_benefits()


async def save_ota_benefit(request: Request):
    config_id = int(request.path_params["config_id"])
    data = await request.json()
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.update_ota_benefit(
            db_conn,
            ota_config_id=config_id,
            external_code=data["external_code"],
            status=data["status"],
            type=data["type"],
        )
        return OrjsonResponse(dict(record))
