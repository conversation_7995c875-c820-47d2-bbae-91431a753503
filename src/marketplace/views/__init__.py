from decimal import Decimal
from typing import Any

import orjson
from starlette.responses import JSONResponse


def bad_request_response(message: str | None = None) -> JSONResponse:
    return JSONResponse({"message": message or "Bad request."}, status_code=400)


class OrjsonResponse(JSONResponse):
    def render(self, content: Any) -> bytes:
        return orjson.dumps(content, default=self._default)

    def _default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        raise TypeError
