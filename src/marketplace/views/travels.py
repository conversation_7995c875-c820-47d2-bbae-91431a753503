from dataclasses import asdict

import asyncpg
from starlette.requests import Request

from marketplace import (
    database,
    get_searcher_from_ota_config_id,
)
from marketplace.models import (
    InputTravel,
    InputTravelItinerary,
    Travel,
    from_dict,
)
from marketplace.otas.exception import ItineraryNotFound
from marketplace.views import OrjsonResponse, bad_request_response


async def travel_price(request: Request):
    travel_code = request.path_params["travel_code"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.get_travel(db_conn, travel_code=travel_code)
        travel = from_dict(Travel, record)

        searcher = await get_searcher_from_ota_config_id(db_conn, travel.ota_config_id)
        price = await searcher.travel_price(travel=travel)
        return OrjsonResponse({"price": price})


async def travel_itinerary(request: Request):
    payload = await request.json()
    # TODO: remove this when buser_django requests inputs correctly
    if payload.get("travel"):
        input_travel_itinerary = from_dict(InputTravelItinerary, payload)
        travel = input_travel_itinerary.travel
    else:
        travel = from_dict(InputTravel, payload)

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_ota_config_id(db_conn, travel.ota_config_id)

    try:
        checkpoints = await searcher.travel_itinerary(travel=travel)
        return OrjsonResponse({"itinerary": [asdict(cp) for cp in checkpoints]})
    except (TypeError, KeyError) as ex:
        return bad_request_response(str(ex))
    except ItineraryNotFound as exc:
        return OrjsonResponse(
            {"code": "ITINERARY_NOT_FOUND", "message": str(exc)}, status_code=404
        )
