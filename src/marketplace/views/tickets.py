import logging
from dataclasses import asdict, dataclass

import asyncpg
from starlette.requests import Request

from marketplace import (
    get_ota_config_by_id,
    ticket_issuer,
    tickets,
)
from marketplace.models import (
    BlockedSeat,
    Pax,
    TicketTravel,
    from_dict,
)
from marketplace.otas.exception import (
    OTAIssuerInvalidParameters,
    OTAIssuerInvalidResponse,
    TicketAlreadyCancelled,
)
from marketplace.views import OrjsonResponse

logger = logging.getLogger(__name__)


@dataclass
class TicketRequest:
    client_code: str
    pax: Pax
    blocked_seat: BlockedSeat
    travel: TicketTravel
    price: float
    tags: dict[str, str]


@dataclass
class UpdateTicketRequest:
    pax: Pax | None = None
    blocked_seat: BlockedSeat | None = None
    travel: TicketTravel | None = None
    price: float | None = None
    tags: dict[str, str] | None = None


@dataclass
class RefreshTicketRequest:
    ota_config_id: int
    extra: dict


async def list_tickets(request: Request):
    client_code = (
        request.query_params.get("client_code") if request.query_params else None
    )
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        items = await tickets.list_tickets(db_conn, client_code=client_code)
        return OrjsonResponse(items)


async def create_ticket(request: Request):
    payload = await request.json()
    ticket_request = from_dict(TicketRequest, payload)

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_config = await get_ota_config_by_id(
            db_conn, ticket_request.travel.ota_config_id
        )

        async with db_conn.transaction():
            try:
                issuer = ticket_issuer(ota_config)
                new_ticket = issuer.create_ticket(
                    client_code=ticket_request.client_code,
                    travel=ticket_request.travel,
                    blocked_seat=ticket_request.blocked_seat,
                    pax=ticket_request.pax,
                    tags=ticket_request.tags,
                    price=ticket_request.price,
                )

                ticket_id = await tickets.insert_ticket(db_conn, new_ticket)
                return OrjsonResponse({"id": ticket_id}, status_code=201)
            except tickets.TicketAlreadyExists as exc:
                return OrjsonResponse({"message": str(exc)}, status_code=409)


async def get_ticket(request: Request):
    ticket_id = request.path_params["ticket_id"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)
        return OrjsonResponse({"ticket": asdict(ticket)}, status_code=200)


async def update_ticket(request: Request):
    ticket_id = request.path_params["ticket_id"]
    payload = await request.json()
    ticket_request = from_dict(UpdateTicketRequest, payload)

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)

        try:
            ticket.issued_ticket
            return OrjsonResponse(
                {"message": "Update not allowed. Ticket already issued"},
                status_code=400,
            )
        except ValueError:
            pass
        ota_config = await get_ota_config_by_id(db_conn, ticket.ota_config.id)

        old_ticket = asdict(ticket)
        await tickets.insert_ticket_status(
            db_conn,
            ticket.id,
            tickets.TicketStatus.UPDATING,
        )
        try:
            async with db_conn.transaction():
                issuer = ticket_issuer(ota_config)
                new_ticket = issuer.update_ticket(
                    ticket,
                    blocked_seat=ticket_request.blocked_seat,
                    pax=ticket_request.pax,
                    tags=ticket_request.tags,
                    price=ticket_request.price,
                )
                await tickets.update_ticket(db_conn, ticket_id, new_ticket)
                await tickets.insert_ticket_status(
                    db_conn,
                    ticket.id,
                    tickets.TicketStatus.UPDATED,
                    result={"old_ticket": old_ticket, "new_ticket": asdict(new_ticket)},
                )
        except Exception as err:
            await tickets.insert_ticket_status(
                db_conn,
                ticket.id,
                tickets.TicketStatus.UPDATE_FAILED,
                result={"error": str(err)},
            )
            raise
        return OrjsonResponse({"ticket": asdict(new_ticket)}, status_code=200)


async def confirm_ticket(request: Request):
    ticket_id = request.path_params["ticket_id"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)

        try:
            ota_config = await get_ota_config_by_id(db_conn, ticket.ota_config.id)
            issuer = ticket_issuer(ota_config)
            # TODO: put ticket into the processing queue (should I create a queued status?)
            await tickets.insert_ticket_status(
                db_conn, ticket.id, tickets.TicketStatus.PROCESSING
            )
            result = await issuer.issue_ticket(ticket)
            await tickets.insert_ticket_status(
                db_conn,
                ticket.id,
                tickets.TicketStatus.ISSUED,
                result=asdict(result),
            )
            return OrjsonResponse(
                {"id": ticket_id, "issued_ticket": asdict(result)}, status_code=200
            )
        except OTAIssuerInvalidParameters as error:
            await tickets.insert_ticket_status(
                db_conn, ticket.id, tickets.TicketStatus.FAILED, error.response
            )
            return OrjsonResponse(
                {"message": "Verifique os dados digitados."}, status_code=422
            )
        except OTAIssuerInvalidResponse as error:
            await tickets.insert_ticket_status(
                db_conn, ticket.id, tickets.TicketStatus.FAILED, error.response
            )
            return OrjsonResponse(
                {"message": "Erro ao converter retorno da OTA."}, status_code=500
            )
        except Exception as err:
            await tickets.insert_ticket_status(
                db_conn, ticket.id, tickets.TicketStatus.FAILED, {"error": str(err)}
            )
            raise


async def cancel_ticket(request: Request):
    ticket_id = request.path_params["ticket_id"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)

        if ticket.last_status_ignoring_refreshing.status not in (
            tickets.TicketStatus.ISSUED,
            tickets.TicketStatus.CANCEL_FAILED,
        ):
            raise tickets.TicketInvalidStatus(
                "Ticket status must be issued or cancel_failed"
            )

        ota_config = await get_ota_config_by_id(db_conn, ticket.ota_config.id)
        issuer = ticket_issuer(ota_config)
        await tickets.insert_ticket_status(
            db_conn, ticket.id, tickets.TicketStatus.CANCELING
        )

        try:
            result = await issuer.cancel_ticket(ticket)
        except TicketAlreadyCancelled as error:
            # TODO - Tem OTA que esse erro significa que já foi
            # cancelado unicamente, tem OTA que significa que
            # pode ter sido cancelado OU devolvido no guiche
            # antes de chamar o cancelamento pelo buserdjango
            # precisa dessa análise/diferenciação por OTA
            result = error.response
        except Exception as err:
            await tickets.insert_ticket_status(
                db_conn,
                ticket.id,
                tickets.TicketStatus.CANCEL_FAILED,
                result={"error": str(err)},
            )
            raise

        await tickets.insert_ticket_status(
            db_conn,
            ticket.id,
            tickets.TicketStatus.CANCELED,
            result=result,
        )

        return OrjsonResponse({"id": ticket_id}, status_code=200)


async def refresh_ticket(request: Request):
    # primeiro tenta atualizar usando o ticket_id
    try:
        return await refresh_ticket_from_ticket_id(request)
    except Exception as ex:
        logger.info(
            f"refresh_ticket_from_ticket_id.error.{str(ex)}",
            exc_info=ex,
            extra={"ticket_id": request.path_params["ticket_id"]},
        )
        pass

    # caso não tenha ticket existente, tenta atualizar a partir do payload
    payload = await request.json()
    ticket_request = from_dict(RefreshTicketRequest, payload)

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_config = await get_ota_config_by_id(db_conn, ticket_request.ota_config_id)
        issuer = ticket_issuer(ota_config)
        refreshed_ticket = await issuer.refresh_ticket_from_extra(ticket_request.extra)

    return OrjsonResponse({"ticket": asdict(refreshed_ticket)}, status_code=200)


async def refresh_ticket_from_ticket_id(request: Request):
    ticket_id = request.path_params["ticket_id"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)
        allowed_statuses = [
            tickets.TicketStatus.ISSUED,
            tickets.TicketStatus.CANCELED,
            tickets.TicketStatus.CANCEL_FAILED,
            tickets.TicketStatus.REFRESHED,
            tickets.TicketStatus.REFRESH_FAILED,
        ]
        if ticket.last_status.status not in allowed_statuses:
            raise tickets.TicketInvalidStatus(
                "Current ticket status is invalid for refreshing. "
                f" Current status: {ticket.status_history[0].status}. "
                f" Allowed statuses: {allowed_statuses}"
            )

        ota_config = await get_ota_config_by_id(db_conn, ticket.ota_config.id)
        issuer = ticket_issuer(ota_config)
        await tickets.insert_ticket_status(
            db_conn, ticket.id, tickets.TicketStatus.REFRESHING
        )

        try:
            refreshed_ticket = await issuer.refresh_ticket(ticket)
        except Exception as err:
            await tickets.insert_ticket_status(
                db_conn,
                ticket.id,
                tickets.TicketStatus.REFRESH_FAILED,
                result={"error": str(err)},
            )
            raise

        await tickets.insert_ticket_status(
            db_conn,
            ticket.id,
            tickets.TicketStatus.REFRESHED,
            result=asdict(refreshed_ticket),
        )
        return OrjsonResponse({"ticket": asdict(refreshed_ticket)}, status_code=200)
