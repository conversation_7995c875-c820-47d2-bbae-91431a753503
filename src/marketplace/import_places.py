# example_map from https://dbc-7fd7ccba-6f77.cloud.databricks.com/editor/notebooks/3407457770995985?o=2209260981226515#command/3407457770995986
# map = { "100_Eucatur_eulabs" : {"local_ota_id": 100, "buser_local_slug": "terminal-rodoviario-de-londrina-londrina-pr", "buser_local_nickname": "Terminal Rodoviário de Londrina", "buser_cidade_slug": "londrina-pr", "buser_cidade_name": "<PERSON><PERSON><PERSON>", "empresa_name": "Euca<PERSON>", "integracao": "eulabs"},  "1020_Eucatur_eulabs" : {"local_ota_id": 1020, "buser_local_slug": "terminal-rodoviario-tiete-sao-paulo-sp", "buser_local_nickname": "Terminal Rodoviário do Tietê", "buser_cidade_slug": "sao-paulo-sp", "buser_cidade_name": "São Paulo", "empresa_name": "Eucatur", "integracao": "eulabs"},  "103_Eucatur_eulabs" : {"local_ota_id": 103, "buser_local_slug": "rodoviaria-de-presidente-prudente-presidente-prudente-sp", "buser_local_nickname": "Rodoviária de Presidente Prudente", "buser_cidade_slug": "presidente-prudente-sp", "buser_cidade_name": "Presidente Prudente", "empresa_name": "Eucatur", "integracao": "eulabs"},  "107_Eucatur_eulabs" : {"local_ota_id": 107, "buser_local_slug": "rodoviaria-de-sao-gabriel-do-oeste-sao-gabriel-do-oeste-ms", "buser_local_nickname": "Rodoviária de São Gabriel do Oeste", "buser_cidade_slug": "sao-gabriel-do-oeste-ms", "buser_cidade_name": "São Gabriel do Oeste", "empresa_name": "Eucatur", "integracao": "eulabs"},  "1080_Eucatur_eulabs" : {"local_ota_id": 1080, "buser_local_slug": "rodoviaria-de-varzea-grande-terminal-alvorada-varzea-grande-mt", "buser_local_nickname": "Rodoviária de Várzea Grande (Terminal Alvorada)", "buser_cidade_slug": "varzea-grande-mt", "buser_cidade_name": "Várzea Grande", "empresa_name": "Eucatur", "integracao": "eulabs"},  "1083_Eucatur_eulabs" : {"local_ota_id": 1083, "buser_local_slug": "rodoviaria-de-campo-novo-do-parecis-campo-novo-do-parecis-mt", "buser_local_nickname": "Rodoviária de Campo Novo do Parecis", "buser_cidade_slug": "campo-novo-do-parecis-mt", "buser_cidade_name": "Campo Novo do Parecis", "empresa_name": "Eucatur", "integracao": "eulabs"},  "10_Eucatur_eulabs" : {"local_ota_id": 10, "buser_local_slug": "terminal-rodoviario-de-ariquemes-ariquemes-ro", "buser_local_nickname": "Terminal Rodoviário de Ariquemes", "buser_cidade_slug": "ariquemes-ro", "buser_cidade_name": "Ariquemes", "empresa_name": "Eucatur", "integracao": "eulabs"},  "112_Eucatur_eulabs" : {"local_ota_id": 112, "buser_local_slug": "terminal-rodoviario-de-coxim-coxim-ms", "buser_local_nickname": "Terminal Rodoviário de Coxim", "buser_cidade_slug": "coxim-ms", "buser_cidade_name": "Coxim", "empresa_name": "Eucatur", "integracao": "eulabs"},  "113_Eucatur_eulabs" : {"local_ota_id": 113, "buser_local_slug": "terminal-rodoviario-de-sonora-sonora-ms", "buser_local_nickname": "Terminal Rodoviário de Sonora", "buser_cidade_slug": "sonora-ms", "buser_cidade_name": "Sonora", "empresa_name": "Eucatur", "integracao": "eulabs"},  "114_Eucatur_eulabs" : {"local_ota_id": 114, "buser_local_slug": "terminal-rodoviario-de-rondonopolis-rondonopolis-mt", "buser_local_nickname": "Terminal Rodoviário de Rondonópolis", "buser_cidade_slug": "rondonopolis-mt", "buser_cidade_name": "Rondonópolis", "empresa_name": "Eucatur", "integracao": "eulabs"},  "116_Eucatur_eulabs" : {"local_ota_id": 116, "buser_local_slug": "terminal-rodoviario-de-jaciara-jaciara-mt", "buser_local_nickname": "Terminal Rodoviário de Jaciara", "buser_cidade_slug": "jaciara-mt", "buser_cidade_name": "Jaciara", "empresa_name": "Eucatur", "integracao": "eulabs"},  "118_Eucatur_eulabs" : {"local_ota_id": 118, "buser_local_slug": "terminal-rodoviario-cuiaba-cuiaba-mt", "buser_local_nickname": "Terminal Rodoviário Cuiabá", "buser_cidade_slug": "cuiaba-mt", "buser_cidade_name": "Cuiabá", "empresa_name": "Eucatur", "integracao": "eulabs"},  "1213_Eucatur_eulabs" : {"local_ota_id": 1213, "buser_local_slug": "rodoviaria-de-extrema-porto-velho-ro", "buser_local_nickname": "Rodoviária de Extrema ", "buser_cidade_slug": "extrema-porto-velho-ro", "buser_cidade_name": "Extrema (Porto Velho)", "empresa_name": "Eucatur", "integracao": "eulabs"},  "121_Eucatur_eulabs" : {"local_ota_id": 121, "buser_local_slug": "terminal-rodoviario-de-pontes-e-lacerda-pontes-e-lacerda-mt", "buser_local_nickname": "Terminal Rodoviário de Pontes e Lacerda", "buser_cidade_slug": "pontes-e-lacerda-mt", "buser_cidade_name": "Pontes e Lacerda", "empresa_name": "Eucatur", "integracao": "eulabs"},  "12_Eucatur_eulabs" : {"local_ota_id": 12, "buser_local_slug": "rodoviaria-de-ibatiba-ibatiba-es", "buser_local_nickname": "Rodoviária de Ibatiba", "buser_cidade_slug": "ibatiba-es", "buser_cidade_name": "Ibatiba", "empresa_name": "Eucatur", "integracao": "eulabs"},  "1351_Eucatur_eulabs" : {"local_ota_id": 1351, "buser_local_slug": "terminal-rodoviario-de-campinas-campinas-sp", "buser_local_nickname": "Terminal Rodoviário de Campinas", "buser_cidade_slug": "campinas-sp", "buser_cidade_name": "Campinas", "empresa_name": "Eucatur", "integracao": "eulabs"},  "14_Eucatur_eulabs" : {"local_ota_id": 14, "buser_local_slug": "terminal-rodoviario-de-jaru-jaru-ro", "buser_local_nickname": "Terminal Rodoviário de Jaru", "buser_cidade_slug": "jaru-ro", "buser_cidade_name": "Jaru", "empresa_name": "Eucatur", "integracao": "eulabs"},  "15_Eucatur_eulabs" : {"local_ota_id": 15, "buser_local_slug": "rodoviaria-de-ouro-preto-do-oeste-ouro-preto-do-oeste-ro", "buser_local_nickname": "Rodoviária de Ouro Preto do Oeste", "buser_cidade_slug": "ouro-preto-do-oeste-ro", "buser_cidade_name": "Ouro Preto do Oeste", "empresa_name": "Eucatur", "integracao": "eulabs"}}

import asyncio
from dataclasses import asdict

from marketplace import database
from marketplace.models import OTAConfig, OTAPlace, Place
from src.marketplace import list_ota_configs
from src.marketplace.__main__ import _import_places
from src.marketplace.places import list_all_places, list_ota_places


async def _run(map: dict):
    places = {
        Place(name=value[key], slug=value[slug_key])
        for value in map.values()
        for key, slug_key in [
            ("buser_local_nickname", "buser_local_slug"),
            ("buser_cidade_name", "buser_cidade_slug"),
        ]
    }
    await _save_new_places(places)

    map_configs = await _get_configs_map()
    current_ota_places = await _list_ota_places(list(map_configs.values()))

    result = []
    for ota_place in current_ota_places:
        config = map_configs[ota_place.ota_config_id]
        key = f"{ota_place.ota_place_id}_{config.name}_{config.provider}"
        slug = map.get(key) and map[key]["buser_local_slug"]
        cidade_slug = map.get(key) and map[key]["buser_cidade_slug"]

        if not slug or not cidade_slug:
            print(f"Not found local for {key}")
            continue

        result.append({**asdict(ota_place), "place": slug or cidade_slug})

    print(f"Found: {result}")
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            await database.queries.insert_ota_config_places(db_conn, result)


async def _save_new_places(places: set[Place]):
    await _import_places(list(places))


async def _get_places_map() -> dict[str, Place]:
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            all_places = await list_all_places(db_conn)
    return {place.slug: place for place in all_places}


async def _get_configs_map() -> dict[int, OTAConfig]:
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            all_configs = await list_ota_configs(db_conn)
    return {config.id: config for config in all_configs}


async def _list_ota_places(ota_configs: list[OTAConfig]) -> set[OTAPlace]:
    current_ota_places = set()
    async with database.db_pool as db_pool:
        async with db_pool.acquire() as db_conn:
            for ota_config in ota_configs:
                current_ota_places |= set(
                    await list_ota_places(db_conn, config_id=ota_config.id)
                )
    return current_ota_places


async def run_async(map: dict):
    await _run(map)


def run_sync(map: dict):
    try:
        asyncio.run(run_async(map))
    except Exception as e:
        print(f"An error occurred: {e}")
