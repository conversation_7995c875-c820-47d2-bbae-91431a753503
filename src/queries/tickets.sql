-- name: list_tickets
select
  tickets.id,
  tickets.client_code,
  tickets.tags,
  tickets.created_at,
  tickets.valid_until,
  tickets.valid_until < current_timestamp as expired,
  tickets.pax_name,
  tickets.pax_doc,
  tickets.pax_doc_type,
  tickets.pax_cpf,
  tickets.pax_phone,
  tickets.pax_birthdate,
  tickets.pax_benefit,
  tickets.code,
  tickets.company_id,
  tickets.departure_at,
  tickets.arrival_at,
  tickets.seat_type,
  tickets.travel_extra,
  tickets.seat_number,
  tickets.seat_floor,
  tickets.seat_row,
  tickets.seat_column,
  tickets.seat_extra,
  tickets.seat_block_key,
  tickets.price,
  to_jsonb(ota_config_companies) as company,
  to_jsonb(ota_configs) as ota_config,
  to_jsonb(places_origin) as origin,
  to_jsonb(places_destination) as destination,
  (select array_agg(to_jsonb(v)) from (
      select ticket_status_history as v
      from ticket_status_history
      where tickets.id = ticket_status_history.ticket_id
      order by ticket_status_history.created_at desc
  ) as s) as status_history
from tickets
join ota_configs on tickets.ota_config_id = ota_configs.id
join ota_config_companies on tickets.ota_config_id = ota_config_companies.ota_config_id and tickets.company_id = ota_config_companies.external_id
join places places_origin on tickets.origin = places_origin.slug
join places places_destination on tickets.destination = places_destination.slug
where tickets.client_code = CAST(:client_code AS text) or :client_code is null
order by tickets.created_at desc;

-- name: get_ticket_by_id^
select
  tickets.id,
  tickets.client_code,
  tickets.tags,
  tickets.created_at,
  tickets.valid_until,
  tickets.valid_until < current_timestamp as expired,
  tickets.pax_name,
  tickets.pax_doc_type,
  tickets.pax_doc,
  tickets.pax_cpf,
  tickets.pax_phone,
  tickets.pax_birthdate,
  tickets.pax_benefit,
  tickets.code,
  tickets.company_id,
  tickets.departure_at,
  tickets.arrival_at,
  tickets.seat_type,
  tickets.travel_extra,
  tickets.seat_number,
  tickets.seat_floor,
  tickets.seat_row,
  tickets.seat_column,
  tickets.seat_extra,
  tickets.seat_block_key,
  tickets.price,
  to_jsonb(ota_config_companies) as company,
  to_jsonb(ota_configs) as ota_config,
  to_jsonb(places_origin) as origin,
  to_jsonb(places_destination) as destination,
  (select array_agg(to_jsonb(v)) from (
      select ticket_status_history as v
      from ticket_status_history
      where tickets.id = ticket_status_history.ticket_id
      order by ticket_status_history.created_at desc
  ) as s) as status_history
from tickets
join ota_configs on tickets.ota_config_id = ota_configs.id
join ota_config_companies on tickets.ota_config_id = ota_config_companies.ota_config_id and tickets.company_id = ota_config_companies.external_id
join places places_origin on tickets.origin = places_origin.slug
join places places_destination on tickets.destination = places_destination.slug
where tickets.id = :ticket_id;

-- name: insert_ticket<!
insert into tickets (
    client_code,
    tags,
    valid_until,
    pax_name,
    pax_doc_type,
    pax_doc,
    pax_cpf,
    pax_phone,
    pax_birthdate,
    pax_benefit,
    ota_config_id,
    code,
    company_id,
    origin,
    destination,
    departure_at,
    arrival_at,
    seat_type,
    travel_extra,
    seat_number,
    seat_floor,
    seat_row,
    seat_column,
    seat_extra,
    seat_block_key,
    price
) values (
    :client_code,
    :tags,
    :valid_until,
    :pax_name,
    :pax_doc_type,
    :pax_doc,
    :pax_cpf,
    :pax_phone,
    :pax_birthdate,
    :pax_benefit,
    :ota_config_id,
    :code,
    :company_id,
    :origin,
    :destination,
    :departure_at,
    :arrival_at,
    :seat_type,
    :travel_extra,
    :seat_number,
    :seat_floor,
    :seat_row,
    :seat_column,
    :seat_extra,
    :seat_block_key,
    :price
) returning id;

-- name: update_ticket<!
update tickets
set
    tags           = :tags,
    valid_until    = :valid_until,

    pax_name       = :pax_name,
    pax_doc_type   = :pax_doc_type,
    pax_doc        = :pax_doc,
    pax_cpf        = :pax_cpf,
    pax_phone      = :pax_phone,
    pax_birthdate  = :pax_birthdate,
    pax_benefit    = :pax_benefit,

    seat_number    = :seat_number,
    seat_floor     = :seat_floor,
    seat_row       = :seat_row,
    seat_column    = :seat_column,
    seat_extra     = :seat_extra,
    seat_block_key = :seat_block_key,

    price          = :price
where id = :id
returning *;

-- name: insert_ticket_status<!
insert into ticket_status_history (
    ticket_id,
    created_at,
    status,
    result
) values (
    :ticket_id,
    current_timestamp,
    :status,
    :result
) returning *;
