-- name: insert_ota_benefits*!
insert into ota_config_benefits (
    ota_config_id,
    description,
    external_code,
    status,
    type
) values (
    :ota_config_id,
    :description,
    :external_code,
    :status,
    :type
)
on conflict (ota_config_id, external_code) do nothing
returning *;

-- name: update_ota_benefit<!
update ota_config_benefits
set
    status = :status,
    type = :type
where ota_config_id = :ota_config_id and external_code = :external_code
returning *;
