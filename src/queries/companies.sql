-- name: insert_ota_company<!
insert into ota_config_companies (external_id, ota_config_id, name, cnpj)
values (:external_id, :ota_config_id, :name, :cnpj)
on conflict (ota_config_id, external_id) do nothing
returning *;

-- name: update_ota_company<!
update ota_config_companies
set
  name = :name,
  cnpj = :cnpj
where ota_config_id = :ota_config_id and external_id = :external_id
returning *;

-- name: update_ota_company_cnpj*!
update ota_config_companies
set
  cnpj = :cnpj
where ota_config_id = :ota_config_id and (external_id = :external_id or :external_id is null)
returning *;

-- name: list_all_ota_companies
select companies.* from ota_config_companies companies
inner join ota_configs configs on configs.id = companies.ota_config_id
where configs.status = :status or :status is null