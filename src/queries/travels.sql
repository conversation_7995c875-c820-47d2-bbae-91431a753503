-- name: get_travel^
select * from travels where code = :travel_code;

-- name: insert_travel*!
insert into travels (
    ota,
    ota_config_id,
    code,
    itinerary,
    origin,
    departure_at,
    destination,
    arrival_at,
    seat_type,
    available_seats,
    total_seats,
    extra,
    updated_at
) values (
    :ota,
    :ota_config_id,
    :code,
    :itinerary,
    :origin,
    :departure_at,
    :destination,
    :arrival_at,
    :seat_type,
    :available_seats,
    :total_seats,
    :extra,
    current_timestamp
) on conflict (ota, code) do update set
    ota = excluded.ota,
    itinerary = excluded.itinerary,
    origin = excluded.origin,
    departure_at = excluded.departure_at,
    destination = excluded.destination,
    arrival_at = excluded.arrival_at,
    seat_type = excluded.seat_type,
    available_seats = excluded.available_seats,
    total_seats = excluded.total_seats,
    extra = excluded.extra,
    updated_at = current_timestamp;


-- name: search_travels
select
  code,
  ota,
  origin,
  destination,
  departure_at,
  itinerary,
  arrival_at,
  seat_type,
  available_seats,
  total_seats,
  created_at
from travels
where origin = :origin
  and destination = :destination
  and departure_at >= :start_date
  and departure_at <= :end_date;
