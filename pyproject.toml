[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "marketplace"
dynamic = ["version"]
description = ''
readme = "README.md"
requires-python = ">=3.13"
license = "LicenseRef-Proprietary"
keywords = []
authors = [{ name = "<PERSON><PERSON>", email = "<EMAIL>" }]
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: Implementation :: CPython",
]
dependencies = [
    "click",
    "httpx",
    "tenacity",
    "asynciolimiter",
    "aio-pika",
    "asyncpg==0.30.0",
    "aiosql==13.3",
    "orjson",
    "starlette",
    "dacite",
    "yapcache==0.1.15",
    "cachebox",
    "python-slugify[unidecode]",
    "rapidfuzz",
    "numpy",
    "jarowinkler",
    "pyjwt",
    "sentry-sdk[starlette]",
    "opentelemetry-api",
    "opentelemetry-instrumentation-starlette",
    "ruff",
    "pyright==1.1.393",
    "coverage[toml]>=6.5",
    "pytest",
    "pytest-env",
    "pytest-cov",
    "pytest-asyncio",
    "pytest-httpx",
    "python-json-logger>=2.0.7",
    "opencv-python-headless==*********",
    "slowapi"
]

[project.urls]
Documentation = "https://buser.gitlab.com/buser/marketplace"
Issues = "https://buser.gitlab.com/buser/marketplace/-/issues"
Source = "https://buser.gitlab.com/buser/marketplace/-/tree"

[tool.hatch.version]
path = "src/marketplace/__about__.py"

[tool.hatch.envs.default]
dependencies = ["uvicorn[standard]"]

[tool.hatch.envs.default.scripts]
server = "uvicorn --reload --host 0.0.0.0 marketplace.asgi:app --port 8000"
test = "pytest {args:tests}"
test-cov = "coverage run -m pytest {args:tests}"
format = ["ruff check --fix {args:.}", "ruff format {args:.}"]
lint = ["ruff check {args:.}", "ruff format --check {args:.}", "type"]
type = "pyright {args:.}"
cov-report = ["- coverage combine", "coverage report"]
cov = ["test-cov", "cov-report"]
refresh_places = "python -m marketplace refresh-places"
upsert_otas = "python -m marketplace upsert-otas"

[[tool.hatch.envs.all.matrix]]
python = ["3.13"]

[tool.hatch.envs.types]
dependencies = ["mypy>=1.0.0"]

[tool.ruff.lint]
extend-select = ["I"]

[tool.pytest.ini_options]
asyncio_mode = "auto"
addopts = ["--import-mode=importlib"]
env = ["TESTING=true"]

[tool.coverage.run]
source_pkgs = ["marketplace", "tests"]
branch = true
parallel = true
omit = ["src/marketplace/__about__.py"]

[tool.coverage.paths]
marketplace = ["src/marketplace", "*/marketplace/src/marketplace"]
tests = ["tests", "*/marketplace/tests"]

[tool.coverage.report]
exclude_lines = ["no cov", "if __name__ == .__main__.:", "if TYPE_CHECKING:"]

[tool.hatch.metadata]
allow-direct-references = true
