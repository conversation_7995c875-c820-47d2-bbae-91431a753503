create extension ltree;

alter table places alter slug type ltree using slug::ltree;

create index idx_places_slug_gist on places using gist (slug);

alter table ota_config_places add column place ltree;

alter table ota_config_places
  add constraint ota_config_places_place_slug_fkey foreign key (place)
  references places (slug) on delete set null on update cascade;

create index idx_ota_config_places_place_gist on ota_config_places using gist (place);

alter table ota_config_places drop column places;
