create type benefit_type as enum (
    'elderly_100',
    'elderly_50',
    'young_100',
    'young_50',
    'kid',
    'disabled',
    'woman_space',
    'pet_space',
    'normal'
);

create type ota_config_benefit_status as enum ('available', 'ignored');

create table ota_config_benefits (
    ota_config_id int not null references ota_configs (id) on delete cascade on update cascade,
    description text not null,
    status ota_config_benefit_status not null default 'available',
    external_code text not null,
    type benefit_type,
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone,
    primary key (ota_config_id, external_code)
);