create type ota_config_place_status as enum ('system', 'user', 'ignored');

create table ota_config_places (
  ota_config_id int not null references ota_configs (id) on delete cascade on update cascade,
  name text not null,
  status ota_config_place_status not null default 'system',
  places int[] default array[]::int[], -- "soft"-reference to places.id
  extra jsonb,
  created_at timestamp with time zone default current_timestamp,
  updated_at timestamp with time zone,
  primary key (ota_config_id, name)
);

create index ota_config_places_places_idx on ota_config_places using gin (places);

-- recreate places table
drop table places;

create type place_status as enum ('active', 'inactive');

create table places (
    id int generated by default as identity,
    slug text not null,
    name text not null,
    status place_status not null default 'active',
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone,
    primary key (id)
);

create unique index idx_places_slug_uq on places (slug);
create index places_name_trgm on places using gist (name gist_trgm_ops);
