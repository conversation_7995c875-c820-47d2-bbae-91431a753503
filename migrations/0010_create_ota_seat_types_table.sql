create type seat_type as enum (
    'cama premium',
    'cama premium individual',
    'leito',
    'semi leito individual',
    'convencional',
    'executivo individual',
    'leito cama',
    'leito cama individual',
    'leito individual',
    'semi leito',
    'executivo'
);

create type ota_config_seat_type_status as enum ('available', 'not-listed');

create table ota_config_seat_types (
    ota_config_id int not null references ota_configs (id) on delete cascade on update cascade,
    name text not null,
    status ota_config_seat_type_status not null default 'available',
    seat_type seat_type,
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone,
    primary key (ota_config_id, name)
);
