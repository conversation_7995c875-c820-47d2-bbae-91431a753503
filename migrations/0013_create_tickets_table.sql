create extension hstore;

create table tickets (
    id bigint primary key generated by default as identity,
    client_code text not null,
    tags hstore,
    created_at timestamp with time zone default current_timestamp,
    valid_until timestamp with time zone, 

    -- pax
    pax_name text not null,
    pax_doc text,
    pax_phone text,
    pax_birthdate date,

    -- travel
    ota_config_id int not null references ota_configs (id),
    code text not null,
    company_id int not null,
    origin ltree not null references places (slug),
    destination ltree not null references places (slug),
    departure_at timestamp with time zone not null,
    arrival_at timestamp with time zone not null,
    seat_type text not null,
    travel_extra jsonb,

    -- seat
    seat_number text not null,
    seat_floor int,
    seat_row int,
    seat_column int,
    seat_extra jsonb,
    seat_block_key text,
    price decimal(10, 2),

    foreign key (ota_config_id, company_id) references ota_config_companies (ota_config_id, external_id)
);

create index idx_tickets_created_at on tickets (created_at);

create unique index idx_tickets_client_code_uq on tickets (client_code);

create index idx_tickets_tags on tickets using gin (tags);

create type ticket_status  as enum ('created', 'expired', 'processing', 'issued', 'failed', 'canceling', 'canceled');

create table ticket_status_history (
    id bigint primary key generated by default as identity,
    ticket_id bigint references tickets (id),
    created_at timestamp with time zone default current_timestamp,
    status ticket_status
);

create index  idx_ticket_status_history_ticket_id on ticket_status_history (ticket_id);
